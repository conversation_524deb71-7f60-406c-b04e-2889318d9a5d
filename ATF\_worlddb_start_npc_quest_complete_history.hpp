// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _worlddb_start_npc_quest_complete_history
    {
        struct __list
        {
            char szQuestCode[64];
            char byLevel;
            __int64 nEndTime;
        public:
            __list();
            void ctor___list();
        };
        unsigned int dwListCnt;
        __list *List;
    };
END_ATF_NAMESPACE
