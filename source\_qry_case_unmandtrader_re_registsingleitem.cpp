#include <_qry_case_unmandtrader_re_registsingleitem.hpp>


START_ATF_NAMESPACE
    _qry_case_unmandtrader_re_registsingleitem::_qry_case_unmandtrader_re_registsingleitem()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_unmandtrader_re_registsingleitem*);
        (org_ptr(0x14035f7d0L))(this);
    };
    void _qry_case_unmandtrader_re_registsingleitem::ctor__qry_case_unmandtrader_re_registsingleitem()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_unmandtrader_re_registsingleitem*);
        (org_ptr(0x14035f7d0L))(this);
    };
    int _qry_case_unmandtrader_re_registsingleitem::size()
    {
        using org_ptr = int (WINAPIV*)(struct _qry_case_unmandtrader_re_registsingleitem*);
        return (org_ptr(0x14035f7f0L))(this);
    };
    
END_ATF_NAMESPACE
