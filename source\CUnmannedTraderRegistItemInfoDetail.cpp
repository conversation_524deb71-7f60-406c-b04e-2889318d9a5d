#include <CUnmannedTraderRegistItemInfoDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace Detail
    {
        
        Info::CUnmannedTraderRegistItemInfoctor_CUnmannedTraderRegistItemInfo2_ptr CUnmannedTraderRegistItemInfoctor_CUnmannedTraderRegistItemInfo2_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoctor_CUnmannedTraderRegistItemInfo2_clbk CUnmannedTraderRegistItemInfoctor_CUnmannedTraderRegistItemInfo2_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoClear4_ptr CUnmannedTraderRegistItemInfoClear4_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoClear4_clbk CUnmannedTraderRegistItemInfoClear4_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoClearBuyerInfo6_ptr CUnmannedTraderRegistItemInfoClearBuyerInfo6_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoClearBuyerInfo6_clbk CUnmannedTraderRegistItemInfoClearBuyerInfo6_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoClearRegist8_ptr CUnmannedTraderRegistItemInfoClearRegist8_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoClearRegist8_clbk CUnmannedTraderRegistItemInfoClearRegist8_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoClearToWaitState10_ptr CUnmannedTraderRegistItemInfoClearToWaitState10_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoClearToWaitState10_clbk CUnmannedTraderRegistItemInfoClearToWaitState10_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoGetBuyerSerial12_ptr CUnmannedTraderRegistItemInfoGetBuyerSerial12_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoGetBuyerSerial12_clbk CUnmannedTraderRegistItemInfoGetBuyerSerial12_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoGetD14_ptr CUnmannedTraderRegistItemInfoGetD14_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoGetD14_clbk CUnmannedTraderRegistItemInfoGetD14_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoGetETSerial16_ptr CUnmannedTraderRegistItemInfoGetETSerial16_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoGetETSerial16_clbk CUnmannedTraderRegistItemInfoGetETSerial16_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoGetItemIndex18_ptr CUnmannedTraderRegistItemInfoGetItemIndex18_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoGetItemIndex18_clbk CUnmannedTraderRegistItemInfoGetItemIndex18_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoGetItemSerial20_ptr CUnmannedTraderRegistItemInfoGetItemSerial20_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoGetItemSerial20_clbk CUnmannedTraderRegistItemInfoGetItemSerial20_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoGetLeftSec22_ptr CUnmannedTraderRegistItemInfoGetLeftSec22_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoGetLeftSec22_clbk CUnmannedTraderRegistItemInfoGetLeftSec22_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoGetPrice24_ptr CUnmannedTraderRegistItemInfoGetPrice24_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoGetPrice24_clbk CUnmannedTraderRegistItemInfoGetPrice24_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoGetRegistSerial26_ptr CUnmannedTraderRegistItemInfoGetRegistSerial26_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoGetRegistSerial26_clbk CUnmannedTraderRegistItemInfoGetRegistSerial26_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoGetResultTime28_ptr CUnmannedTraderRegistItemInfoGetResultTime28_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoGetResultTime28_clbk CUnmannedTraderRegistItemInfoGetResultTime28_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoGetSellTurm30_ptr CUnmannedTraderRegistItemInfoGetSellTurm30_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoGetSellTurm30_clbk CUnmannedTraderRegistItemInfoGetSellTurm30_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoGetStartTime32_ptr CUnmannedTraderRegistItemInfoGetStartTime32_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoGetStartTime32_clbk CUnmannedTraderRegistItemInfoGetStartTime32_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoGetStartTimePtr34_ptr CUnmannedTraderRegistItemInfoGetStartTimePtr34_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoGetStartTimePtr34_clbk CUnmannedTraderRegistItemInfoGetStartTimePtr34_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoGetState36_ptr CUnmannedTraderRegistItemInfoGetState36_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoGetState36_clbk CUnmannedTraderRegistItemInfoGetState36_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoGetStorageIndex38_ptr CUnmannedTraderRegistItemInfoGetStorageIndex38_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoGetStorageIndex38_clbk CUnmannedTraderRegistItemInfoGetStorageIndex38_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoGetTableCode40_ptr CUnmannedTraderRegistItemInfoGetTableCode40_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoGetTableCode40_clbk CUnmannedTraderRegistItemInfoGetTableCode40_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoGetTax42_ptr CUnmannedTraderRegistItemInfoGetTax42_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoGetTax42_clbk CUnmannedTraderRegistItemInfoGetTax42_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoGetU44_ptr CUnmannedTraderRegistItemInfoGetU44_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoGetU44_clbk CUnmannedTraderRegistItemInfoGetU44_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoIsEmpty46_ptr CUnmannedTraderRegistItemInfoIsEmpty46_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoIsEmpty46_clbk CUnmannedTraderRegistItemInfoIsEmpty46_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoIsOverRegistTime48_ptr CUnmannedTraderRegistItemInfoIsOverRegistTime48_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoIsOverRegistTime48_clbk CUnmannedTraderRegistItemInfoIsOverRegistTime48_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoIsRegist50_ptr CUnmannedTraderRegistItemInfoIsRegist50_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoIsRegist50_clbk CUnmannedTraderRegistItemInfoIsRegist50_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoIsSellUpdateWait52_ptr CUnmannedTraderRegistItemInfoIsSellUpdateWait52_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoIsSellUpdateWait52_clbk CUnmannedTraderRegistItemInfoIsSellUpdateWait52_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoIsSellWait54_ptr CUnmannedTraderRegistItemInfoIsSellWait54_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoIsSellWait54_clbk CUnmannedTraderRegistItemInfoIsSellWait54_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoIsWaitNoitfyClose56_ptr CUnmannedTraderRegistItemInfoIsWaitNoitfyClose56_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoIsWaitNoitfyClose56_clbk CUnmannedTraderRegistItemInfoIsWaitNoitfyClose56_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoReRegistItem58_ptr CUnmannedTraderRegistItemInfoReRegistItem58_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoReRegistItem58_clbk CUnmannedTraderRegistItemInfoReRegistItem58_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoRegistItem60_ptr CUnmannedTraderRegistItemInfoRegistItem60_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoRegistItem60_clbk CUnmannedTraderRegistItemInfoRegistItem60_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoRepriceItem62_ptr CUnmannedTraderRegistItemInfoRepriceItem62_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoRepriceItem62_clbk CUnmannedTraderRegistItemInfoRepriceItem62_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoSellComplete64_ptr CUnmannedTraderRegistItemInfoSellComplete64_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoSellComplete64_clbk CUnmannedTraderRegistItemInfoSellComplete64_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoSellWaitItem66_ptr CUnmannedTraderRegistItemInfoSellWaitItem66_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoSellWaitItem66_clbk CUnmannedTraderRegistItemInfoSellWaitItem66_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoSet68_ptr CUnmannedTraderRegistItemInfoSet68_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoSet68_clbk CUnmannedTraderRegistItemInfoSet68_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoSetOverRegistTime70_ptr CUnmannedTraderRegistItemInfoSetOverRegistTime70_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoSetOverRegistTime70_clbk CUnmannedTraderRegistItemInfoSetOverRegistTime70_user(nullptr);
        
        Info::CUnmannedTraderRegistItemInfoSetState72_ptr CUnmannedTraderRegistItemInfoSetState72_next(nullptr);
        Info::CUnmannedTraderRegistItemInfoSetState72_clbk CUnmannedTraderRegistItemInfoSetState72_user(nullptr);
        
        
        Info::CUnmannedTraderRegistItemInfodtor_CUnmannedTraderRegistItemInfo78_ptr CUnmannedTraderRegistItemInfodtor_CUnmannedTraderRegistItemInfo78_next(nullptr);
        Info::CUnmannedTraderRegistItemInfodtor_CUnmannedTraderRegistItemInfo78_clbk CUnmannedTraderRegistItemInfodtor_CUnmannedTraderRegistItemInfo78_user(nullptr);
        
        
        void CUnmannedTraderRegistItemInfoctor_CUnmannedTraderRegistItemInfo2_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           CUnmannedTraderRegistItemInfoctor_CUnmannedTraderRegistItemInfo2_user(_this, CUnmannedTraderRegistItemInfoctor_CUnmannedTraderRegistItemInfo2_next);
        };
        void CUnmannedTraderRegistItemInfoClear4_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           CUnmannedTraderRegistItemInfoClear4_user(_this, CUnmannedTraderRegistItemInfoClear4_next);
        };
        void CUnmannedTraderRegistItemInfoClearBuyerInfo6_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           CUnmannedTraderRegistItemInfoClearBuyerInfo6_user(_this, CUnmannedTraderRegistItemInfoClearBuyerInfo6_next);
        };
        void CUnmannedTraderRegistItemInfoClearRegist8_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           CUnmannedTraderRegistItemInfoClearRegist8_user(_this, CUnmannedTraderRegistItemInfoClearRegist8_next);
        };
        void CUnmannedTraderRegistItemInfoClearToWaitState10_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           CUnmannedTraderRegistItemInfoClearToWaitState10_user(_this, CUnmannedTraderRegistItemInfoClearToWaitState10_next);
        };
        unsigned int CUnmannedTraderRegistItemInfoGetBuyerSerial12_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoGetBuyerSerial12_user(_this, CUnmannedTraderRegistItemInfoGetBuyerSerial12_next);
        };
        uint64_t CUnmannedTraderRegistItemInfoGetD14_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoGetD14_user(_this, CUnmannedTraderRegistItemInfoGetD14_next);
        };
        unsigned int CUnmannedTraderRegistItemInfoGetETSerial16_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoGetETSerial16_user(_this, CUnmannedTraderRegistItemInfoGetETSerial16_next);
        };
        uint16_t CUnmannedTraderRegistItemInfoGetItemIndex18_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoGetItemIndex18_user(_this, CUnmannedTraderRegistItemInfoGetItemIndex18_next);
        };
        uint16_t CUnmannedTraderRegistItemInfoGetItemSerial20_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoGetItemSerial20_user(_this, CUnmannedTraderRegistItemInfoGetItemSerial20_next);
        };
        unsigned int CUnmannedTraderRegistItemInfoGetLeftSec22_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoGetLeftSec22_user(_this, CUnmannedTraderRegistItemInfoGetLeftSec22_next);
        };
        unsigned int CUnmannedTraderRegistItemInfoGetPrice24_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoGetPrice24_user(_this, CUnmannedTraderRegistItemInfoGetPrice24_next);
        };
        unsigned int CUnmannedTraderRegistItemInfoGetRegistSerial26_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoGetRegistSerial26_user(_this, CUnmannedTraderRegistItemInfoGetRegistSerial26_next);
        };
        int64_t CUnmannedTraderRegistItemInfoGetResultTime28_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoGetResultTime28_user(_this, CUnmannedTraderRegistItemInfoGetResultTime28_next);
        };
        char CUnmannedTraderRegistItemInfoGetSellTurm30_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoGetSellTurm30_user(_this, CUnmannedTraderRegistItemInfoGetSellTurm30_next);
        };
        int64_t CUnmannedTraderRegistItemInfoGetStartTime32_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoGetStartTime32_user(_this, CUnmannedTraderRegistItemInfoGetStartTime32_next);
        };
        int64_t* CUnmannedTraderRegistItemInfoGetStartTimePtr34_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoGetStartTimePtr34_user(_this, CUnmannedTraderRegistItemInfoGetStartTimePtr34_next);
        };
        CUnmannedTraderItemState::STATE CUnmannedTraderRegistItemInfoGetState36_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoGetState36_user(_this, CUnmannedTraderRegistItemInfoGetState36_next);
        };
        char CUnmannedTraderRegistItemInfoGetStorageIndex38_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoGetStorageIndex38_user(_this, CUnmannedTraderRegistItemInfoGetStorageIndex38_next);
        };
        char CUnmannedTraderRegistItemInfoGetTableCode40_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoGetTableCode40_user(_this, CUnmannedTraderRegistItemInfoGetTableCode40_next);
        };
        unsigned int CUnmannedTraderRegistItemInfoGetTax42_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoGetTax42_user(_this, CUnmannedTraderRegistItemInfoGetTax42_next);
        };
        unsigned int CUnmannedTraderRegistItemInfoGetU44_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoGetU44_user(_this, CUnmannedTraderRegistItemInfoGetU44_next);
        };
        bool CUnmannedTraderRegistItemInfoIsEmpty46_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoIsEmpty46_user(_this, CUnmannedTraderRegistItemInfoIsEmpty46_next);
        };
        bool CUnmannedTraderRegistItemInfoIsOverRegistTime48_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoIsOverRegistTime48_user(_this, CUnmannedTraderRegistItemInfoIsOverRegistTime48_next);
        };
        bool CUnmannedTraderRegistItemInfoIsRegist50_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoIsRegist50_user(_this, CUnmannedTraderRegistItemInfoIsRegist50_next);
        };
        bool CUnmannedTraderRegistItemInfoIsSellUpdateWait52_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoIsSellUpdateWait52_user(_this, CUnmannedTraderRegistItemInfoIsSellUpdateWait52_next);
        };
        bool CUnmannedTraderRegistItemInfoIsSellWait54_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoIsSellWait54_user(_this, CUnmannedTraderRegistItemInfoIsSellWait54_next);
        };
        bool CUnmannedTraderRegistItemInfoIsWaitNoitfyClose56_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           return CUnmannedTraderRegistItemInfoIsWaitNoitfyClose56_user(_this, CUnmannedTraderRegistItemInfoIsWaitNoitfyClose56_next);
        };
        void CUnmannedTraderRegistItemInfoReRegistItem58_wrapper(struct CUnmannedTraderRegistItemInfo* _this, unsigned int dwPrice)
        {
           CUnmannedTraderRegistItemInfoReRegistItem58_user(_this, dwPrice, CUnmannedTraderRegistItemInfoReRegistItem58_next);
        };
        void CUnmannedTraderRegistItemInfoRegistItem60_wrapper(struct CUnmannedTraderRegistItemInfo* _this, unsigned int dwRegistSerial, uint16_t wItemSerial, unsigned int dwETSerialNumber, unsigned int dwPrice, char bySellTurm, char byTableCode, uint16_t wItemIndex, char byStorageIndex, uint64_t dwD, unsigned int dwU, bool bInserted)
        {
           CUnmannedTraderRegistItemInfoRegistItem60_user(_this, dwRegistSerial, wItemSerial, dwETSerialNumber, dwPrice, bySellTurm, byTableCode, wItemIndex, byStorageIndex, dwD, dwU, bInserted, CUnmannedTraderRegistItemInfoRegistItem60_next);
        };
        void CUnmannedTraderRegistItemInfoRepriceItem62_wrapper(struct CUnmannedTraderRegistItemInfo* _this, unsigned int dwPrice)
        {
           CUnmannedTraderRegistItemInfoRepriceItem62_user(_this, dwPrice, CUnmannedTraderRegistItemInfoRepriceItem62_next);
        };
        void CUnmannedTraderRegistItemInfoSellComplete64_wrapper(struct CUnmannedTraderRegistItemInfo* _this, unsigned int dwPrice, unsigned int dwBuyerSerial, unsigned int dwTax, int64_t tResultTime, char* wszBuyerName, char* szBuyerAccount)
        {
           CUnmannedTraderRegistItemInfoSellComplete64_user(_this, dwPrice, dwBuyerSerial, dwTax, tResultTime, wszBuyerName, szBuyerAccount, CUnmannedTraderRegistItemInfoSellComplete64_next);
        };
        char CUnmannedTraderRegistItemInfoSellWaitItem66_wrapper(struct CUnmannedTraderRegistItemInfo* _this, uint16_t wInx, struct CLogFile* pkLogger, int64_t tResultTime, char* byStorageInx)
        {
           return CUnmannedTraderRegistItemInfoSellWaitItem66_user(_this, wInx, pkLogger, tResultTime, byStorageInx, CUnmannedTraderRegistItemInfoSellWaitItem66_next);
        };
        bool CUnmannedTraderRegistItemInfoSet68_wrapper(struct CUnmannedTraderRegistItemInfo* _this, uint16_t wInx, char byInvenIndex, unsigned int uiInx, struct _TRADE_DB_BASE* kInfo, struct CLogFile* pkLogger)
        {
           return CUnmannedTraderRegistItemInfoSet68_user(_this, wInx, byInvenIndex, uiInx, kInfo, pkLogger, CUnmannedTraderRegistItemInfoSet68_next);
        };
        void CUnmannedTraderRegistItemInfoSetOverRegistTime70_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           CUnmannedTraderRegistItemInfoSetOverRegistTime70_user(_this, CUnmannedTraderRegistItemInfoSetOverRegistTime70_next);
        };
        bool CUnmannedTraderRegistItemInfoSetState72_wrapper(struct CUnmannedTraderRegistItemInfo* _this, char byState)
        {
           return CUnmannedTraderRegistItemInfoSetState72_user(_this, byState, CUnmannedTraderRegistItemInfoSetState72_next);
        };
        
        void CUnmannedTraderRegistItemInfodtor_CUnmannedTraderRegistItemInfo78_wrapper(struct CUnmannedTraderRegistItemInfo* _this)
        {
           CUnmannedTraderRegistItemInfodtor_CUnmannedTraderRegistItemInfo78_user(_this, CUnmannedTraderRegistItemInfodtor_CUnmannedTraderRegistItemInfo78_next);
        };
        
        ::std::array<hook_record, 37> CUnmannedTraderRegistItemInfo_functions = 
        {
            _hook_record {
                (LPVOID)0x140351da0L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoctor_CUnmannedTraderRegistItemInfo2_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoctor_CUnmannedTraderRegistItemInfo2_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoctor_CUnmannedTraderRegistItemInfo2_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::ctor_CUnmannedTraderRegistItemInfo)
            },
            _hook_record {
                (LPVOID)0x140352e50L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoClear4_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoClear4_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoClear4_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::Clear)
            },
            _hook_record {
                (LPVOID)0x14035fe50L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoClearBuyerInfo6_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoClearBuyerInfo6_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoClearBuyerInfo6_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::ClearBuyerInfo)
            },
            _hook_record {
                (LPVOID)0x140360830L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoClearRegist8_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoClearRegist8_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoClearRegist8_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::ClearRegist)
            },
            _hook_record {
                (LPVOID)0x14035f930L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoClearToWaitState10_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoClearToWaitState10_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoClearToWaitState10_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::ClearToWaitState)
            },
            _hook_record {
                (LPVOID)0x1403601a0L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetBuyerSerial12_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetBuyerSerial12_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoGetBuyerSerial12_wrapper),
                (LPVOID)cast_pointer_function((unsigned int(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::GetBuyerSerial)
            },
            _hook_record {
                (LPVOID)0x140243b60L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetD14_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetD14_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoGetD14_wrapper),
                (LPVOID)cast_pointer_function((uint64_t(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::GetD)
            },
            _hook_record {
                (LPVOID)0x140243ae0L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetETSerial16_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetETSerial16_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoGetETSerial16_wrapper),
                (LPVOID)cast_pointer_function((unsigned int(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::GetETSerial)
            },
            _hook_record {
                (LPVOID)0x140243b20L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetItemIndex18_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetItemIndex18_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoGetItemIndex18_wrapper),
                (LPVOID)cast_pointer_function((uint16_t(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::GetItemIndex)
            },
            _hook_record {
                (LPVOID)0x14035f910L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetItemSerial20_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetItemSerial20_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoGetItemSerial20_wrapper),
                (LPVOID)cast_pointer_function((uint16_t(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::GetItemSerial)
            },
            _hook_record {
                (LPVOID)0x14035fca0L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetLeftSec22_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetLeftSec22_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoGetLeftSec22_wrapper),
                (LPVOID)cast_pointer_function((unsigned int(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::GetLeftSec)
            },
            _hook_record {
                (LPVOID)0x140243ba0L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetPrice24_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetPrice24_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoGetPrice24_wrapper),
                (LPVOID)cast_pointer_function((unsigned int(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::GetPrice)
            },
            _hook_record {
                (LPVOID)0x140243ac0L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetRegistSerial26_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetRegistSerial26_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoGetRegistSerial26_wrapper),
                (LPVOID)cast_pointer_function((unsigned int(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::GetRegistSerial)
            },
            _hook_record {
                (LPVOID)0x140360180L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetResultTime28_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetResultTime28_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoGetResultTime28_wrapper),
                (LPVOID)cast_pointer_function((int64_t(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::GetResultTime)
            },
            _hook_record {
                (LPVOID)0x140243c00L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetSellTurm30_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetSellTurm30_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoGetSellTurm30_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::GetSellTurm)
            },
            _hook_record {
                (LPVOID)0x140243be0L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetStartTime32_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetStartTime32_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoGetStartTime32_wrapper),
                (LPVOID)cast_pointer_function((int64_t(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::GetStartTime)
            },
            _hook_record {
                (LPVOID)0x140243bc0L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetStartTimePtr34_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetStartTimePtr34_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoGetStartTimePtr34_wrapper),
                (LPVOID)cast_pointer_function((int64_t*(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::GetStartTimePtr)
            },
            _hook_record {
                (LPVOID)0x1403603d0L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetState36_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetState36_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoGetState36_wrapper),
                (LPVOID)cast_pointer_function((CUnmannedTraderItemState::STATE(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::GetState)
            },
            _hook_record {
                (LPVOID)0x140243b40L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetStorageIndex38_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetStorageIndex38_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoGetStorageIndex38_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::GetStorageIndex)
            },
            _hook_record {
                (LPVOID)0x140243b00L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetTableCode40_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetTableCode40_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoGetTableCode40_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::GetTableCode)
            },
            _hook_record {
                (LPVOID)0x140360160L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetTax42_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetTax42_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoGetTax42_wrapper),
                (LPVOID)cast_pointer_function((unsigned int(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::GetTax)
            },
            _hook_record {
                (LPVOID)0x140243b80L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetU44_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoGetU44_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoGetU44_wrapper),
                (LPVOID)cast_pointer_function((unsigned int(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::GetU)
            },
            _hook_record {
                (LPVOID)0x140360290L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoIsEmpty46_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoIsEmpty46_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoIsEmpty46_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::IsEmpty)
            },
            _hook_record {
                (LPVOID)0x14035fc40L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoIsOverRegistTime48_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoIsOverRegistTime48_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoIsOverRegistTime48_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::IsOverRegistTime)
            },
            _hook_record {
                (LPVOID)0x140243c20L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoIsRegist50_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoIsRegist50_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoIsRegist50_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::IsRegist)
            },
            _hook_record {
                (LPVOID)0x14035fe90L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoIsSellUpdateWait52_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoIsSellUpdateWait52_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoIsSellUpdateWait52_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::IsSellUpdateWait)
            },
            _hook_record {
                (LPVOID)0x140352fc0L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoIsSellWait54_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoIsSellWait54_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoIsSellWait54_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::IsSellWait)
            },
            _hook_record {
                (LPVOID)0x14035ff00L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoIsWaitNoitfyClose56_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoIsWaitNoitfyClose56_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoIsWaitNoitfyClose56_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::IsWaitNoitfyClose)
            },
            _hook_record {
                (LPVOID)0x140360770L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoReRegistItem58_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoReRegistItem58_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoReRegistItem58_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderRegistItemInfo::*)(unsigned int))&CUnmannedTraderRegistItemInfo::ReRegistItem)
            },
            _hook_record {
                (LPVOID)0x1403605b0L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoRegistItem60_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoRegistItem60_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoRegistItem60_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderRegistItemInfo::*)(unsigned int, uint16_t, unsigned int, unsigned int, char, char, uint16_t, char, uint64_t, unsigned int, bool))&CUnmannedTraderRegistItemInfo::RegistItem)
            },
            _hook_record {
                (LPVOID)0x140360810L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoRepriceItem62_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoRepriceItem62_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoRepriceItem62_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderRegistItemInfo::*)(unsigned int))&CUnmannedTraderRegistItemInfo::RepriceItem)
            },
            _hook_record {
                (LPVOID)0x14035f990L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoSellComplete64_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoSellComplete64_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoSellComplete64_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderRegistItemInfo::*)(unsigned int, unsigned int, unsigned int, int64_t, char*, char*))&CUnmannedTraderRegistItemInfo::SellComplete)
            },
            _hook_record {
                (LPVOID)0x140352200L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoSellWaitItem66_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoSellWaitItem66_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoSellWaitItem66_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderRegistItemInfo::*)(uint16_t, struct CLogFile*, int64_t, char*))&CUnmannedTraderRegistItemInfo::SellWaitItem)
            },
            _hook_record {
                (LPVOID)0x140351eb0L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoSet68_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoSet68_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoSet68_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderRegistItemInfo::*)(uint16_t, char, unsigned int, struct _TRADE_DB_BASE*, struct CLogFile*))&CUnmannedTraderRegistItemInfo::Set)
            },
            _hook_record {
                (LPVOID)0x14035fa80L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoSetOverRegistTime70_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoSetOverRegistTime70_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoSetOverRegistTime70_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::SetOverRegistTime)
            },
            _hook_record {
                (LPVOID)0x14035f850L,
                (LPVOID *)&CUnmannedTraderRegistItemInfoSetState72_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfoSetState72_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfoSetState72_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderRegistItemInfo::*)(char))&CUnmannedTraderRegistItemInfo::SetState)
            },
            _hook_record {
                (LPVOID)0x140351e30L,
                (LPVOID *)&CUnmannedTraderRegistItemInfodtor_CUnmannedTraderRegistItemInfo78_user,
                (LPVOID *)&CUnmannedTraderRegistItemInfodtor_CUnmannedTraderRegistItemInfo78_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderRegistItemInfodtor_CUnmannedTraderRegistItemInfo78_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderRegistItemInfo::*)())&CUnmannedTraderRegistItemInfo::dtor_CUnmannedTraderRegistItemInfo)
            },
        };
    }; // end namespace Detail
END_ATF_NAMESPACE
