// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_CHRID.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _use_potion_result_other_zocl
    {
        char byRetCode;
        unsigned __int16 wPotionIndex;
        _CHRID idPerformer;
        _CHRID idDster;
        unsigned __int16 wHP;
        unsigned __int16 wFP;
        unsigned __int16 wSP;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
