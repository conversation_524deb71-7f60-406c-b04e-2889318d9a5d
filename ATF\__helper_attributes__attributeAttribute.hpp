// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace vc_attributes
    {
        namespace helper_attributes
        {
            #pragma pack(push, 4)
            template<>
            struct attributeAttribute
            {
                int ValidOn;
                bool AllowMultiple;
                bool Inherited;
            };
            #pragma pack(pop)
        }; // end namespace helper_attributes
    }; // end namespace vc_attributes
END_ATF_NAMESPACE
