#include <_qry_case_select_guild_master_lastconn.hpp>


START_ATF_NAMESPACE
    _qry_case_select_guild_master_lastconn::_qry_case_select_guild_master_lastconn()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_select_guild_master_lastconn*);
        (org_ptr(0x14025d680L))(this);
    };
    void _qry_case_select_guild_master_lastconn::ctor__qry_case_select_guild_master_lastconn()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_select_guild_master_lastconn*);
        (org_ptr(0x14025d680L))(this);
    };
    int _qry_case_select_guild_master_lastconn::size()
    {
        using org_ptr = int (WINAPIV*)(struct _qry_case_select_guild_master_lastconn*);
        return (org_ptr(0x14025d6a0L))(this);
    };
END_ATF_NAMESPACE
