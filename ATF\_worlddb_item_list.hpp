// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _worlddb_item_list
    {
        struct __item_charge_info
        {
            unsigned int uiSerial;
            unsigned int dwItemCode_K;
            unsigned __int64 dwItemCode_D;
            unsigned int dwItemCode_U;
            unsigned int dwT;
            unsigned __int64 lnUID;
            char szDate[32];
        };
        char byItemCount;
        __item_charge_info itemList[32];
    };
END_ATF_NAMESPACE
