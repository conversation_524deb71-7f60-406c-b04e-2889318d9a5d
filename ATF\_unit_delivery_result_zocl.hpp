// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _unit_delivery_result_zocl
    {
        char byRetCode;
        char bySlotIndex;
        char byTransDistCode;
        unsigned int dwParkingUnitSerial;
        unsigned int dwPayDalant;
        unsigned int dwLeftDalant;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
