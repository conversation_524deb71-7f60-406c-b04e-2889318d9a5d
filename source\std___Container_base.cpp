#include <std___Container_base.hpp>


START_ATF_NAMESPACE
    std::_Container_base::_Container_base(struct std::_Container_base* arg_0)
    {
        using org_ptr = int64_t (WINAPIV*)(struct std::_Container_base*, struct std::_Container_base*);
        (org_ptr(0x1404dbb32L))(this, arg_0);
    };
    int64_t std::_Container_base::ctor__Container_base(struct std::_Container_base* arg_0)
    {
        using org_ptr = int64_t (WINAPIV*)(struct std::_Container_base*, struct std::_Container_base*);
        return (org_ptr(0x1404dbb32L))(this, arg_0);
    };
    std::_Container_base::_Container_base()
    {
        using org_ptr = int64_t (WINAPIV*)(struct std::_Container_base*);
        (org_ptr(0x1404dbad8L))(this);
    };
    int64_t std::_Container_base::ctor__Container_base()
    {
        using org_ptr = int64_t (WINAPIV*)(struct std::_Container_base*);
        return (org_ptr(0x1404dbad8L))(this);
    };
END_ATF_NAMESPACE
