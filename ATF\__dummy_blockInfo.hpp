// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <__dummy_block.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using __dummy_blockctor___dummy_block2_ptr = void (WINAPIV*)(struct __dummy_block*);
        using __dummy_blockctor___dummy_block2_clbk = void (WINAPIV*)(struct __dummy_block*, __dummy_blockctor___dummy_block2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
