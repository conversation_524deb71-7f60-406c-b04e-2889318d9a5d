// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _WeaponItem_fld : _base_fld
    {
        int m_bExist;
        char m_strModel[64];
        int m_nIconIDX;
        char m_strName[64];
        int m_nKindClt;
        int m_nItemGrade;
        int m_nActiveType;
        char m_strActiveCode_key[64];
        int m_nActiveEffLvl;
        float m_fActiveProba;
        int m_nFixPart;
        char m_strBulletType[64];
        char m_strEffBulletType[64];
        int m_nType;
        int m_nSubType;
        char m_strCivil[64];
        char m_strClassLim[64];
        int m_nLevelLim;
        int m_nUpLevelLim;
        int m_nClassGradeLim;
        int m_nExpertID1;
        int m_nExpertLim1;
        int m_nExpertID2;
        int m_nExpertLim2;
        int m_nMoney;
        int m_nStdPrice;
        int m_nStdPoint;
        int m_nGoldPoint;
        int m_nKillPoint;
        int m_nProcPoint;
        int m_nStoragePrice;
        int m_bAbr;
        int m_nDurUnit;
        float m_fEquipSpeed;
        int m_bRepair;
        int m_nRepPrice;
        int m_nEffState;
        int m_nProperty;
        float m_fFireTol;
        float m_fWaterTol;
        float m_fSoilTol;
        float m_fWindTol;
        float m_fAttGap;
        int m_nAttack_DP;
        float m_fGADst;
        int m_nGASpd;
        int m_nGAMinSelProb;
        float m_fGAMinAF;
        int m_nGAMaxSelProb;
        float m_fGAMaxAF;
        int m_nMADst;
        int m_nMASpd;
        int m_nMAMinSelProb;
        float m_fMAMinAF;
        int m_nMAMaxSelProb;
        float m_fMAMaxAF;
        int m_nEff1Code;
        float m_fEff1Unit;
        int m_nEff2Code;
        float m_fEff2Unit;
        int m_nEff3Code;
        float m_fEff3Unit;
        int m_nEff4Code;
        float m_fEff4Unit;
        int m_nDuration;
        int m_bSell;
        int m_bExchange;
        int m_bGround;
        int m_bStoragePossible;
        int m_bUseableNormalAcc;
        int m_nUpgrade;
        char m_strTooltipIndex[64];
        int m_nAttEffType;
        int m_bIsTime;
    };
END_ATF_NAMESPACE
