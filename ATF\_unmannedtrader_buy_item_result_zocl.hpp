// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _unmannedtrader_buy_item_result_zocl
    {
        struct  __list
        {
            char byRet;
            unsigned int dwPrice;
            unsigned __int16 dwNewItemSerial;
            char byItemTableCode;
            unsigned __int16 wItemIndex;
            unsigned __int64 dwDur;
            unsigned int dwLv;
        };
        char byRetCode;
        unsigned int dwPayDalant;
        unsigned int dwLeftDalant;
        char byNum;
        __list List[10];
    public:
        _unmannedtrader_buy_item_result_zocl();
        void ctor__unmannedtrader_buy_item_result_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_unmannedtrader_buy_item_result_zocl, 230>(), "_unmannedtrader_buy_item_result_zocl");
END_ATF_NAMESPACE
