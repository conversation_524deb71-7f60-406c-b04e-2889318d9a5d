#include <_RETURNPOST_DB_BASE.hpp>


START_ATF_NAMESPACE
    void _RETURNPOST_DB_BASE::Init()
    {
        using org_ptr = void (WINAPIV*)(struct _RETURNPOST_DB_BASE*);
        (org_ptr(0x140077570L))(this);
    };
    _RETURNPOST_DB_BASE::_RETURNPOST_DB_BASE()
    {
        using org_ptr = void (WINAPIV*)(struct _RETURNPOST_DB_BASE*);
        (org_ptr(0x140077520L))(this);
    };
    void _RETURNPOST_DB_BASE::ctor__RETURNPOST_DB_BASE()
    {
        using org_ptr = void (WINAPIV*)(struct _RETURNPOST_DB_BASE*);
        (org_ptr(0x140077520L))(this);
    };
END_ATF_NAMESPACE
