// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _unit_delivery_request_clzo
    {
        char bySlotIndex;
        unsigned int dwStoreIndex;
        __int16 zUnitNewPos[3];
        int bUseNPCLinkIntem;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
