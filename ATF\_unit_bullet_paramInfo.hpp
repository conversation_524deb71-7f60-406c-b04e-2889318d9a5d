// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_unit_bullet_param.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _unit_bullet_paramIsFill2_ptr = int (WINAPIV*)(struct _unit_bullet_param*);
        using _unit_bullet_paramIsFill2_clbk = int (WINAPIV*)(struct _unit_bullet_param*, _unit_bullet_paramIsFill2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
