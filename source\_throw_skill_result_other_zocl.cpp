#include <_throw_skill_result_other_zocl.hpp>


START_ATF_NAMESPACE
    _throw_skill_result_other_zocl::_throw_skill_result_other_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _throw_skill_result_other_zocl*);
        (org_ptr(0x1400efd90L))(this);
    };
    void _throw_skill_result_other_zocl::ctor__throw_skill_result_other_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _throw_skill_result_other_zocl*);
        (org_ptr(0x1400efd90L))(this);
    };
END_ATF_NAMESPACE
