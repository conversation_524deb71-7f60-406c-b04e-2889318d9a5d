// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _unmannedtrader_registsingleitem
    {
        char byType;
        char bySellTurm;
        char byRace;
        unsigned int dwOwnerSerial;
        unsigned int dwPrice;
        char byInveninx;
        unsigned int dwK;
        unsigned __int64 dwD;
        unsigned int dwU;
        char byLv;
        char byGrade;
        char byClass1;
        char byClass2;
        char byClass3;
        unsigned int dwT;
        unsigned __int64 lnUID;
        unsigned int dwTax;
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_unmannedtrader_registsingleitem, 64>(), "_unmannedtrader_registsingleitem");
END_ATF_NAMESPACE
