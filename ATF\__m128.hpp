// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    union __m128
    {
        float m128_f32[4];
        unsigned __int64 m128_u64[2];
        char m128_i8[16];
        __int16 m128_i16[8];
        int m128_i32[4];
        __int64 m128_i64[2];
        char m128_u8[16];
        unsigned __int16 m128_u16[8];
        unsigned int m128_u32[4];
    };
END_ATF_NAMESPACE
