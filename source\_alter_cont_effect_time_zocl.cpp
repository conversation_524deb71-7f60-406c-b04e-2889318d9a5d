#include <_alter_cont_effect_time_zocl.hpp>


START_ATF_NAMESPACE
    _alter_cont_effect_time_zocl::_alter_cont_effect_time_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _alter_cont_effect_time_zocl*);
        (org_ptr(0x1400efdf0L))(this);
    };
    void _alter_cont_effect_time_zocl::ctor__alter_cont_effect_time_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _alter_cont_effect_time_zocl*);
        (org_ptr(0x1400efdf0L))(this);
    };
    int _alter_cont_effect_time_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _alter_cont_effect_time_zocl*);
        return (org_ptr(0x1400efe10L))(this);
    };
    
END_ATF_NAMESPACE
