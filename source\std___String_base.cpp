#include <std___String_base.hpp>


START_ATF_NAMESPACE
    std::_String_base::_String_base(struct std::_String_base* arg_0)
    {
        using org_ptr = int64_t (WINAPIV*)(struct std::_String_base*, struct std::_String_base*);
        (org_ptr(0x1404dbb2cL))(this, arg_0);
    };
    int64_t std::_String_base::ctor__String_base(struct std::_String_base* arg_0)
    {
        using org_ptr = int64_t (WINAPIV*)(struct std::_String_base*, struct std::_String_base*);
        return (org_ptr(0x1404dbb2cL))(this, arg_0);
    };
END_ATF_NAMESPACE
