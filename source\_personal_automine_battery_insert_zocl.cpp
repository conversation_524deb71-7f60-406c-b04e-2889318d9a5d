#include <_personal_automine_battery_insert_zocl.hpp>


START_ATF_NAMESPACE
    _personal_automine_battery_insert_zocl::_personal_automine_battery_insert_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_battery_insert_zocl*);
        (org_ptr(0x1402e1970L))(this);
    };
    void _personal_automine_battery_insert_zocl::ctor__personal_automine_battery_insert_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_battery_insert_zocl*);
        (org_ptr(0x1402e1970L))(this);
    };
    int _personal_automine_battery_insert_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _personal_automine_battery_insert_zocl*);
        return (org_ptr(0x1402e19c0L))(this);
    };
END_ATF_NAMESPACE
