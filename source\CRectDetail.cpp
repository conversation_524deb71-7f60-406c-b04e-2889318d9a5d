#include <CRectDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace Detail
    {
        Info::CRectBottomRight1_ptr CRectBottomRight1_next(nullptr);
        Info::CRectBottomRight1_clbk CRectBottomRight1_user(nullptr);
        
        Info::CRectBottomRight2_ptr CRectBottomRight2_next(nullptr);
        Info::CRectBottomRight2_clbk CRectBottomRight2_user(nullptr);
        
        
        Info::CRectctor_CRect3_ptr CRectctor_CRect3_next(nullptr);
        Info::CRectctor_CRect3_clbk CRectctor_CRect3_user(nullptr);
        
        
        Info::CRectctor_CRect4_ptr CRectctor_CRect4_next(nullptr);
        Info::CRectctor_CRect4_clbk CRectctor_CRect4_user(nullptr);
        
        
        Info::CRectctor_CRect5_ptr CRectctor_CRect5_next(nullptr);
        Info::CRectctor_CRect5_clbk CRectctor_CRect5_user(nullptr);
        
        
        Info::CRectctor_CRect6_ptr CRectctor_CRect6_next(nullptr);
        Info::CRectctor_CRect6_clbk CRectctor_CRect6_user(nullptr);
        
        
        Info::CRectctor_CRect7_ptr CRectctor_CRect7_next(nullptr);
        Info::CRectctor_CRect7_clbk CRectctor_CRect7_user(nullptr);
        
        
        Info::CRectctor_CRect8_ptr CRectctor_CRect8_next(nullptr);
        Info::CRectctor_CRect8_clbk CRectctor_CRect8_user(nullptr);
        
        Info::CRectCenterPoint9_ptr CRectCenterPoint9_next(nullptr);
        Info::CRectCenterPoint9_clbk CRectCenterPoint9_user(nullptr);
        
        Info::CRectCopyRect10_ptr CRectCopyRect10_next(nullptr);
        Info::CRectCopyRect10_clbk CRectCopyRect10_user(nullptr);
        
        Info::CRectDeflateRect11_ptr CRectDeflateRect11_next(nullptr);
        Info::CRectDeflateRect11_clbk CRectDeflateRect11_user(nullptr);
        
        Info::CRectDeflateRect12_ptr CRectDeflateRect12_next(nullptr);
        Info::CRectDeflateRect12_clbk CRectDeflateRect12_user(nullptr);
        
        Info::CRectDeflateRect13_ptr CRectDeflateRect13_next(nullptr);
        Info::CRectDeflateRect13_clbk CRectDeflateRect13_user(nullptr);
        
        Info::CRectDeflateRect14_ptr CRectDeflateRect14_next(nullptr);
        Info::CRectDeflateRect14_clbk CRectDeflateRect14_user(nullptr);
        
        Info::CRectEqualRect15_ptr CRectEqualRect15_next(nullptr);
        Info::CRectEqualRect15_clbk CRectEqualRect15_user(nullptr);
        
        Info::CRectHeight16_ptr CRectHeight16_next(nullptr);
        Info::CRectHeight16_clbk CRectHeight16_user(nullptr);
        
        Info::CRectInflateRect17_ptr CRectInflateRect17_next(nullptr);
        Info::CRectInflateRect17_clbk CRectInflateRect17_user(nullptr);
        
        Info::CRectInflateRect18_ptr CRectInflateRect18_next(nullptr);
        Info::CRectInflateRect18_clbk CRectInflateRect18_user(nullptr);
        
        Info::CRectInflateRect19_ptr CRectInflateRect19_next(nullptr);
        Info::CRectInflateRect19_clbk CRectInflateRect19_user(nullptr);
        
        Info::CRectInflateRect20_ptr CRectInflateRect20_next(nullptr);
        Info::CRectInflateRect20_clbk CRectInflateRect20_user(nullptr);
        
        Info::CRectIntersectRect21_ptr CRectIntersectRect21_next(nullptr);
        Info::CRectIntersectRect21_clbk CRectIntersectRect21_user(nullptr);
        
        Info::CRectIsRectEmpty22_ptr CRectIsRectEmpty22_next(nullptr);
        Info::CRectIsRectEmpty22_clbk CRectIsRectEmpty22_user(nullptr);
        
        Info::CRectIsRectNull23_ptr CRectIsRectNull23_next(nullptr);
        Info::CRectIsRectNull23_clbk CRectIsRectNull23_user(nullptr);
        
        Info::CRectMoveToX24_ptr CRectMoveToX24_next(nullptr);
        Info::CRectMoveToX24_clbk CRectMoveToX24_user(nullptr);
        
        Info::CRectMoveToXY25_ptr CRectMoveToXY25_next(nullptr);
        Info::CRectMoveToXY25_clbk CRectMoveToXY25_user(nullptr);
        
        Info::CRectMoveToXY26_ptr CRectMoveToXY26_next(nullptr);
        Info::CRectMoveToXY26_clbk CRectMoveToXY26_user(nullptr);
        
        Info::CRectMoveToY27_ptr CRectMoveToY27_next(nullptr);
        Info::CRectMoveToY27_clbk CRectMoveToY27_user(nullptr);
        
        Info::CRectMulDiv28_ptr CRectMulDiv28_next(nullptr);
        Info::CRectMulDiv28_clbk CRectMulDiv28_user(nullptr);
        
        Info::CRectNormalizeRect29_ptr CRectNormalizeRect29_next(nullptr);
        Info::CRectNormalizeRect29_clbk CRectNormalizeRect29_user(nullptr);
        
        Info::CRectOffsetRect30_ptr CRectOffsetRect30_next(nullptr);
        Info::CRectOffsetRect30_clbk CRectOffsetRect30_user(nullptr);
        
        Info::CRectOffsetRect31_ptr CRectOffsetRect31_next(nullptr);
        Info::CRectOffsetRect31_clbk CRectOffsetRect31_user(nullptr);
        
        Info::CRectOffsetRect32_ptr CRectOffsetRect32_next(nullptr);
        Info::CRectOffsetRect32_clbk CRectOffsetRect32_user(nullptr);
        
        Info::CRectPtInRect33_ptr CRectPtInRect33_next(nullptr);
        Info::CRectPtInRect33_clbk CRectPtInRect33_user(nullptr);
        
        Info::CRectSetRect34_ptr CRectSetRect34_next(nullptr);
        Info::CRectSetRect34_clbk CRectSetRect34_user(nullptr);
        
        Info::CRectSetRect35_ptr CRectSetRect35_next(nullptr);
        Info::CRectSetRect35_clbk CRectSetRect35_user(nullptr);
        
        Info::CRectSetRectEmpty36_ptr CRectSetRectEmpty36_next(nullptr);
        Info::CRectSetRectEmpty36_clbk CRectSetRectEmpty36_user(nullptr);
        
        Info::CRectSize37_ptr CRectSize37_next(nullptr);
        Info::CRectSize37_clbk CRectSize37_user(nullptr);
        
        Info::CRectSubtractRect38_ptr CRectSubtractRect38_next(nullptr);
        Info::CRectSubtractRect38_clbk CRectSubtractRect38_user(nullptr);
        
        Info::CRectSwapLeftRight39_ptr CRectSwapLeftRight39_next(nullptr);
        Info::CRectSwapLeftRight39_clbk CRectSwapLeftRight39_user(nullptr);
        
        Info::CRectSwapLeftRight40_ptr CRectSwapLeftRight40_next(nullptr);
        Info::CRectSwapLeftRight40_clbk CRectSwapLeftRight40_user(nullptr);
        
        Info::CRectTopLeft41_ptr CRectTopLeft41_next(nullptr);
        Info::CRectTopLeft41_clbk CRectTopLeft41_user(nullptr);
        
        Info::CRectTopLeft42_ptr CRectTopLeft42_next(nullptr);
        Info::CRectTopLeft42_clbk CRectTopLeft42_user(nullptr);
        
        Info::CRectUnionRect43_ptr CRectUnionRect43_next(nullptr);
        Info::CRectUnionRect43_clbk CRectUnionRect43_user(nullptr);
        
        Info::CRectWidth44_ptr CRectWidth44_next(nullptr);
        Info::CRectWidth44_clbk CRectWidth44_user(nullptr);
        
        struct CPoint* CRectBottomRight1_wrapper(struct CRect* _this)
        {
           return CRectBottomRight1_user(_this, CRectBottomRight1_next);
        };
        struct CPoint* CRectBottomRight2_wrapper(struct CRect* _this)
        {
           return CRectBottomRight2_user(_this, CRectBottomRight2_next);
        };
        
        void CRectctor_CRect3_wrapper(struct CRect* _this, int l, int t, int r, int b)
        {
           CRectctor_CRect3_user(_this, l, t, r, b, CRectctor_CRect3_next);
        };
        
        void CRectctor_CRect4_wrapper(struct CRect* _this, struct tagPOINT topLeft, struct tagPOINT bottomRight)
        {
           CRectctor_CRect4_user(_this, topLeft, bottomRight, CRectctor_CRect4_next);
        };
        
        void CRectctor_CRect5_wrapper(struct CRect* _this, struct tagPOINT point, struct tagSIZE size)
        {
           CRectctor_CRect5_user(_this, point, size, CRectctor_CRect5_next);
        };
        
        void CRectctor_CRect6_wrapper(struct CRect* _this, struct tagRECT* srcRect)
        {
           CRectctor_CRect6_user(_this, srcRect, CRectctor_CRect6_next);
        };
        
        void CRectctor_CRect7_wrapper(struct CRect* _this, struct tagRECT* lpSrcRect)
        {
           CRectctor_CRect7_user(_this, lpSrcRect, CRectctor_CRect7_next);
        };
        
        void CRectctor_CRect8_wrapper(struct CRect* _this)
        {
           CRectctor_CRect8_user(_this, CRectctor_CRect8_next);
        };
        struct CPoint* CRectCenterPoint9_wrapper(struct CRect* _this, struct CPoint* result)
        {
           return CRectCenterPoint9_user(_this, result, CRectCenterPoint9_next);
        };
        void CRectCopyRect10_wrapper(struct CRect* _this, struct tagRECT* lpSrcRect)
        {
           CRectCopyRect10_user(_this, lpSrcRect, CRectCopyRect10_next);
        };
        void CRectDeflateRect11_wrapper(struct CRect* _this, int x, int y)
        {
           CRectDeflateRect11_user(_this, x, y, CRectDeflateRect11_next);
        };
        void CRectDeflateRect12_wrapper(struct CRect* _this, int l, int t, int r, int b)
        {
           CRectDeflateRect12_user(_this, l, t, r, b, CRectDeflateRect12_next);
        };
        void CRectDeflateRect13_wrapper(struct CRect* _this, struct tagRECT* lpRect)
        {
           CRectDeflateRect13_user(_this, lpRect, CRectDeflateRect13_next);
        };
        void CRectDeflateRect14_wrapper(struct CRect* _this, struct tagSIZE size)
        {
           CRectDeflateRect14_user(_this, size, CRectDeflateRect14_next);
        };
        int CRectEqualRect15_wrapper(struct CRect* _this, struct tagRECT* lpRect)
        {
           return CRectEqualRect15_user(_this, lpRect, CRectEqualRect15_next);
        };
        int CRectHeight16_wrapper(struct CRect* _this)
        {
           return CRectHeight16_user(_this, CRectHeight16_next);
        };
        void CRectInflateRect17_wrapper(struct CRect* _this, int x, int y)
        {
           CRectInflateRect17_user(_this, x, y, CRectInflateRect17_next);
        };
        void CRectInflateRect18_wrapper(struct CRect* _this, int l, int t, int r, int b)
        {
           CRectInflateRect18_user(_this, l, t, r, b, CRectInflateRect18_next);
        };
        void CRectInflateRect19_wrapper(struct CRect* _this, struct tagRECT* lpRect)
        {
           CRectInflateRect19_user(_this, lpRect, CRectInflateRect19_next);
        };
        void CRectInflateRect20_wrapper(struct CRect* _this, struct tagSIZE size)
        {
           CRectInflateRect20_user(_this, size, CRectInflateRect20_next);
        };
        int CRectIntersectRect21_wrapper(struct CRect* _this, struct tagRECT* lpRect1, struct tagRECT* lpRect2)
        {
           return CRectIntersectRect21_user(_this, lpRect1, lpRect2, CRectIntersectRect21_next);
        };
        int CRectIsRectEmpty22_wrapper(struct CRect* _this)
        {
           return CRectIsRectEmpty22_user(_this, CRectIsRectEmpty22_next);
        };
        int CRectIsRectNull23_wrapper(struct CRect* _this)
        {
           return CRectIsRectNull23_user(_this, CRectIsRectNull23_next);
        };
        void CRectMoveToX24_wrapper(struct CRect* _this, int x)
        {
           CRectMoveToX24_user(_this, x, CRectMoveToX24_next);
        };
        void CRectMoveToXY25_wrapper(struct CRect* _this, int x, int y)
        {
           CRectMoveToXY25_user(_this, x, y, CRectMoveToXY25_next);
        };
        void CRectMoveToXY26_wrapper(struct CRect* _this, struct tagPOINT pt)
        {
           CRectMoveToXY26_user(_this, pt, CRectMoveToXY26_next);
        };
        void CRectMoveToY27_wrapper(struct CRect* _this, int y)
        {
           CRectMoveToY27_user(_this, y, CRectMoveToY27_next);
        };
        struct CRect* CRectMulDiv28_wrapper(struct CRect* _this, struct CRect* result, int nMultiplier, int nDivisor)
        {
           return CRectMulDiv28_user(_this, result, nMultiplier, nDivisor, CRectMulDiv28_next);
        };
        void CRectNormalizeRect29_wrapper(struct CRect* _this)
        {
           CRectNormalizeRect29_user(_this, CRectNormalizeRect29_next);
        };
        void CRectOffsetRect30_wrapper(struct CRect* _this, int x, int y)
        {
           CRectOffsetRect30_user(_this, x, y, CRectOffsetRect30_next);
        };
        void CRectOffsetRect31_wrapper(struct CRect* _this, struct tagPOINT point)
        {
           CRectOffsetRect31_user(_this, point, CRectOffsetRect31_next);
        };
        void CRectOffsetRect32_wrapper(struct CRect* _this, struct tagSIZE size)
        {
           CRectOffsetRect32_user(_this, size, CRectOffsetRect32_next);
        };
        int CRectPtInRect33_wrapper(struct CRect* _this, struct tagPOINT point)
        {
           return CRectPtInRect33_user(_this, point, CRectPtInRect33_next);
        };
        void CRectSetRect34_wrapper(struct CRect* _this, int x1, int y1, int x2, int y2)
        {
           CRectSetRect34_user(_this, x1, y1, x2, y2, CRectSetRect34_next);
        };
        void CRectSetRect35_wrapper(struct CRect* _this, struct tagPOINT topLeft, struct tagPOINT bottomRight)
        {
           CRectSetRect35_user(_this, topLeft, bottomRight, CRectSetRect35_next);
        };
        void CRectSetRectEmpty36_wrapper(struct CRect* _this)
        {
           CRectSetRectEmpty36_user(_this, CRectSetRectEmpty36_next);
        };
        struct CSize* CRectSize37_wrapper(struct CRect* _this, struct CSize* result)
        {
           return CRectSize37_user(_this, result, CRectSize37_next);
        };
        int CRectSubtractRect38_wrapper(struct CRect* _this, struct tagRECT* lpRectSrc1, struct tagRECT* lpRectSrc2)
        {
           return CRectSubtractRect38_user(_this, lpRectSrc1, lpRectSrc2, CRectSubtractRect38_next);
        };
        void CRectSwapLeftRight39_wrapper(struct tagRECT* lpRect)
        {
           CRectSwapLeftRight39_user(lpRect, CRectSwapLeftRight39_next);
        };
        void CRectSwapLeftRight40_wrapper(struct CRect* _this)
        {
           CRectSwapLeftRight40_user(_this, CRectSwapLeftRight40_next);
        };
        struct CPoint* CRectTopLeft41_wrapper(struct CRect* _this)
        {
           return CRectTopLeft41_user(_this, CRectTopLeft41_next);
        };
        struct CPoint* CRectTopLeft42_wrapper(struct CRect* _this)
        {
           return CRectTopLeft42_user(_this, CRectTopLeft42_next);
        };
        int CRectUnionRect43_wrapper(struct CRect* _this, struct tagRECT* lpRect1, struct tagRECT* lpRect2)
        {
           return CRectUnionRect43_user(_this, lpRect1, lpRect2, CRectUnionRect43_next);
        };
        int CRectWidth44_wrapper(struct CRect* _this)
        {
           return CRectWidth44_user(_this, CRectWidth44_next);
        };
        
        ::std::array<hook_record, 44> CRect_functions = 
        {
            _hook_record {
                (LPVOID)0x140671260L,
                (LPVOID *)&CRectBottomRight1_user,
                (LPVOID *)&CRectBottomRight1_next,
                (LPVOID)cast_pointer_function(CRectBottomRight1_wrapper),
                (LPVOID)cast_pointer_function((struct CPoint*(CRect::*)())&CRect::BottomRight)
            },
            _hook_record {
                (LPVOID)0x1406712a0L,
                (LPVOID *)&CRectBottomRight2_user,
                (LPVOID *)&CRectBottomRight2_next,
                (LPVOID)cast_pointer_function(CRectBottomRight2_wrapper),
                (LPVOID)cast_pointer_function((struct CPoint*(CRect::*)())&CRect::BottomRight)
            },
            _hook_record {
                (LPVOID)0x140671050L,
                (LPVOID *)&CRectctor_CRect3_user,
                (LPVOID *)&CRectctor_CRect3_next,
                (LPVOID)cast_pointer_function(CRectctor_CRect3_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(int, int, int, int))&CRect::ctor_CRect)
            },
            _hook_record {
                (LPVOID)0x140671160L,
                (LPVOID *)&CRectctor_CRect4_user,
                (LPVOID *)&CRectctor_CRect4_next,
                (LPVOID)cast_pointer_function(CRectctor_CRect4_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(struct tagPOINT, struct tagPOINT))&CRect::ctor_CRect)
            },
            _hook_record {
                (LPVOID)0x140671100L,
                (LPVOID *)&CRectctor_CRect5_user,
                (LPVOID *)&CRectctor_CRect5_next,
                (LPVOID)cast_pointer_function(CRectctor_CRect5_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(struct tagPOINT, struct tagSIZE))&CRect::ctor_CRect)
            },
            _hook_record {
                (LPVOID)0x1406710a0L,
                (LPVOID *)&CRectctor_CRect6_user,
                (LPVOID *)&CRectctor_CRect6_next,
                (LPVOID)cast_pointer_function(CRectctor_CRect6_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(struct tagRECT*))&CRect::ctor_CRect)
            },
            _hook_record {
                (LPVOID)0x1406710d0L,
                (LPVOID *)&CRectctor_CRect7_user,
                (LPVOID *)&CRectctor_CRect7_next,
                (LPVOID)cast_pointer_function(CRectctor_CRect7_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(struct tagRECT*))&CRect::ctor_CRect)
            },
            _hook_record {
                (LPVOID)0x140671030L,
                (LPVOID *)&CRectctor_CRect8_user,
                (LPVOID *)&CRectctor_CRect8_next,
                (LPVOID)cast_pointer_function(CRectctor_CRect8_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)())&CRect::ctor_CRect)
            },
            _hook_record {
                (LPVOID)0x1406712c0L,
                (LPVOID *)&CRectCenterPoint9_user,
                (LPVOID *)&CRectCenterPoint9_next,
                (LPVOID)cast_pointer_function(CRectCenterPoint9_wrapper),
                (LPVOID)cast_pointer_function((struct CPoint*(CRect::*)(struct CPoint*))&CRect::CenterPoint)
            },
            _hook_record {
                (LPVOID)0x140671520L,
                (LPVOID *)&CRectCopyRect10_user,
                (LPVOID *)&CRectCopyRect10_next,
                (LPVOID)cast_pointer_function(CRectCopyRect10_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(struct tagRECT*))&CRect::CopyRect)
            },
            _hook_record {
                (LPVOID)0x1406715f0L,
                (LPVOID *)&CRectDeflateRect11_user,
                (LPVOID *)&CRectDeflateRect11_next,
                (LPVOID)cast_pointer_function(CRectDeflateRect11_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(int, int))&CRect::DeflateRect)
            },
            _hook_record {
                (LPVOID)0x1406720c0L,
                (LPVOID *)&CRectDeflateRect12_user,
                (LPVOID *)&CRectDeflateRect12_next,
                (LPVOID)cast_pointer_function(CRectDeflateRect12_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(int, int, int, int))&CRect::DeflateRect)
            },
            _hook_record {
                (LPVOID)0x140672040L,
                (LPVOID *)&CRectDeflateRect13_user,
                (LPVOID *)&CRectDeflateRect13_next,
                (LPVOID)cast_pointer_function(CRectDeflateRect13_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(struct tagRECT*))&CRect::DeflateRect)
            },
            _hook_record {
                (LPVOID)0x140671630L,
                (LPVOID *)&CRectDeflateRect14_user,
                (LPVOID *)&CRectDeflateRect14_next,
                (LPVOID)cast_pointer_function(CRectDeflateRect14_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(struct tagSIZE))&CRect::DeflateRect)
            },
            _hook_record {
                (LPVOID)0x140671550L,
                (LPVOID *)&CRectEqualRect15_user,
                (LPVOID *)&CRectEqualRect15_next,
                (LPVOID)cast_pointer_function(CRectEqualRect15_wrapper),
                (LPVOID)cast_pointer_function((int(CRect::*)(struct tagRECT*))&CRect::EqualRect)
            },
            _hook_record {
                (LPVOID)0x1406711d0L,
                (LPVOID *)&CRectHeight16_user,
                (LPVOID *)&CRectHeight16_next,
                (LPVOID)cast_pointer_function(CRectHeight16_wrapper),
                (LPVOID)cast_pointer_function((int(CRect::*)())&CRect::Height)
            },
            _hook_record {
                (LPVOID)0x140671580L,
                (LPVOID *)&CRectInflateRect17_user,
                (LPVOID *)&CRectInflateRect17_next,
                (LPVOID)cast_pointer_function(CRectInflateRect17_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(int, int))&CRect::InflateRect)
            },
            _hook_record {
                (LPVOID)0x140671fd0L,
                (LPVOID *)&CRectInflateRect18_user,
                (LPVOID *)&CRectInflateRect18_next,
                (LPVOID)cast_pointer_function(CRectInflateRect18_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(int, int, int, int))&CRect::InflateRect)
            },
            _hook_record {
                (LPVOID)0x140671f50L,
                (LPVOID *)&CRectInflateRect19_user,
                (LPVOID *)&CRectInflateRect19_next,
                (LPVOID)cast_pointer_function(CRectInflateRect19_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(struct tagRECT*))&CRect::InflateRect)
            },
            _hook_record {
                (LPVOID)0x1406715c0L,
                (LPVOID *)&CRectInflateRect20_user,
                (LPVOID *)&CRectInflateRect20_next,
                (LPVOID)cast_pointer_function(CRectInflateRect20_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(struct tagSIZE))&CRect::InflateRect)
            },
            _hook_record {
                (LPVOID)0x140671810L,
                (LPVOID *)&CRectIntersectRect21_user,
                (LPVOID *)&CRectIntersectRect21_next,
                (LPVOID)cast_pointer_function(CRectIntersectRect21_wrapper),
                (LPVOID)cast_pointer_function((int(CRect::*)(struct tagRECT*, struct tagRECT*))&CRect::IntersectRect)
            },
            _hook_record {
                (LPVOID)0x1406713c0L,
                (LPVOID *)&CRectIsRectEmpty22_user,
                (LPVOID *)&CRectIsRectEmpty22_next,
                (LPVOID)cast_pointer_function(CRectIsRectEmpty22_wrapper),
                (LPVOID)cast_pointer_function((int(CRect::*)())&CRect::IsRectEmpty)
            },
            _hook_record {
                (LPVOID)0x1406713e0L,
                (LPVOID *)&CRectIsRectNull23_user,
                (LPVOID *)&CRectIsRectNull23_next,
                (LPVOID)cast_pointer_function(CRectIsRectNull23_wrapper),
                (LPVOID)cast_pointer_function((int(CRect::*)())&CRect::IsRectNull)
            },
            _hook_record {
                (LPVOID)0x140671750L,
                (LPVOID *)&CRectMoveToX24_user,
                (LPVOID *)&CRectMoveToX24_next,
                (LPVOID)cast_pointer_function(CRectMoveToX24_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(int))&CRect::MoveToX)
            },
            _hook_record {
                (LPVOID)0x140671790L,
                (LPVOID *)&CRectMoveToXY25_user,
                (LPVOID *)&CRectMoveToXY25_next,
                (LPVOID)cast_pointer_function(CRectMoveToXY25_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(int, int))&CRect::MoveToXY)
            },
            _hook_record {
                (LPVOID)0x1406717d0L,
                (LPVOID *)&CRectMoveToXY26_user,
                (LPVOID *)&CRectMoveToXY26_next,
                (LPVOID)cast_pointer_function(CRectMoveToXY26_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(struct tagPOINT))&CRect::MoveToXY)
            },
            _hook_record {
                (LPVOID)0x140671710L,
                (LPVOID *)&CRectMoveToY27_user,
                (LPVOID *)&CRectMoveToY27_next,
                (LPVOID)cast_pointer_function(CRectMoveToY27_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(int))&CRect::MoveToY)
            },
            _hook_record {
                (LPVOID)0x140672130L,
                (LPVOID *)&CRectMulDiv28_user,
                (LPVOID *)&CRectMulDiv28_next,
                (LPVOID)cast_pointer_function(CRectMulDiv28_wrapper),
                (LPVOID)cast_pointer_function((struct CRect*(CRect::*)(struct CRect*, int, int))&CRect::MulDiv)
            },
            _hook_record {
                (LPVOID)0x140671ec0L,
                (LPVOID *)&CRectNormalizeRect29_user,
                (LPVOID *)&CRectNormalizeRect29_next,
                (LPVOID)cast_pointer_function(CRectNormalizeRect29_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)())&CRect::NormalizeRect)
            },
            _hook_record {
                (LPVOID)0x140671670L,
                (LPVOID *)&CRectOffsetRect30_user,
                (LPVOID *)&CRectOffsetRect30_next,
                (LPVOID)cast_pointer_function(CRectOffsetRect30_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(int, int))&CRect::OffsetRect)
            },
            _hook_record {
                (LPVOID)0x1406716b0L,
                (LPVOID *)&CRectOffsetRect31_user,
                (LPVOID *)&CRectOffsetRect31_next,
                (LPVOID)cast_pointer_function(CRectOffsetRect31_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(struct tagPOINT))&CRect::OffsetRect)
            },
            _hook_record {
                (LPVOID)0x1406716e0L,
                (LPVOID *)&CRectOffsetRect32_user,
                (LPVOID *)&CRectOffsetRect32_next,
                (LPVOID)cast_pointer_function(CRectOffsetRect32_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(struct tagSIZE))&CRect::OffsetRect)
            },
            _hook_record {
                (LPVOID)0x140671440L,
                (LPVOID *)&CRectPtInRect33_user,
                (LPVOID *)&CRectPtInRect33_next,
                (LPVOID)cast_pointer_function(CRectPtInRect33_wrapper),
                (LPVOID)cast_pointer_function((int(CRect::*)(struct tagPOINT))&CRect::PtInRect)
            },
            _hook_record {
                (LPVOID)0x140671470L,
                (LPVOID *)&CRectSetRect34_user,
                (LPVOID *)&CRectSetRect34_next,
                (LPVOID)cast_pointer_function(CRectSetRect34_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(int, int, int, int))&CRect::SetRect)
            },
            _hook_record {
                (LPVOID)0x1406714c0L,
                (LPVOID *)&CRectSetRect35_user,
                (LPVOID *)&CRectSetRect35_next,
                (LPVOID)cast_pointer_function(CRectSetRect35_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)(struct tagPOINT, struct tagPOINT))&CRect::SetRect)
            },
            _hook_record {
                (LPVOID)0x140671500L,
                (LPVOID *)&CRectSetRectEmpty36_user,
                (LPVOID *)&CRectSetRectEmpty36_next,
                (LPVOID)cast_pointer_function(CRectSetRectEmpty36_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)())&CRect::SetRectEmpty)
            },
            _hook_record {
                (LPVOID)0x1406711f0L,
                (LPVOID *)&CRectSize37_user,
                (LPVOID *)&CRectSize37_next,
                (LPVOID)cast_pointer_function(CRectSize37_wrapper),
                (LPVOID)cast_pointer_function((struct CSize*(CRect::*)(struct CSize*))&CRect::Size)
            },
            _hook_record {
                (LPVOID)0x140671e80L,
                (LPVOID *)&CRectSubtractRect38_user,
                (LPVOID *)&CRectSubtractRect38_next,
                (LPVOID)cast_pointer_function(CRectSubtractRect38_wrapper),
                (LPVOID)cast_pointer_function((int(CRect::*)(struct tagRECT*, struct tagRECT*))&CRect::SubtractRect)
            },
            _hook_record {
                (LPVOID)0x140671340L,
                (LPVOID *)&CRectSwapLeftRight39_user,
                (LPVOID *)&CRectSwapLeftRight39_next,
                (LPVOID)cast_pointer_function(CRectSwapLeftRight39_wrapper),
                (LPVOID)cast_pointer_function((void(*)(struct tagRECT*))&CRect::SwapLeftRight)
            },
            _hook_record {
                (LPVOID)0x140671320L,
                (LPVOID *)&CRectSwapLeftRight40_user,
                (LPVOID *)&CRectSwapLeftRight40_next,
                (LPVOID)cast_pointer_function(CRectSwapLeftRight40_wrapper),
                (LPVOID)cast_pointer_function((void(CRect::*)())&CRect::SwapLeftRight)
            },
            _hook_record {
                (LPVOID)0x140671240L,
                (LPVOID *)&CRectTopLeft41_user,
                (LPVOID *)&CRectTopLeft41_next,
                (LPVOID)cast_pointer_function(CRectTopLeft41_wrapper),
                (LPVOID)cast_pointer_function((struct CPoint*(CRect::*)())&CRect::TopLeft)
            },
            _hook_record {
                (LPVOID)0x140671280L,
                (LPVOID *)&CRectTopLeft42_user,
                (LPVOID *)&CRectTopLeft42_next,
                (LPVOID)cast_pointer_function(CRectTopLeft42_wrapper),
                (LPVOID)cast_pointer_function((struct CPoint*(CRect::*)())&CRect::TopLeft)
            },
            _hook_record {
                (LPVOID)0x140671850L,
                (LPVOID *)&CRectUnionRect43_user,
                (LPVOID *)&CRectUnionRect43_next,
                (LPVOID)cast_pointer_function(CRectUnionRect43_wrapper),
                (LPVOID)cast_pointer_function((int(CRect::*)(struct tagRECT*, struct tagRECT*))&CRect::UnionRect)
            },
            _hook_record {
                (LPVOID)0x1406711b0L,
                (LPVOID *)&CRectWidth44_user,
                (LPVOID *)&CRectWidth44_next,
                (LPVOID)cast_pointer_function(CRectWidth44_wrapper),
                (LPVOID)cast_pointer_function((int(CRect::*)())&CRect::Width)
            },
        };
    }; // end namespace Detail
END_ATF_NAMESPACE
