@echo off
REM Build script for Visual Studio 2022 on Windows

echo ========================================
echo NexusPro Visual Studio 2022 Build Script
echo ========================================

REM Check if CMake is available
cmake --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: CMake is not installed or not in PATH
    echo Please install CMake and add it to your PATH
    pause
    exit /b 1
)

REM Create build directory
if not exist "build" mkdir build
cd build

echo.
echo Configuring project with CMake...
echo.

REM Configure for Visual Studio 2022 (preferred) with fallback options
echo Configuring for Visual Studio 2022...
cmake .. -G "Visual Studio 17 2022" -A x64 -DBUILD_MONOLITHIC_DLL=ON 2>nul
if %errorlevel% neq 0 (
    echo Visual Studio 2022 not found, trying Visual Studio 2019...
    cmake .. -G "Visual Studio 16 2019" -A x64 -DBUILD_MONOLITHIC_DLL=ON 2>nul
    if %errorlevel% neq 0 (
        echo Visual Studio 2019 not found, trying Visual Studio 2017...
        cmake .. -G "Visual Studio 15 2017" -A x64 -DBUILD_MONOLITHIC_DLL=ON 2>nul
        if %errorlevel% neq 0 (
            echo ERROR: No compatible Visual Studio version found
            echo Please install Visual Studio 2017 or later with C++ workload
            echo.
            echo Required components:
            echo - MSVC v143 compiler toolset
            echo - Windows 10/11 SDK
            echo - CMake tools for Visual Studio
            pause
            exit /b 1
        )
    )
)

echo.
echo Configuration completed successfully!
echo.

REM Build the project
echo Building project...
cmake --build . --config Release

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo Build completed successfully!
    echo ========================================
    echo.
    echo Generated files:
    echo - Visual Studio solution: build\NexusPro.sln
    echo - Test executable: build\Release\NexusProTest.exe
    echo - Monolithic DLL: build\bin\Release\NexusProMod.dll
    echo - DLL Injector: build\bin\Release\NexusProInjector.exe
    echo.
    echo To open in Visual Studio 2022:
    echo   start build\NexusPro.sln
    echo.
    echo For game modification:
    echo 1. Build the solution in Release mode
    echo 2. Use NexusProInjector.exe to inject NexusProMod.dll into your target game
    echo.
    echo Running test application...
    echo.
    Release\NexusProTest.exe
) else (
    echo.
    echo ========================================
    echo Build failed!
    echo ========================================
    echo Please check the error messages above.
    echo Make sure you have Visual Studio 2022 with C++ workload installed.
)

echo.
echo Press any key to exit...
pause >nul

cd ..
