// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$8FA45092402D762F87B6C8204B98EBDA.hpp>


START_ATF_NAMESPACE
    struct _wireVARIANT
    {
        unsigned int clSize;
        unsigned int rpcReserved;
        unsigned __int16 vt;
        unsigned __int16 wReserved1;
        unsigned __int16 wReserved2;
        unsigned __int16 wReserved3;
        $8FA45092402D762F87B6C8204B98EBDA ___u6;
    };
END_ATF_NAMESPACE
