// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GUID.hpp>


START_ATF_NAMESPACE
    struct _VIDEOPARAMETERS
    {
        _GUID Guid;
        unsigned int dwOffset;
        unsigned int dwCommand;
        unsigned int dwFlags;
        unsigned int dwMode;
        unsigned int dwTVStandard;
        unsigned int dwAvailableModes;
        unsigned int dwAvailableTVStandard;
        unsigned int dwFlickerFilter;
        unsigned int dwOverScanX;
        unsigned int dwOverScanY;
        unsigned int dwMaxUnscaledX;
        unsigned int dwMaxUnscaledY;
        unsigned int dwPositionX;
        unsigned int dwPositionY;
        unsigned int dwBrightness;
        unsigned int dwContrast;
        unsigned int dwCPType;
        unsigned int dwCPCommand;
        unsigned int dwCPStandard;
        unsigned int dwCPKey;
        unsigned int bCP_APSTriggerBits;
        char bOEMCopyProtection[256];
    };
END_ATF_NAMESPACE
