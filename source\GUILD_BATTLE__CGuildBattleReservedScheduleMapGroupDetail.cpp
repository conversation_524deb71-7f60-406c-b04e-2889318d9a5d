#include <GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Detail
        {
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupAdd2_ptr GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupAdd2_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupAdd2_clbk GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupAdd2_user(nullptr);
            
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupctor_CGuildBattleReservedScheduleMapGroup4_ptr GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupctor_CGuildBattleReservedScheduleMapGroup4_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupctor_CGuildBattleReservedScheduleMapGroup4_clbk GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupctor_CGuildBattleReservedScheduleMapGroup4_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCleanUpDanglingReservedSchedule6_ptr GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCleanUpDanglingReservedSchedule6_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCleanUpDanglingReservedSchedule6_clbk GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCleanUpDanglingReservedSchedule6_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear8_ptr GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear8_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear8_clbk GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear8_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear10_ptr GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear10_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear10_clbk GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear10_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCopyUseTimeField12_ptr GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCopyUseTimeField12_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCopyUseTimeField12_clbk GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCopyUseTimeField12_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupFlip14_ptr GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupFlip14_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupFlip14_clbk GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupFlip14_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetCurScheduleID16_ptr GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetCurScheduleID16_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetCurScheduleID16_clbk GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetCurScheduleID16_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetDayID18_ptr GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetDayID18_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetDayID18_clbk GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetDayID18_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetSLID20_ptr GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetSLID20_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetSLID20_clbk GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetSLID20_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupInit22_ptr GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupInit22_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupInit22_clbk GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupInit22_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsDone24_ptr GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsDone24_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsDone24_clbk GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsDone24_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsEmptyTime26_ptr GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsEmptyTime26_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsEmptyTime26_clbk GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsEmptyTime26_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoad28_ptr GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoad28_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoad28_clbk GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoad28_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoop30_ptr GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoop30_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoop30_clbk GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoop30_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupPushDQSClear32_ptr GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupPushDQSClear32_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupPushDQSClear32_clbk GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupPushDQSClear32_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupUpdateUseFlag34_ptr GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupUpdateUseFlag34_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupUpdateUseFlag34_clbk GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupUpdateUseFlag34_user(nullptr);
            
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupdtor_CGuildBattleReservedScheduleMapGroup38_ptr GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupdtor_CGuildBattleReservedScheduleMapGroup38_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupdtor_CGuildBattleReservedScheduleMapGroup38_clbk GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupdtor_CGuildBattleReservedScheduleMapGroup38_user(nullptr);
            
            char GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupAdd2_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup* _this, unsigned int uiFieldInx, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt, struct GUILD_BATTLE::CGuildBattleSchedule** ppkSchedule, unsigned int* uiSLID)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupAdd2_user(_this, uiFieldInx, dwStartTimeInx, dwElapseTimeCnt, ppkSchedule, uiSLID, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupAdd2_next);
            };
            
            void GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupctor_CGuildBattleReservedScheduleMapGroup4_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup* _this)
            {
               GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupctor_CGuildBattleReservedScheduleMapGroup4_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupctor_CGuildBattleReservedScheduleMapGroup4_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCleanUpDanglingReservedSchedule6_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup* _this)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCleanUpDanglingReservedSchedule6_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCleanUpDanglingReservedSchedule6_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear8_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup* _this, unsigned int uiMapID, unsigned int dwID)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear8_user(_this, uiMapID, dwID, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear8_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear10_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup* _this)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear10_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear10_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCopyUseTimeField12_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup* _this, unsigned int uiMapID, bool* pbField)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCopyUseTimeField12_user(_this, uiMapID, pbField, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCopyUseTimeField12_next);
            };
            void GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupFlip14_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup* _this)
            {
               GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupFlip14_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupFlip14_next);
            };
            unsigned int GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetCurScheduleID16_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup* _this, unsigned int uiMapID)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetCurScheduleID16_user(_this, uiMapID, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetCurScheduleID16_next);
            };
            unsigned int GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetDayID18_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup* _this)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetDayID18_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetDayID18_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetSLID20_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup* _this, unsigned int uiMapID, unsigned int* uiSLID)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetSLID20_user(_this, uiMapID, uiSLID, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetSLID20_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupInit22_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup* _this, unsigned int uiDayInx, unsigned int uiMapCnt)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupInit22_user(_this, uiDayInx, uiMapCnt, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupInit22_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsDone24_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup* _this)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsDone24_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsDone24_next);
            };
            char GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsEmptyTime26_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup* _this, unsigned int uiFieldInx, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsEmptyTime26_user(_this, uiFieldInx, dwStartTimeInx, dwElapseTimeCnt, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsEmptyTime26_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoad28_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup* _this, bool bToday)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoad28_user(_this, bToday, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoad28_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoop30_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup* _this)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoop30_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoop30_next);
            };
            void GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupPushDQSClear32_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup* _this)
            {
               GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupPushDQSClear32_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupPushDQSClear32_next);
            };
            struct GUILD_BATTLE::CGuildBattleSchedule* GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupUpdateUseFlag34_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup* _this, unsigned int uiMapID, unsigned int dwID)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupUpdateUseFlag34_user(_this, uiMapID, dwID, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupUpdateUseFlag34_next);
            };
            
            void GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupdtor_CGuildBattleReservedScheduleMapGroup38_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup* _this)
            {
               GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupdtor_CGuildBattleReservedScheduleMapGroup38_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupdtor_CGuildBattleReservedScheduleMapGroup38_next);
            };
            
            ::std::array<hook_record, 18> CGuildBattleReservedScheduleMapGroup_functions = 
            {
                _hook_record {
                    (LPVOID)0x1403dc440L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupAdd2_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupAdd2_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupAdd2_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::*)(unsigned int, unsigned int, unsigned int, struct GUILD_BATTLE::CGuildBattleSchedule**, unsigned int*))&GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Add)
                },
                _hook_record {
                    (LPVOID)0x1403dba50L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupctor_CGuildBattleReservedScheduleMapGroup4_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupctor_CGuildBattleReservedScheduleMapGroup4_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupctor_CGuildBattleReservedScheduleMapGroup4_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::*)())&GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::ctor_CGuildBattleReservedScheduleMapGroup)
                },
                _hook_record {
                    (LPVOID)0x1403dc5f0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCleanUpDanglingReservedSchedule6_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCleanUpDanglingReservedSchedule6_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCleanUpDanglingReservedSchedule6_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::*)())&GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::CleanUpDanglingReservedSchedule)
                },
                _hook_record {
                    (LPVOID)0x1403dc020L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear8_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear8_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear8_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::*)(unsigned int, unsigned int))&GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Clear)
                },
                _hook_record {
                    (LPVOID)0x1403dc230L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear10_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear10_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupClear10_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::*)())&GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Clear)
                },
                _hook_record {
                    (LPVOID)0x1403dc4f0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCopyUseTimeField12_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCopyUseTimeField12_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupCopyUseTimeField12_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::*)(unsigned int, bool*))&GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::CopyUseTimeField)
                },
                _hook_record {
                    (LPVOID)0x1403dc1a0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupFlip14_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupFlip14_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupFlip14_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::*)())&GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Flip)
                },
                _hook_record {
                    (LPVOID)0x1403dc710L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetCurScheduleID16_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetCurScheduleID16_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetCurScheduleID16_wrapper),
                    (LPVOID)cast_pointer_function((unsigned int(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::*)(unsigned int))&GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::GetCurScheduleID)
                },
                _hook_record {
                    (LPVOID)0x1403d9a90L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetDayID18_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetDayID18_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetDayID18_wrapper),
                    (LPVOID)cast_pointer_function((unsigned int(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::*)())&GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::GetDayID)
                },
                _hook_record {
                    (LPVOID)0x1403dc680L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetSLID20_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetSLID20_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupGetSLID20_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::*)(unsigned int, unsigned int*))&GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::GetSLID)
                },
                _hook_record {
                    (LPVOID)0x1403dbb90L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupInit22_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupInit22_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupInit22_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::*)(unsigned int, unsigned int))&GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Init)
                },
                _hook_record {
                    (LPVOID)0x1403ded60L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsDone24_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsDone24_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsDone24_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::*)())&GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::IsDone)
                },
                _hook_record {
                    (LPVOID)0x1403dc3c0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsEmptyTime26_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsEmptyTime26_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupIsEmptyTime26_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::*)(unsigned int, unsigned int, unsigned int))&GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::IsEmptyTime)
                },
                _hook_record {
                    (LPVOID)0x1403dbdc0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoad28_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoad28_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoad28_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::*)(bool))&GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Load)
                },
                _hook_record {
                    (LPVOID)0x1403dc0d0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoop30_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoop30_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupLoop30_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::*)())&GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Loop)
                },
                _hook_record {
                    (LPVOID)0x1403dc2e0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupPushDQSClear32_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupPushDQSClear32_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupPushDQSClear32_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::*)())&GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::PushDQSClear)
                },
                _hook_record {
                    (LPVOID)0x1403dc560L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupUpdateUseFlag34_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupUpdateUseFlag34_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupUpdateUseFlag34_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CGuildBattleSchedule*(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::*)(unsigned int, unsigned int))&GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::UpdateUseFlag)
                },
                _hook_record {
                    (LPVOID)0x1403dba90L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupdtor_CGuildBattleReservedScheduleMapGroup38_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupdtor_CGuildBattleReservedScheduleMapGroup38_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleMapGroupdtor_CGuildBattleReservedScheduleMapGroup38_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::*)())&GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::dtor_CGuildBattleReservedScheduleMapGroup)
                },
            };
        }; // end namespace Detail
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE
