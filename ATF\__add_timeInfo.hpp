// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <__add_time.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using __add_timector___add_time2_ptr = void (WINAPIV*)(struct __add_time*);
        using __add_timector___add_time2_clbk = void (WINAPIV*)(struct __add_time*, __add_timector___add_time2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
