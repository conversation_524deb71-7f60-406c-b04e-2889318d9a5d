// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _worlddb_trade_info
    {
        struct __trade_key
        {
            char byState;
            unsigned int dwRegistSerial;
            char byInvenIndex;
            unsigned int dwPrice;
            __int64 tStartTime;
            char bySellTurm;
            unsigned int dwBuyerSerial;
            unsigned int dwTax;
            __int64 tResultTime;
            char wszBuyerName[17];
            char szBuyerAccount[13];
        };
        unsigned int dwCnt;
        __trade_key list[20];
    };
END_ATF_NAMESPACE
