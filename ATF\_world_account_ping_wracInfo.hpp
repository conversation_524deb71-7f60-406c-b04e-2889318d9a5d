// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_world_account_ping_wrac.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using _world_account_ping_wracsize2_ptr = int (WINAPIV*)(struct _world_account_ping_wrac*);
        using _world_account_ping_wracsize2_clbk = int (WINAPIV*)(struct _world_account_ping_wrac*, _world_account_ping_wracsize2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
