// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_STORAGE_POS_INDIV.hpp>


START_ATF_NAMESPACE
    struct _upgrade_item_request_clzo
    {
        _STORAGE_POS_INDIV m_posUpgItem;
        _STORAGE_POS_INDIV m_posTalik;
        _STORAGE_POS_INDIV m_posToolItem;
        char by<PERSON><PERSON><PERSON><PERSON><PERSON>;
        _STORAGE_POS_INDIV m_posUpgJewel[4];
    };
END_ATF_NAMESPACE
