// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace vc_attributes
    {
        namespace helper_attributes
        {
            template<>
            struct v1_nameAttribute
            {
                const char *name;
            };
        }; // end namespace helper_attributes
    }; // end namespace vc_attributes
END_ATF_NAMESPACE
