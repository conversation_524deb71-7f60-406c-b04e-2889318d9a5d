#pragma once

#include "../ATFMod.hpp"

namespace ATFMod {

    // Example modification: Player enhancements
    class PlayerMod : public IGameModification {
    private:
        bool enabled;
        bool godModeEnabled;
        bool unlimitedMoney;
        bool speedHackEnabled;
        float speedMultiplier;

        // Original function pointers (will be set by hooks)
        typedef void (WINAPIV* OriginalAddDalant)(ATF::CPlayer* player, unsigned int amount, bool apply);
        typedef void (WINAPIV* OriginalAddGold)(ATF::CPlayer* player, unsigned int amount, bool apply);
        typedef void (WINAPIV* OriginalAlterExp)(ATF::CPlayer* player, long double exp, bool reward, bool useRecover, bool useAddition);
        typedef void (WINAPIV* OriginalTakeDamage)(ATF::CPlayer* player, int damage, void* attacker);

        static OriginalAddDalant originalAddDalant;
        static OriginalAddGold originalAddGold;
        static OriginalAlterExp originalAlterExp;
        static OriginalTakeDamage originalTakeDamage;

    public:
        PlayerMod();
        virtual ~PlayerMod() = default;

        // IGameModification interface
        bool Initialize() override;
        void Shutdown() override;
        const char* GetName() const override { return "PlayerMod"; }
        Priority GetPriority() const override { return Priority::HIGH; }
        bool IsEnabled() const override { return enabled; }
        void SetEnabled(bool enable) override;

        // Player modification features
        void SetGodMode(bool enable);
        void SetUnlimitedMoney(bool enable);
        void SetSpeedHack(bool enable, float multiplier = 2.0f);
        void AddExperience(long double amount);
        void AddMoney(unsigned int dalant, unsigned int gold);
        void SetLevel(int level);

        // Configuration
        void LoadSettings();
        void SaveSettings();

        // Hook functions (static because they're called by the game)
        static void WINAPIV HookedAddDalant(ATF::CPlayer* player, unsigned int amount, bool apply);
        static void WINAPIV HookedAddGold(ATF::CPlayer* player, unsigned int amount, bool apply);
        static void WINAPIV HookedAlterExp(ATF::CPlayer* player, long double exp, bool reward, bool useRecover, bool useAddition);
        static void WINAPIV HookedTakeDamage(ATF::CPlayer* player, int damage, void* attacker);

    private:
        bool InstallHooks();
        void RemoveHooks();
        ATF::CPlayer* GetLocalPlayer();
    };

    // Guild modification example
    class GuildMod : public IGameModification {
    private:
        bool enabled;
        bool autoAcceptGuildInvites;
        bool guildBattleEnhancements;

    public:
        GuildMod();
        virtual ~GuildMod() = default;

        // IGameModification interface
        bool Initialize() override;
        void Shutdown() override;
        const char* GetName() const override { return "GuildMod"; }
        Priority GetPriority() const override { return Priority::NORMAL; }
        bool IsEnabled() const override { return enabled; }
        void SetEnabled(bool enable) override;

        // Guild features
        void SetAutoAcceptInvites(bool enable);
        void EnableGuildBattleEnhancements(bool enable);
        void AutoJoinGuildBattle();
        void SetGuildTax(float rate);

    private:
        bool InstallHooks();
        void RemoveHooks();
    };

    // Combat modification example
    class CombatMod : public IGameModification {
    private:
        bool enabled;
        bool damageMultiplierEnabled;
        float damageMultiplier;
        bool criticalHitAlways;
        bool skillCooldownRemoval;

        // Original function pointers
        typedef int (WINAPIV* OriginalCalculateDamage)(void* attacker, void* target, int baseDamage);
        typedef bool (WINAPIV* OriginalIsCriticalHit)(void* attacker, void* target);
        typedef int (WINAPIV* OriginalGetSkillCooldown)(void* skill);

        static OriginalCalculateDamage originalCalculateDamage;
        static OriginalIsCriticalHit originalIsCriticalHit;
        static OriginalGetSkillCooldown originalGetSkillCooldown;

    public:
        CombatMod();
        virtual ~CombatMod() = default;

        // IGameModification interface
        bool Initialize() override;
        void Shutdown() override;
        const char* GetName() const override { return "CombatMod"; }
        Priority GetPriority() const override { return Priority::HIGH; }
        bool IsEnabled() const override { return enabled; }
        void SetEnabled(bool enable) override;

        // Combat features
        void SetDamageMultiplier(float multiplier);
        void SetAlwaysCritical(bool enable);
        void SetNoSkillCooldown(bool enable);

        // Hook functions
        static int WINAPIV HookedCalculateDamage(void* attacker, void* target, int baseDamage);
        static bool WINAPIV HookedIsCriticalHit(void* attacker, void* target);
        static int WINAPIV HookedGetSkillCooldown(void* skill);

    private:
        bool InstallHooks();
        void RemoveHooks();
    };

    // Item modification example
    class ItemMod : public IGameModification {
    private:
        bool enabled;
        bool unlimitedDurability;
        bool autoRepair;
        bool itemDropMultiplier;
        float dropMultiplier;

    public:
        ItemMod();
        virtual ~ItemMod() = default;

        // IGameModification interface
        bool Initialize() override;
        void Shutdown() override;
        const char* GetName() const override { return "ItemMod"; }
        Priority GetPriority() const override { return Priority::NORMAL; }
        bool IsEnabled() const override { return enabled; }
        void SetEnabled(bool enable) override;

        // Item features
        void SetUnlimitedDurability(bool enable);
        void SetAutoRepair(bool enable);
        void SetDropMultiplier(float multiplier);
        void RepairAllItems();
        void DuplicateItem(int itemSlot);

    private:
        bool InstallHooks();
        void RemoveHooks();
    };

    // Network modification example
    class NetworkMod : public IGameModification {
    private:
        bool enabled;
        bool packetLogging;
        bool antiKick;
        bool speedHackProtection;

    public:
        NetworkMod();
        virtual ~NetworkMod() = default;

        // IGameModification interface
        bool Initialize() override;
        void Shutdown() override;
        const char* GetName() const override { return "NetworkMod"; }
        Priority GetPriority() const override { return Priority::CRITICAL; }
        bool IsEnabled() const override { return enabled; }
        void SetEnabled(bool enable) override;

        // Network features
        void SetPacketLogging(bool enable);
        void SetAntiKick(bool enable);
        void SetSpeedHackProtection(bool enable);
        void SendCustomPacket(const void* data, size_t size);

    private:
        bool InstallHooks();
        void RemoveHooks();
        void LogPacket(const void* data, size_t size, bool incoming);
    };

    // Utility modification for common game functions
    class UtilityMod : public IGameModification {
    private:
        bool enabled;
        bool autoLogin;
        bool autoReconnect;
        bool chatCommands;

    public:
        UtilityMod();
        virtual ~UtilityMod() = default;

        // IGameModification interface
        bool Initialize() override;
        void Shutdown() override;
        const char* GetName() const override { return "UtilityMod"; }
        Priority GetPriority() const override { return Priority::LOW; }
        bool IsEnabled() const override { return enabled; }
        void SetEnabled(bool enable) override;

        // Utility features
        void SetAutoLogin(bool enable, const std::string& username = "", const std::string& password = "");
        void SetAutoReconnect(bool enable);
        void EnableChatCommands(bool enable);
        void ExecuteChatCommand(const std::string& command);
        void TeleportToLocation(float x, float y, float z);
        void TeleportToPlayer(const std::string& playerName);

    private:
        bool InstallHooks();
        void RemoveHooks();
        void ProcessChatCommand(const std::string& message);
        
        std::string autoLoginUsername;
        std::string autoLoginPassword;
    };

} // namespace ATFMod
