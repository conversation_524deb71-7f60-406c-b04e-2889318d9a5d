// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <__add_loot_item.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using __add_loot_itemctor___add_loot_item2_ptr = void (WINAPIV*)(struct __add_loot_item*);
        using __add_loot_itemctor___add_loot_item2_clbk = void (WINAPIV*)(struct __add_loot_item*, __add_loot_itemctor___add_loot_item2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
