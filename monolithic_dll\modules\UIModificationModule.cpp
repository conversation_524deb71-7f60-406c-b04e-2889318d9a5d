#include "UIModificationModule.hpp"
#include <algorithm>
#include <locale>
#include <codecvt>

namespace NexusPro {

    // Static member definitions
    UIModificationModule::OriginalCreateWindowExA UIModificationModule::originalCreateWindowExA = nullptr;
    UIModificationModule::OriginalCreateWindowExW UIModificationModule::originalCreateWindowExW = nullptr;
    UIModificationModule::OriginalSetWindowTextA UIModificationModule::originalSetWindowTextA = nullptr;
    UIModificationModule::OriginalSetWindowTextW UIModificationModule::originalSetWindowTextW = nullptr;
    UIModificationModule::OriginalMessageBoxA UIModificationModule::originalMessageBoxA = nullptr;
    UIModificationModule::OriginalMessageBoxW UIModificationModule::originalMessageBoxW = nullptr;

    LoadingDialogInterceptor* LoadingDialogInterceptor::instance = nullptr;

    UIModificationModule::UIModificationModule() : enabled(false) {
        // Set up default text replacements for RF Online
        SetupDefaultReplacements();
    }

    void UIModificationModule::SetupDefaultReplacements() {
        // Set up default text replacements for RF Online
        AddTextReplacement("RF SERVER Now Loading", "NexusProtection", UIElementType::LOADING_DIALOG);
        AddTextReplacement("RF Online", "NexusProtection", UIElementType::WINDOW_TITLE);
        AddTextReplacement("Loading...", "NexusProtection Loading...", UIElementType::LOADING_DIALOG);
    }

    UIModificationModule::~UIModificationModule() {
        Shutdown();
    }

    bool UIModificationModule::Initialize() {
        NEXUS_LOG_INFO("Initializing UI Modification Module...");
        
        if (!InstallHooks()) {
            NEXUS_LOG_ERROR("Failed to install UI modification hooks");
            return false;
        }
        
        // Initialize the loading dialog interceptor
        LoadingDialogInterceptor::GetInstance()->EnableInterception(true);
        LoadingDialogInterceptor::GetInstance()->SetNexusProtectionBranding();
        
        enabled = true;
        NEXUS_LOG_INFO("UI Modification Module initialized successfully");
        return true;
    }

    void UIModificationModule::Shutdown() {
        if (!enabled) return;
        
        NEXUS_LOG_INFO("Shutting down UI Modification Module...");
        
        RemoveHooks();
        LoadingDialogInterceptor::GetInstance()->EnableInterception(false);
        
        enabled = false;
        NEXUS_LOG_INFO("UI Modification Module shutdown complete");
    }

    void UIModificationModule::SetEnabled(bool enable) {
        if (enable == enabled) return;

        if (enable) {
            if (!InstallHooks()) {
                NEXUS_LOG_ERROR("Failed to enable UI modification hooks");
                return;
            }
        } else {
            RemoveHooks();
        }

        enabled = enable;
        NEXUS_LOG_INFO("UI Modification Module %s", enable ? "enabled" : "disabled");
    }

    void UIModificationModule::AddTextReplacement(const std::string& original, const std::string& replacement,
                                                 UIElementType type, bool caseSensitive, bool exactMatch) {
        TextReplacement textRepl;
        textRepl.originalText = original;
        textRepl.replacementText = replacement;
        textRepl.elementType = type;
        textRepl.caseSensitive = caseSensitive;
        textRepl.exactMatch = exactMatch;

        textReplacements.push_back(textRepl);
        NEXUS_LOG_INFO("Added text replacement: '%s' -> '%s'", original.c_str(), replacement.c_str());
    }

    void UIModificationModule::SetLoadingDialogText(const std::string& newText) {
        LoadingDialogInterceptor::GetInstance()->SetCustomLoadingText(newText);
        NEXUS_LOG_INFO("Loading dialog text set to: %s", newText.c_str());
    }

    void UIModificationModule::SetServerLoadingText(const std::string& newText) {
        LoadingDialogInterceptor::GetInstance()->SetCustomServerText(newText);
        NEXUS_LOG_INFO("Server loading text set to: %s", newText.c_str());
    }

    void UIModificationModule::EnableNexusProtectionBranding() {
        // Set up NexusProtection branding
        AddTextReplacement("RF SERVER Now Loading", "NexusProtection", UIElementType::LOADING_DIALOG);
        AddTextReplacement("RF Online", "NexusProtection", UIElementType::WINDOW_TITLE);
        AddTextReplacement("Loading...", "NexusProtection Loading...", UIElementType::LOADING_DIALOG);
        AddTextReplacement("Connecting to server", "NexusProtection - Connecting", UIElementType::LOADING_DIALOG);
        AddTextReplacement("Please wait", "NexusProtection - Please wait", UIElementType::LOADING_DIALOG);
        
        NEXUS_LOG_INFO("NexusProtection branding enabled");
    }

    bool UIModificationModule::InstallHooks() {
        auto* hookManager = NexusProModManager::GetInstance()->GetHookManager();
        
        // Hook CreateWindowExA
        HMODULE user32 = GetModuleHandleA("user32.dll");
        if (!user32) {
            NEXUS_LOG_ERROR("Failed to get user32.dll handle");
            return false;
        }
        
        void* createWindowExAAddr = GetProcAddress(user32, "CreateWindowExA");
        if (!hookManager->InstallHook("UI_CreateWindowExA", createWindowExAAddr, 
                                     reinterpret_cast<void*>(HookedCreateWindowExA))) {
            NEXUS_LOG_ERROR("Failed to hook CreateWindowExA");
            return false;
        }
        originalCreateWindowExA = hookManager->GetOriginalFunction<OriginalCreateWindowExA>("UI_CreateWindowExA");
        
        // Hook CreateWindowExW
        void* createWindowExWAddr = GetProcAddress(user32, "CreateWindowExW");
        if (!hookManager->InstallHook("UI_CreateWindowExW", createWindowExWAddr, 
                                     reinterpret_cast<void*>(HookedCreateWindowExW))) {
            NEXUS_LOG_ERROR("Failed to hook CreateWindowExW");
            return false;
        }
        originalCreateWindowExW = hookManager->GetOriginalFunction<OriginalCreateWindowExW>("UI_CreateWindowExW");
        
        // Hook SetWindowTextA
        void* setWindowTextAAddr = GetProcAddress(user32, "SetWindowTextA");
        if (!hookManager->InstallHook("UI_SetWindowTextA", setWindowTextAAddr, 
                                     reinterpret_cast<void*>(HookedSetWindowTextA))) {
            NEXUS_LOG_ERROR("Failed to hook SetWindowTextA");
            return false;
        }
        originalSetWindowTextA = hookManager->GetOriginalFunction<OriginalSetWindowTextA>("UI_SetWindowTextA");
        
        // Hook SetWindowTextW
        void* setWindowTextWAddr = GetProcAddress(user32, "SetWindowTextW");
        if (!hookManager->InstallHook("UI_SetWindowTextW", setWindowTextWAddr, 
                                     reinterpret_cast<void*>(HookedSetWindowTextW))) {
            NEXUS_LOG_ERROR("Failed to hook SetWindowTextW");
            return false;
        }
        originalSetWindowTextW = hookManager->GetOriginalFunction<OriginalSetWindowTextW>("UI_SetWindowTextW");
        
        // Hook MessageBoxA
        void* messageBoxAAddr = GetProcAddress(user32, "MessageBoxA");
        if (!hookManager->InstallHook("UI_MessageBoxA", messageBoxAAddr, 
                                     reinterpret_cast<void*>(HookedMessageBoxA))) {
            NEXUS_LOG_ERROR("Failed to hook MessageBoxA");
            return false;
        }
        originalMessageBoxA = hookManager->GetOriginalFunction<OriginalMessageBoxA>("UI_MessageBoxA");
        
        // Enable all hooks
        hookManager->EnableHook("UI_CreateWindowExA");
        hookManager->EnableHook("UI_CreateWindowExW");
        hookManager->EnableHook("UI_SetWindowTextA");
        hookManager->EnableHook("UI_SetWindowTextW");
        hookManager->EnableHook("UI_MessageBoxA");
        
        NEXUS_LOG_INFO("UI modification hooks installed successfully");
        return true;
    }

    void UIModificationModule::RemoveHooks() {
        auto* hookManager = NexusProModManager::GetInstance()->GetHookManager();
        
        hookManager->RemoveHook("UI_CreateWindowExA");
        hookManager->RemoveHook("UI_CreateWindowExW");
        hookManager->RemoveHook("UI_SetWindowTextA");
        hookManager->RemoveHook("UI_SetWindowTextW");
        hookManager->RemoveHook("UI_MessageBoxA");
        
        originalCreateWindowExA = nullptr;
        originalCreateWindowExW = nullptr;
        originalSetWindowTextA = nullptr;
        originalSetWindowTextW = nullptr;
        originalMessageBoxA = nullptr;
        
        NEXUS_LOG_INFO("UI modification hooks removed");
    }

    // Hook function implementations
    HWND WINAPI UIModificationModule::HookedCreateWindowExA(
        DWORD dwExStyle, LPCSTR lpClassName, LPCSTR lpWindowName,
        DWORD dwStyle, int X, int Y, int nWidth, int nHeight,
        HWND hWndParent, HMENU hMenu, HINSTANCE hInstance, LPVOID lpParam) {
        
        std::string windowName = lpWindowName ? lpWindowName : "";
        std::string processedName = LoadingDialogInterceptor::GetInstance()->ProcessLoadingText(windowName);
        
        // Log the window creation for debugging
        NEXUS_LOG_DEBUG("CreateWindowExA: Original='%s', Processed='%s'", 
                       windowName.c_str(), processedName.c_str());
        
        // Call original function with modified text
        return originalCreateWindowExA(dwExStyle, lpClassName, 
                                      processedName.empty() ? lpWindowName : processedName.c_str(),
                                      dwStyle, X, Y, nWidth, nHeight, 
                                      hWndParent, hMenu, hInstance, lpParam);
    }

    HWND WINAPI UIModificationModule::HookedCreateWindowExW(
        DWORD dwExStyle, LPCWSTR lpClassName, LPCWSTR lpWindowName,
        DWORD dwStyle, int X, int Y, int nWidth, int nHeight,
        HWND hWndParent, HMENU hMenu, HINSTANCE hInstance, LPVOID lpParam) {
        
        std::wstring windowName = lpWindowName ? lpWindowName : L"";
        std::wstring processedName = LoadingDialogInterceptor::GetInstance()->ProcessLoadingText(windowName);
        
        // Call original function with modified text
        return originalCreateWindowExW(dwExStyle, lpClassName, 
                                      processedName.empty() ? lpWindowName : processedName.c_str(),
                                      dwStyle, X, Y, nWidth, nHeight, 
                                      hWndParent, hMenu, hInstance, lpParam);
    }

    BOOL WINAPI UIModificationModule::HookedSetWindowTextA(HWND hWnd, LPCSTR lpString) {
        std::string text = lpString ? lpString : "";
        std::string processedText = LoadingDialogInterceptor::GetInstance()->ProcessLoadingText(text);
        
        // Log the text change for debugging
        NEXUS_LOG_DEBUG("SetWindowTextA: Original='%s', Processed='%s'", 
                       text.c_str(), processedText.c_str());
        
        return originalSetWindowTextA(hWnd, processedText.empty() ? lpString : processedText.c_str());
    }

    BOOL WINAPI UIModificationModule::HookedSetWindowTextW(HWND hWnd, LPCWSTR lpString) {
        std::wstring text = lpString ? lpString : L"";
        std::wstring processedText = LoadingDialogInterceptor::GetInstance()->ProcessLoadingText(text);
        
        return originalSetWindowTextW(hWnd, processedText.empty() ? lpString : processedText.c_str());
    }

    int WINAPI UIModificationModule::HookedMessageBoxA(
        HWND hWnd, LPCSTR lpText, LPCSTR lpCaption, UINT uType) {
        
        std::string text = lpText ? lpText : "";
        std::string caption = lpCaption ? lpCaption : "";
        
        // Process both text and caption
        std::string processedText = LoadingDialogInterceptor::GetInstance()->ProcessLoadingText(text);
        std::string processedCaption = LoadingDialogInterceptor::GetInstance()->ProcessLoadingText(caption);
        
        return originalMessageBoxA(hWnd, 
                                  processedText.empty() ? lpText : processedText.c_str(),
                                  processedCaption.empty() ? lpCaption : processedCaption.c_str(),
                                  uType);
    }

    // LoadingDialogInterceptor implementation
    LoadingDialogInterceptor* LoadingDialogInterceptor::GetInstance() {
        if (!instance) {
            instance = new LoadingDialogInterceptor();
        }
        return instance;
    }

    LoadingDialogInterceptor::LoadingDialogInterceptor() 
        : customLoadingText("NexusProtection"), customServerText("NexusProtection"), interceptEnabled(false) {
        SetupRFOnlineReplacements();
    }

    void LoadingDialogInterceptor::SetNexusProtectionBranding() {
        customLoadingText = "NexusProtection";
        customServerText = "NexusProtection";
        
        NEXUS_LOG_INFO("NexusProtection branding applied to loading dialogs");
    }

    std::string LoadingDialogInterceptor::ProcessLoadingText(const std::string& originalText) {
        if (!interceptEnabled || originalText.empty()) {
            return originalText;
        }
        
        std::string result = originalText;
        
        // RF Online specific replacements
        if (result.find("RF SERVER Now Loading") != std::string::npos) {
            result = customServerText;
            NEXUS_LOG_DEBUG("Replaced 'RF SERVER Now Loading' with '%s'", result.c_str());
        }
        else if (result.find("RF Online") != std::string::npos) {
            std::replace(result.begin(), result.end(), 'R', 'N');
            std::replace(result.begin(), result.end(), 'F', 'e');
            result = "NexusProtection";
            NEXUS_LOG_DEBUG("Replaced 'RF Online' with '%s'", result.c_str());
        }
        else if (result.find("Loading") != std::string::npos) {
            result = customLoadingText + " Loading...";
            NEXUS_LOG_DEBUG("Replaced loading text with '%s'", result.c_str());
        }
        else if (result.find("Connecting") != std::string::npos) {
            result = customServerText + " - Connecting...";
            NEXUS_LOG_DEBUG("Replaced connecting text with '%s'", result.c_str());
        }
        
        return result;
    }

    std::wstring LoadingDialogInterceptor::ProcessLoadingText(const std::wstring& originalText) {
        // Convert to string, process, then convert back
        std::wstring_convert<std::codecvt_utf8<wchar_t>> converter;
        std::string str = converter.to_bytes(originalText);
        std::string processed = ProcessLoadingText(str);
        return converter.from_bytes(processed);
    }

    void LoadingDialogInterceptor::SetupRFOnlineReplacements() {
        // This will be called during initialization to set up common RF Online text replacements
        interceptEnabled = true;
    }

} // namespace NexusPro
