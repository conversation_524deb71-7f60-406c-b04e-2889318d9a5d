#pragma once

// NexusPro UI Modification Module
// Hooks and modifies game UI elements including loading dialogs
// Compatible with Visual Studio 2022

#include "../ATFMod.hpp"
#include <windows.h>
#include <string>
#include <map>
#include <vector>

namespace NexusPro {

    // UI element types that can be modified
    enum class UIElementType {
        LOADING_DIALOG,
        MESSAGE_BOX,
        WINDOW_TITLE,
        BUTTON_TEXT,
        LABEL_TEXT,
        TOOLTIP_TEXT,
        STATUS_BAR,
        MENU_ITEM
    };

    // Text replacement structure
    struct TextReplacement {
        std::string originalText;
        std::string replacementText;
        UIElementType elementType;
        bool caseSensitive;
        bool exactMatch;
    };

    // UI Modification Module
    class UIModificationModule : public IGameModification {
    private:
        bool enabled;
        std::vector<TextReplacement> textReplacements;
        std::map<std::string, std::string> windowTitleReplacements;
        
        // Original function pointers
        typedef HWND (WINAPI* OriginalCreateWindowExA)(
            DWORD dwExStyle, LPCSTR lpClassName, LPCSTR lpWindowName,
            DWORD dwStyle, int X, int Y, int nWidth, int nHeight,
            HWND hWndParent, HMENU hMenu, HINSTANCE hInstance, LPVOID lpParam);
        
        typedef HWND (WINAPI* OriginalCreateWindowExW)(
            DWORD dwExStyle, LPCWSTR lpClassName, LPCWSTR lpWindowName,
            DWORD dwStyle, int X, int Y, int nWidth, int nHeight,
            HWND hWndParent, HMENU hMenu, HINSTANCE hInstance, LPVOID lpParam);
        
        typedef BOOL (WINAPI* OriginalSetWindowTextA)(HWND hWnd, LPCSTR lpString);
        typedef BOOL (WINAPI* OriginalSetWindowTextW)(HWND hWnd, LPCWSTR lpString);
        
        typedef int (WINAPI* OriginalMessageBoxA)(
            HWND hWnd, LPCSTR lpText, LPCSTR lpCaption, UINT uType);
        
        typedef int (WINAPI* OriginalMessageBoxW)(
            HWND hWnd, LPCWSTR lpText, LPCWSTR lpCaption, UINT uType);
        
        static OriginalCreateWindowExA originalCreateWindowExA;
        static OriginalCreateWindowExW originalCreateWindowExW;
        static OriginalSetWindowTextA originalSetWindowTextA;
        static OriginalSetWindowTextW originalSetWindowTextW;
        static OriginalMessageBoxA originalMessageBoxA;
        static OriginalMessageBoxW originalMessageBoxW;

    public:
        UIModificationModule();
        virtual ~UIModificationModule();

        // IGameModification interface
        bool Initialize() override;
        void Shutdown() override;
        const char* GetName() const override { return "UIModificationModule"; }
        Priority GetPriority() const override { return Priority::NORMAL; }
        bool IsEnabled() const override { return enabled; }
        void SetEnabled(bool enable) override;

        // Text replacement management
        void AddTextReplacement(const std::string& original, const std::string& replacement,
                               UIElementType type = UIElementType::LOADING_DIALOG,
                               bool caseSensitive = false, bool exactMatch = true);
        void RemoveTextReplacement(const std::string& original);
        void ClearTextReplacements();
        
        // Specific UI modifications
        void SetLoadingDialogText(const std::string& newText);
        void SetServerLoadingText(const std::string& newText);
        void SetWindowTitle(const std::string& originalTitle, const std::string& newTitle);
        void SetMessageBoxText(const std::string& originalText, const std::string& newText);
        
        // Predefined modifications for RF Online
        void EnableNexusProtectionBranding();
        void SetCustomLoadingMessages();
        void ModifyServerConnectionDialogs();
        
        // Hook functions (static because they're called by Windows API)
        static HWND WINAPI HookedCreateWindowExA(
            DWORD dwExStyle, LPCSTR lpClassName, LPCSTR lpWindowName,
            DWORD dwStyle, int X, int Y, int nWidth, int nHeight,
            HWND hWndParent, HMENU hMenu, HINSTANCE hInstance, LPVOID lpParam);
        
        static HWND WINAPI HookedCreateWindowExW(
            DWORD dwExStyle, LPCWSTR lpClassName, LPCWSTR lpWindowName,
            DWORD dwStyle, int X, int Y, int nWidth, int nHeight,
            HWND hWndParent, HMENU hMenu, HINSTANCE hInstance, LPVOID lpParam);
        
        static BOOL WINAPI HookedSetWindowTextA(HWND hWnd, LPCSTR lpString);
        static BOOL WINAPI HookedSetWindowTextW(HWND hWnd, LPCWSTR lpString);
        
        static int WINAPI HookedMessageBoxA(
            HWND hWnd, LPCSTR lpText, LPCSTR lpCaption, UINT uType);
        
        static int WINAPI HookedMessageBoxW(
            HWND hWnd, LPCWSTR lpText, LPCWSTR lpCaption, UINT uType);

    private:
        bool InstallHooks();
        void RemoveHooks();
        
        // Text processing helpers
        std::string ProcessTextReplacement(const std::string& originalText, UIElementType type);
        std::wstring ProcessTextReplacement(const std::wstring& originalText, UIElementType type);
        bool ShouldReplaceText(const std::string& text, const TextReplacement& replacement);
        
        // Window analysis helpers
        bool IsLoadingDialog(HWND hWnd, const std::string& windowText);
        bool IsServerDialog(HWND hWnd, const std::string& windowText);
        UIElementType DetermineElementType(HWND hWnd, const std::string& className);
        
        // Utility functions
        std::string WStringToString(const std::wstring& wstr);
        std::wstring StringToWString(const std::string& str);
    };

    // Loading Dialog Interceptor - Specialized for RF Online loading screens
    class LoadingDialogInterceptor {
    private:
        static LoadingDialogInterceptor* instance;
        std::string customLoadingText;
        std::string customServerText;
        bool interceptEnabled;

    public:
        static LoadingDialogInterceptor* GetInstance();
        
        LoadingDialogInterceptor();
        ~LoadingDialogInterceptor();
        
        // Configuration
        void SetCustomLoadingText(const std::string& text);
        void SetCustomServerText(const std::string& text);
        void EnableInterception(bool enable);
        
        // Text processing
        std::string ProcessLoadingText(const std::string& originalText);
        std::wstring ProcessLoadingText(const std::wstring& originalText);
        
        // Predefined replacements for RF Online
        void SetupRFOnlineReplacements();
        void SetNexusProtectionBranding();
    };

    // Message Box Customizer
    class MessageBoxCustomizer {
    private:
        std::map<std::string, std::string> titleReplacements;
        std::map<std::string, std::string> messageReplacements;
        bool customizationEnabled;

    public:
        MessageBoxCustomizer();
        ~MessageBoxCustomizer();
        
        void EnableCustomization(bool enable);
        void AddTitleReplacement(const std::string& original, const std::string& replacement);
        void AddMessageReplacement(const std::string& original, const std::string& replacement);
        
        std::string ProcessTitle(const std::string& originalTitle);
        std::string ProcessMessage(const std::string& originalMessage);
        
        // Predefined customizations
        void SetupNexusProtectionMessages();
        void SetupCustomErrorMessages();
    };

    // Window Title Manager
    class WindowTitleManager {
    private:
        std::map<HWND, std::string> originalTitles;
        std::map<std::string, std::string> titleMappings;
        bool titleManagementEnabled;

    public:
        WindowTitleManager();
        ~WindowTitleManager();
        
        void EnableTitleManagement(bool enable);
        void AddTitleMapping(const std::string& original, const std::string& replacement);
        void RestoreOriginalTitle(HWND hWnd);
        void RestoreAllTitles();
        
        std::string ProcessWindowTitle(HWND hWnd, const std::string& originalTitle);
        
        // Specific for RF Online
        void SetupRFOnlineTitleMappings();
        void BrandWithNexusProtection();
    };

} // namespace NexusPro
