#include <windows.h>
#include <tlhelp32.h>
#include <iostream>
#include <string>
#include <vector>

class DLLInjector {
private:
    DWORD processId;
    std::string processName;
    std::string dllPath;

public:
    DLLInjector() : processId(0) {}

    // Find process by name
    bool FindProcess(const std::string& name) {
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE) {
            std::cerr << "Failed to create process snapshot" << std::endl;
            return false;
        }

        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (!Process32First(hSnapshot, &pe32)) {
            std::cerr << "Failed to get first process" << std::endl;
            CloseHandle(hSnapshot);
            return false;
        }

        do {
            std::string currentProcess(pe32.szExeFile);
            if (currentProcess.find(name) != std::string::npos) {
                processId = pe32.th32ProcessID;
                processName = currentProcess;
                CloseHandle(hSnapshot);
                std::cout << "Found process: " << processName << " (PID: " << processId << ")" << std::endl;
                return true;
            }
        } while (Process32Next(hSnapshot, &pe32));

        CloseHandle(hSnapshot);
        std::cerr << "Process '" << name << "' not found" << std::endl;
        return false;
    }

    // List all running processes
    void ListProcesses() {
        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (hSnapshot == INVALID_HANDLE_VALUE) {
            std::cerr << "Failed to create process snapshot" << std::endl;
            return;
        }

        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        std::cout << "\n=== Running Processes ===" << std::endl;
        std::cout << "PID\t\tProcess Name" << std::endl;
        std::cout << "---\t\t------------" << std::endl;

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::cout << pe32.th32ProcessID << "\t\t" << pe32.szExeFile << std::endl;
            } while (Process32Next(hSnapshot, &pe32));
        }

        CloseHandle(hSnapshot);
    }

    // Inject DLL into target process
    bool InjectDLL(const std::string& dllPath) {
        if (processId == 0) {
            std::cerr << "No target process selected" << std::endl;
            return false;
        }

        // Check if DLL file exists
        DWORD fileAttributes = GetFileAttributesA(dllPath.c_str());
        if (fileAttributes == INVALID_FILE_ATTRIBUTES) {
            std::cerr << "DLL file not found: " << dllPath << std::endl;
            return false;
        }

        // Open target process
        HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        if (!hProcess) {
            std::cerr << "Failed to open process (PID: " << processId << "). Error: " << GetLastError() << std::endl;
            std::cerr << "Make sure you're running as administrator" << std::endl;
            return false;
        }

        // Get LoadLibraryA address
        HMODULE hKernel32 = GetModuleHandleA("kernel32.dll");
        if (!hKernel32) {
            std::cerr << "Failed to get kernel32.dll handle" << std::endl;
            CloseHandle(hProcess);
            return false;
        }

        LPVOID pLoadLibraryA = GetProcAddress(hKernel32, "LoadLibraryA");
        if (!pLoadLibraryA) {
            std::cerr << "Failed to get LoadLibraryA address" << std::endl;
            CloseHandle(hProcess);
            return false;
        }

        // Allocate memory in target process
        SIZE_T dllPathSize = dllPath.length() + 1;
        LPVOID pDllPath = VirtualAllocEx(hProcess, NULL, dllPathSize, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
        if (!pDllPath) {
            std::cerr << "Failed to allocate memory in target process. Error: " << GetLastError() << std::endl;
            CloseHandle(hProcess);
            return false;
        }

        // Write DLL path to target process memory
        SIZE_T bytesWritten;
        if (!WriteProcessMemory(hProcess, pDllPath, dllPath.c_str(), dllPathSize, &bytesWritten)) {
            std::cerr << "Failed to write DLL path to target process. Error: " << GetLastError() << std::endl;
            VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }

        // Create remote thread to load DLL
        HANDLE hThread = CreateRemoteThread(hProcess, NULL, 0, 
                                          (LPTHREAD_START_ROUTINE)pLoadLibraryA, 
                                          pDllPath, 0, NULL);
        if (!hThread) {
            std::cerr << "Failed to create remote thread. Error: " << GetLastError() << std::endl;
            VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
            CloseHandle(hProcess);
            return false;
        }

        // Wait for thread to complete
        std::cout << "Injecting DLL..." << std::endl;
        DWORD waitResult = WaitForSingleObject(hThread, 5000); // 5 second timeout
        
        if (waitResult == WAIT_TIMEOUT) {
            std::cerr << "DLL injection timed out" << std::endl;
            TerminateThread(hThread, 0);
            VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
            CloseHandle(hThread);
            CloseHandle(hProcess);
            return false;
        }

        // Get thread exit code (LoadLibrary return value)
        DWORD exitCode;
        if (GetExitCodeThread(hThread, &exitCode)) {
            if (exitCode == 0) {
                std::cerr << "LoadLibrary failed in target process" << std::endl;
                VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
                CloseHandle(hThread);
                CloseHandle(hProcess);
                return false;
            }
        }

        // Cleanup
        VirtualFreeEx(hProcess, pDllPath, 0, MEM_RELEASE);
        CloseHandle(hThread);
        CloseHandle(hProcess);

        std::cout << "DLL injected successfully!" << std::endl;
        std::cout << "Module handle: 0x" << std::hex << exitCode << std::dec << std::endl;
        return true;
    }

    // Check if DLL is already loaded in target process
    bool IsDLLLoaded(const std::string& dllName) {
        if (processId == 0) return false;

        HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPMODULE | TH32CS_SNAPMODULE32, processId);
        if (hSnapshot == INVALID_HANDLE_VALUE) {
            return false;
        }

        MODULEENTRY32 me32;
        me32.dwSize = sizeof(MODULEENTRY32);

        if (Module32First(hSnapshot, &me32)) {
            do {
                std::string moduleName(me32.szModule);
                if (moduleName.find(dllName) != std::string::npos) {
                    CloseHandle(hSnapshot);
                    return true;
                }
            } while (Module32Next(hSnapshot, &me32));
        }

        CloseHandle(hSnapshot);
        return false;
    }
};

void PrintUsage() {
    std::cout << "\n=== ATF DLL Injector ===" << std::endl;
    std::cout << "Usage:" << std::endl;
    std::cout << "  ATFInjector.exe <process_name> <dll_path>" << std::endl;
    std::cout << "  ATFInjector.exe -list" << std::endl;
    std::cout << "\nExamples:" << std::endl;
    std::cout << "  ATFInjector.exe game.exe ATFMod.dll" << std::endl;
    std::cout << "  ATFInjector.exe RF_Online.exe C:\\Mods\\ATFMod.dll" << std::endl;
    std::cout << "  ATFInjector.exe -list" << std::endl;
    std::cout << "\nNote: Run as administrator for best results" << std::endl;
}

int main(int argc, char* argv[]) {
    std::cout << "ATF DLL Injector v1.0" << std::endl;
    std::cout << "=====================" << std::endl;

    if (argc < 2) {
        PrintUsage();
        return 1;
    }

    DLLInjector injector;

    // Handle list command
    if (std::string(argv[1]) == "-list") {
        injector.ListProcesses();
        return 0;
    }

    if (argc < 3) {
        PrintUsage();
        return 1;
    }

    std::string processName = argv[1];
    std::string dllPath = argv[2];

    // Convert relative path to absolute path
    char absolutePath[MAX_PATH];
    if (GetFullPathNameA(dllPath.c_str(), MAX_PATH, absolutePath, NULL)) {
        dllPath = absolutePath;
    }

    std::cout << "Target process: " << processName << std::endl;
    std::cout << "DLL path: " << dllPath << std::endl;
    std::cout << std::endl;

    // Find target process
    if (!injector.FindProcess(processName)) {
        std::cout << "\nAvailable processes:" << std::endl;
        injector.ListProcesses();
        return 1;
    }

    // Check if DLL is already loaded
    if (injector.IsDLLLoaded("ATFMod.dll")) {
        std::cout << "ATFMod.dll is already loaded in the target process" << std::endl;
        return 0;
    }

    // Inject DLL
    if (injector.InjectDLL(dllPath)) {
        std::cout << "\nInjection completed successfully!" << std::endl;
        std::cout << "The ATF modification DLL is now active in the target process." << std::endl;
        return 0;
    } else {
        std::cerr << "\nInjection failed!" << std::endl;
        return 1;
    }
}
