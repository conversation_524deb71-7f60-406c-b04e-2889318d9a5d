// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _worlddb_ore_cutting
    {
        struct _ore_cutting_info
        {
            int nKey;
            unsigned int dwDur;
        };
        _ore_cutting_info List[20];
    };    
    static_assert(ATF::checkSize<_worlddb_ore_cutting, 160>(), "_worlddb_ore_cutting");
END_ATF_NAMESPACE
