// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <__error_info.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        using __error_infoSetFileName2_ptr = void (WINAPIV*)(struct __error_info*, char*);
        using __error_infoSetFileName2_clbk = void (WINAPIV*)(struct __error_info*, char*, __error_infoSetFileName2_ptr);
        using __error_infoSetQuestTitle4_ptr = void (WINAPIV*)(struct __error_info*, char*);
        using __error_infoSetQuestTitle4_clbk = void (WINAPIV*)(struct __error_info*, char*, __error_infoSetQuestTitle4_ptr);
        
        using __error_infoctor___error_info6_ptr = void (WINAPIV*)(struct __error_info*);
        using __error_infoctor___error_info6_clbk = void (WINAPIV*)(struct __error_info*, __error_infoctor___error_info6_ptr);
        using __error_infoinit8_ptr = void (WINAPIV*)(struct __error_info*);
        using __error_infoinit8_clbk = void (WINAPIV*)(struct __error_info*, __error_infoinit8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
