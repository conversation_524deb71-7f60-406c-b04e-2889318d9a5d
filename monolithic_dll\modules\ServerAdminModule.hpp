#pragma once

// NexusPro Server Administration Module
// Comprehensive server management and monitoring capabilities
// Compatible with Visual Studio 2022

#include "../ATFMod.hpp"
#include <vector>
#include <map>
#include <string>
#include <chrono>
#include <thread>
#include <atomic>
#include <queue>

namespace NexusPro {

    // Server status enumeration
    enum class ServerStatus {
        OFFLINE,
        STARTING,
        ONLINE,
        MAINTENANCE,
        OVERLOADED,
        ERROR,
        SHUTTING_DOWN
    };

    // Player management actions
    enum class PlayerAction {
        KICK,
        BAN,
        MUTE,
        TELEPORT,
        GIVE_ITEM,
        SET_LEVEL,
        HEAL,
        KILL,
        FREEZE,
        UNFREEZE
    };

    // Server monitoring metrics
    struct ServerMetrics {
        uint32_t playerCount;
        uint32_t maxPlayers;
        float cpuUsage;
        float memoryUsage;
        float networkBandwidth;
        uint32_t activeConnections;
        uint64_t totalPacketsProcessed;
        uint64_t totalBytesTransferred;
        std::chrono::steady_clock::time_point uptime;
        float averageLatency;
        uint32_t errorCount;
    };

    // Player information structure
    struct PlayerInfo {
        std::string username;
        std::string characterName;
        uint32_t playerId;
        uint32_t level;
        std::string ipAddress;
        std::chrono::steady_clock::time_point loginTime;
        std::chrono::steady_clock::time_point lastActivity;
        bool isOnline;
        bool isMuted;
        bool isBanned;
        std::string location;
        uint32_t guildId;
    };

    // Server Administration Module
    class ServerAdministrationModule {
    private:
        ServerStatus currentStatus;
        ServerMetrics metrics;
        std::map<uint32_t, PlayerInfo> players;
        std::vector<std::string> bannedIPs;
        std::vector<std::string> adminUsers;
        std::mutex adminMutex;
        bool maintenanceMode;

    public:
        ServerAdministrationModule();
        ~ServerAdministrationModule();

        // Server control
        bool StartServer();
        bool StopServer();
        bool RestartServer();
        void SetMaintenanceMode(bool enable);
        void EmergencyShutdown();
        
        // Server status
        ServerStatus GetServerStatus() const { return currentStatus; }
        ServerMetrics GetServerMetrics() const { return metrics; }
        void UpdateMetrics();
        
        // Player management
        bool KickPlayer(uint32_t playerId, const std::string& reason = "");
        bool BanPlayer(uint32_t playerId, const std::string& reason = "", 
                      std::chrono::hours duration = std::chrono::hours(24));
        bool UnbanPlayer(uint32_t playerId);
        bool MutePlayer(uint32_t playerId, std::chrono::minutes duration);
        bool UnmutePlayer(uint32_t playerId);
        
        // Player information
        std::vector<PlayerInfo> GetOnlinePlayers();
        PlayerInfo GetPlayerInfo(uint32_t playerId);
        bool IsPlayerOnline(uint32_t playerId);
        uint32_t GetPlayerCount() const;
        
        // Admin commands
        bool TeleportPlayer(uint32_t playerId, float x, float y, float z);
        bool GiveItemToPlayer(uint32_t playerId, uint32_t itemId, uint32_t quantity);
        bool SetPlayerLevel(uint32_t playerId, uint32_t level);
        bool HealPlayer(uint32_t playerId);
        bool KillPlayer(uint32_t playerId);
        
        // Broadcast messages
        void BroadcastMessage(const std::string& message);
        void SendMessageToPlayer(uint32_t playerId, const std::string& message);
        void SendAdminNotification(const std::string& message);
        
        // Admin management
        bool AddAdmin(const std::string& username);
        bool RemoveAdmin(const std::string& username);
        bool IsAdmin(const std::string& username);
        std::vector<std::string> GetAdminList();
    };

    // Performance Monitoring Module
    class PerformanceMonitoringModule {
    private:
        std::atomic<bool> monitoring;
        std::thread monitoringThread;
        std::queue<ServerMetrics> metricsHistory;
        std::chrono::milliseconds updateInterval;
        
        // Performance thresholds
        float cpuThreshold;
        float memoryThreshold;
        float latencyThreshold;
        uint32_t errorThreshold;

    public:
        PerformanceMonitoringModule();
        ~PerformanceMonitoringModule();

        // Monitoring control
        void StartMonitoring();
        void StopMonitoring();
        void SetUpdateInterval(std::chrono::milliseconds interval);
        
        // Performance metrics
        float GetCPUUsage();
        float GetMemoryUsage();
        float GetNetworkBandwidth();
        float GetAverageLatency();
        uint32_t GetErrorRate();
        
        // Threshold management
        void SetCPUThreshold(float threshold);
        void SetMemoryThreshold(float threshold);
        void SetLatencyThreshold(float threshold);
        void SetErrorThreshold(uint32_t threshold);
        
        // Alerts
        bool IsCPUOverloaded();
        bool IsMemoryOverloaded();
        bool IsLatencyHigh();
        bool IsErrorRateHigh();
        
        // Historical data
        std::vector<ServerMetrics> GetMetricsHistory(std::chrono::hours duration);
        void ClearMetricsHistory();
        void ExportMetrics(const std::string& filename);
        
    private:
        void MonitoringLoop();
        void CheckThresholds(const ServerMetrics& metrics);
        void TriggerAlert(const std::string& alertType, const std::string& message);
    };

    // Event Management Module
    class EventManagementModule {
    private:
        struct GameEvent {
            uint32_t eventId;
            std::string eventName;
            std::string description;
            std::chrono::system_clock::time_point startTime;
            std::chrono::system_clock::time_point endTime;
            bool isActive;
            std::map<std::string, std::string> parameters;
        };
        
        std::map<uint32_t, GameEvent> events;
        std::thread eventThread;
        std::atomic<bool> eventManagerRunning;

    public:
        EventManagementModule();
        ~EventManagementModule();

        // Event management
        uint32_t CreateEvent(const std::string& name, const std::string& description,
                           std::chrono::system_clock::time_point startTime,
                           std::chrono::system_clock::time_point endTime);
        bool StartEvent(uint32_t eventId);
        bool StopEvent(uint32_t eventId);
        bool DeleteEvent(uint32_t eventId);
        
        // Event configuration
        void SetEventParameter(uint32_t eventId, const std::string& key, 
                             const std::string& value);
        std::string GetEventParameter(uint32_t eventId, const std::string& key);
        
        // Pre-defined events
        void StartDoubleExpEvent(std::chrono::hours duration);
        void StartDropRateEvent(float multiplier, std::chrono::hours duration);
        void StartPvPEvent(const std::string& mapName, std::chrono::hours duration);
        void StartGuildWarEvent(std::chrono::hours duration);
        void StartBossRaidEvent(uint32_t bossId, std::chrono::hours duration);
        
        // Event queries
        std::vector<GameEvent> GetActiveEvents();
        std::vector<GameEvent> GetScheduledEvents();
        GameEvent GetEvent(uint32_t eventId);
        
    private:
        void EventManagerLoop();
        void ProcessEventStart(const GameEvent& event);
        void ProcessEventEnd(const GameEvent& event);
    };

    // Economy Management Module
    class EconomyManagementModule {
    private:
        struct EconomySettings {
            float globalDropRate;
            float expMultiplier;
            float goldMultiplier;
            std::map<uint32_t, float> itemPrices;
            std::map<uint32_t, uint32_t> itemStock;
            bool economyEnabled;
        };
        
        EconomySettings settings;
        std::map<uint32_t, uint32_t> playerWealth;
        std::mutex economyMutex;

    public:
        EconomyManagementModule();
        ~EconomyManagementModule();

        // Economy control
        void SetGlobalDropRate(float rate);
        void SetExperienceMultiplier(float multiplier);
        void SetGoldMultiplier(float multiplier);
        void EnableEconomy(bool enable);
        
        // Item management
        void SetItemPrice(uint32_t itemId, float price);
        void SetItemStock(uint32_t itemId, uint32_t stock);
        void AddItemToMarket(uint32_t itemId, uint32_t quantity, float price);
        void RemoveItemFromMarket(uint32_t itemId);
        
        // Player economy
        uint32_t GetPlayerWealth(uint32_t playerId);
        void SetPlayerWealth(uint32_t playerId, uint32_t amount);
        void AddPlayerWealth(uint32_t playerId, uint32_t amount);
        void SubtractPlayerWealth(uint32_t playerId, uint32_t amount);
        
        // Market analysis
        std::map<uint32_t, float> GetMarketPrices();
        std::vector<uint32_t> GetTopSellingItems();
        float GetInflationRate();
        void GenerateEconomyReport(const std::string& filename);
        
        // Economy events
        void StartInflationEvent(float rate, std::chrono::hours duration);
        void StartDeflationEvent(float rate, std::chrono::hours duration);
        void StartMarketCrash(float severity, std::chrono::hours duration);
        void StartEconomicBoost(float multiplier, std::chrono::hours duration);
    };

    // Security Monitoring Module
    class SecurityMonitoringModule {
    private:
        struct SecurityEvent {
            std::chrono::system_clock::time_point timestamp;
            std::string eventType;
            std::string description;
            std::string sourceIP;
            uint32_t playerId;
            std::string severity;
        };
        
        std::vector<SecurityEvent> securityLog;
        std::map<std::string, uint32_t> suspiciousIPs;
        std::mutex securityMutex;
        bool realTimeMonitoring;

    public:
        SecurityMonitoringModule();
        ~SecurityMonitoringModule();

        // Security monitoring
        void EnableRealTimeMonitoring(bool enable);
        void LogSecurityEvent(const std::string& type, const std::string& description,
                            const std::string& sourceIP, uint32_t playerId = 0);
        
        // Threat detection
        bool DetectSpeedHack(uint32_t playerId);
        bool DetectTeleportHack(uint32_t playerId);
        bool DetectDupeExploit(uint32_t playerId);
        bool DetectPacketManipulation(uint32_t playerId);
        bool DetectBotBehavior(uint32_t playerId);
        
        // IP monitoring
        void AddSuspiciousIP(const std::string& ip, const std::string& reason);
        void RemoveSuspiciousIP(const std::string& ip);
        bool IsIPSuspicious(const std::string& ip);
        std::vector<std::string> GetSuspiciousIPs();
        
        // Security reports
        std::vector<SecurityEvent> GetSecurityEvents(std::chrono::hours duration);
        void GenerateSecurityReport(const std::string& filename);
        void ClearSecurityLog();
        
        // Automated responses
        void EnableAutoKick(bool enable);
        void EnableAutoBan(bool enable);
        void SetAutoResponseThreshold(uint32_t violations);
    };

    // Main Server Administration Manager
    class ServerAdministrationManager : public IGameModification {
    private:
        std::unique_ptr<ServerAdministrationModule> serverAdmin;
        std::unique_ptr<PerformanceMonitoringModule> performanceMonitor;
        std::unique_ptr<EventManagementModule> eventManager;
        std::unique_ptr<EconomyManagementModule> economyManager;
        std::unique_ptr<SecurityMonitoringModule> securityMonitor;
        
        bool enabled;
        std::string configFile;

    public:
        ServerAdministrationManager();
        virtual ~ServerAdministrationManager();

        // IGameModification interface
        bool Initialize() override;
        void Shutdown() override;
        const char* GetName() const override { return "ServerAdministrationManager"; }
        Priority GetPriority() const override { return Priority::CRITICAL; }
        bool IsEnabled() const override { return enabled; }
        void SetEnabled(bool enable) override;

        // Module access
        ServerAdministrationModule* GetServerAdmin() { return serverAdmin.get(); }
        PerformanceMonitoringModule* GetPerformanceMonitor() { return performanceMonitor.get(); }
        EventManagementModule* GetEventManager() { return eventManager.get(); }
        EconomyManagementModule* GetEconomyManager() { return economyManager.get(); }
        SecurityMonitoringModule* GetSecurityMonitor() { return securityMonitor.get(); }
        
        // Configuration
        void LoadConfiguration();
        void SaveConfiguration();
        void ResetToDefaults();
        
        // Dashboard
        void GenerateDashboard(const std::string& filename);
        void StartWebInterface(uint16_t port = 8080);
        void StopWebInterface();
    };

} // namespace NexusPro
