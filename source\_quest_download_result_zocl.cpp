#include <_quest_download_result_zocl.hpp>


START_ATF_NAMESPACE
    _quest_download_result_zocl::_quest_download_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _quest_download_result_zocl*);
        (org_ptr(0x1400ef590L))(this);
    };
    void _quest_download_result_zocl::ctor__quest_download_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _quest_download_result_zocl*);
        (org_ptr(0x1400ef590L))(this);
    };
    int _quest_download_result_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _quest_download_result_zocl*);
        return (org_ptr(0x1400ef600L))(this);
    };
    _quest_download_result_zocl::__list::__list()
    {
        using org_ptr = void (WINAPIV*)(struct _quest_download_result_zocl::__list*);
        (org_ptr(0x1400ef670L))(this);
    };
    void _quest_download_result_zocl::__list::ctor___list()
    {
        using org_ptr = void (WINAPIV*)(struct _quest_download_result_zocl::__list*);
        (org_ptr(0x1400ef670L))(this);
    };
END_ATF_NAMESPACE
