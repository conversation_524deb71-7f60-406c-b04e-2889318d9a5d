#include "PlayerMod.hpp"

namespace ATFMod {

    // Static member definitions
    PlayerMod::OriginalAddDalant PlayerMod::originalAddDalant = nullptr;
    PlayerMod::OriginalAddGold PlayerMod::originalAddGold = nullptr;
    PlayerMod::OriginalAlterExp PlayerMod::originalAlterExp = nullptr;
    PlayerMod::OriginalTakeDamage PlayerMod::originalTakeDamage = nullptr;

    PlayerMod::PlayerMod() 
        : enabled(false), godModeEnabled(false), unlimitedMoney(false), 
          speedHackEnabled(false), speedMultiplier(2.0f) {
    }

    bool PlayerMod::Initialize() {
        ATF_LOG_INFO("Initializing PlayerMod...");
        
        // Load settings from configuration
        LoadSettings();
        
        // Install hooks
        if (!InstallHooks()) {
            ATF_LOG_ERROR("Failed to install PlayerMod hooks");
            return false;
        }
        
        enabled = true;
        ATF_LOG_INFO("PlayerMod initialized successfully");
        return true;
    }

    void PlayerMod::Shutdown() {
        if (!enabled) return;
        
        ATF_LOG_INFO("Shutting down PlayerMod...");
        
        // Save settings
        SaveSettings();
        
        // Remove hooks
        RemoveHooks();
        
        enabled = false;
        ATF_LOG_INFO("PlayerMod shutdown complete");
    }

    void PlayerMod::SetEnabled(bool enable) {
        if (enable == enabled) return;
        
        if (enable) {
            if (!InstallHooks()) {
                ATF_LOG_ERROR("Failed to enable PlayerMod hooks");
                return;
            }
        } else {
            RemoveHooks();
        }
        
        enabled = enable;
        ATF_LOG_INFO("PlayerMod %s", enable ? "enabled" : "disabled");
    }

    void PlayerMod::SetGodMode(bool enable) {
        godModeEnabled = enable;
        ATF_LOG_INFO("God mode %s", enable ? "enabled" : "disabled");
        
        // Save setting
        auto* config = ATFModManager::GetInstance()->GetConfigManager();
        config->SetBool("PlayerMod.GodMode", enable);
    }

    void PlayerMod::SetUnlimitedMoney(bool enable) {
        unlimitedMoney = enable;
        ATF_LOG_INFO("Unlimited money %s", enable ? "enabled" : "disabled");
        
        // Save setting
        auto* config = ATFModManager::GetInstance()->GetConfigManager();
        config->SetBool("PlayerMod.UnlimitedMoney", enable);
    }

    void PlayerMod::SetSpeedHack(bool enable, float multiplier) {
        speedHackEnabled = enable;
        speedMultiplier = multiplier;
        ATF_LOG_INFO("Speed hack %s (multiplier: %.2f)", enable ? "enabled" : "disabled", multiplier);
        
        // Save settings
        auto* config = ATFModManager::GetInstance()->GetConfigManager();
        config->SetBool("PlayerMod.SpeedHack", enable);
        config->SetFloat("PlayerMod.SpeedMultiplier", multiplier);
    }

    void PlayerMod::AddExperience(long double amount) {
        ATF::CPlayer* player = GetLocalPlayer();
        if (!player) {
            ATF_LOG_ERROR("Local player not found");
            return;
        }
        
        // Call the original function to add experience
        if (originalAlterExp) {
            originalAlterExp(player, amount, true, false, false);
            ATF_LOG_INFO("Added %.0Lf experience to player", amount);
        }
    }

    void PlayerMod::AddMoney(unsigned int dalant, unsigned int gold) {
        ATF::CPlayer* player = GetLocalPlayer();
        if (!player) {
            ATF_LOG_ERROR("Local player not found");
            return;
        }
        
        // Add dalant
        if (dalant > 0 && originalAddDalant) {
            originalAddDalant(player, dalant, true);
            ATF_LOG_INFO("Added %u dalant to player", dalant);
        }
        
        // Add gold
        if (gold > 0 && originalAddGold) {
            originalAddGold(player, gold, true);
            ATF_LOG_INFO("Added %u gold to player", gold);
        }
    }

    void PlayerMod::LoadSettings() {
        auto* config = ATFModManager::GetInstance()->GetConfigManager();
        
        godModeEnabled = config->GetBool("PlayerMod.GodMode", false);
        unlimitedMoney = config->GetBool("PlayerMod.UnlimitedMoney", false);
        speedHackEnabled = config->GetBool("PlayerMod.SpeedHack", false);
        speedMultiplier = config->GetFloat("PlayerMod.SpeedMultiplier", 2.0f);
        
        ATF_LOG_INFO("PlayerMod settings loaded");
    }

    void PlayerMod::SaveSettings() {
        auto* config = ATFModManager::GetInstance()->GetConfigManager();
        
        config->SetBool("PlayerMod.GodMode", godModeEnabled);
        config->SetBool("PlayerMod.UnlimitedMoney", unlimitedMoney);
        config->SetBool("PlayerMod.SpeedHack", speedHackEnabled);
        config->SetFloat("PlayerMod.SpeedMultiplier", speedMultiplier);
        
        config->SaveConfig();
        ATF_LOG_INFO("PlayerMod settings saved");
    }

    bool PlayerMod::InstallHooks() {
        auto* hookManager = ATFModManager::GetInstance()->GetHookManager();
        
        // Hook AddDalant function
        // Note: These addresses would need to be determined through reverse engineering
        void* addDalantAddr = reinterpret_cast<void*>(0x140055b00L);
        if (!hookManager->InstallHook("PlayerMod_AddDalant", addDalantAddr, 
                                     reinterpret_cast<void*>(HookedAddDalant))) {
            ATF_LOG_ERROR("Failed to hook AddDalant");
            return false;
        }
        originalAddDalant = hookManager->GetOriginalFunction<OriginalAddDalant>("PlayerMod_AddDalant");
        
        // Hook AddGold function
        void* addGoldAddr = reinterpret_cast<void*>(0x140055d30L);
        if (!hookManager->InstallHook("PlayerMod_AddGold", addGoldAddr, 
                                     reinterpret_cast<void*>(HookedAddGold))) {
            ATF_LOG_ERROR("Failed to hook AddGold");
            return false;
        }
        originalAddGold = hookManager->GetOriginalFunction<OriginalAddGold>("PlayerMod_AddGold");
        
        // Hook AlterExp function
        void* alterExpAddr = reinterpret_cast<void*>(0x14005bb50L);
        if (!hookManager->InstallHook("PlayerMod_AlterExp", alterExpAddr, 
                                     reinterpret_cast<void*>(HookedAlterExp))) {
            ATF_LOG_ERROR("Failed to hook AlterExp");
            return false;
        }
        originalAlterExp = hookManager->GetOriginalFunction<OriginalAlterExp>("PlayerMod_AlterExp");
        
        // Enable all hooks
        hookManager->EnableHook("PlayerMod_AddDalant");
        hookManager->EnableHook("PlayerMod_AddGold");
        hookManager->EnableHook("PlayerMod_AlterExp");
        
        ATF_LOG_INFO("PlayerMod hooks installed successfully");
        return true;
    }

    void PlayerMod::RemoveHooks() {
        auto* hookManager = ATFModManager::GetInstance()->GetHookManager();
        
        hookManager->RemoveHook("PlayerMod_AddDalant");
        hookManager->RemoveHook("PlayerMod_AddGold");
        hookManager->RemoveHook("PlayerMod_AlterExp");
        
        originalAddDalant = nullptr;
        originalAddGold = nullptr;
        originalAlterExp = nullptr;
        
        ATF_LOG_INFO("PlayerMod hooks removed");
    }

    ATF::CPlayer* PlayerMod::GetLocalPlayer() {
        // This would need to be implemented based on the game's structure
        // Typically involves finding the local player instance in memory
        // For example:
        // return *reinterpret_cast<ATF::CPlayer**>(0x141234567L);
        return nullptr; // Placeholder
    }

    // Hook function implementations
    void WINAPIV PlayerMod::HookedAddDalant(ATF::CPlayer* player, unsigned int amount, bool apply) {
        // Get the PlayerMod instance
        auto* playerMod = static_cast<PlayerMod*>(
            ATFModManager::GetInstance()->GetModification("PlayerMod"));
        
        if (playerMod && playerMod->unlimitedMoney) {
            // If unlimited money is enabled, multiply the amount
            amount *= 10; // Give 10x more money
            ATF_LOG_DEBUG("Unlimited money: multiplied dalant amount to %u", amount);
        }
        
        // Call original function
        if (originalAddDalant) {
            originalAddDalant(player, amount, apply);
        }
    }

    void WINAPIV PlayerMod::HookedAddGold(ATF::CPlayer* player, unsigned int amount, bool apply) {
        // Get the PlayerMod instance
        auto* playerMod = static_cast<PlayerMod*>(
            ATFModManager::GetInstance()->GetModification("PlayerMod"));
        
        if (playerMod && playerMod->unlimitedMoney) {
            // If unlimited money is enabled, multiply the amount
            amount *= 10; // Give 10x more money
            ATF_LOG_DEBUG("Unlimited money: multiplied gold amount to %u", amount);
        }
        
        // Call original function
        if (originalAddGold) {
            originalAddGold(player, amount, apply);
        }
    }

    void WINAPIV PlayerMod::HookedAlterExp(ATF::CPlayer* player, long double exp, bool reward, bool useRecover, bool useAddition) {
        // Get the PlayerMod instance
        auto* playerMod = static_cast<PlayerMod*>(
            ATFModManager::GetInstance()->GetModification("PlayerMod"));
        
        if (playerMod) {
            // Could add experience multiplier here
            // exp *= 2.0; // Double experience
        }
        
        // Call original function
        if (originalAlterExp) {
            originalAlterExp(player, exp, reward, useRecover, useAddition);
        }
    }

    void WINAPIV PlayerMod::HookedTakeDamage(ATF::CPlayer* player, int damage, void* attacker) {
        // Get the PlayerMod instance
        auto* playerMod = static_cast<PlayerMod*>(
            ATFModManager::GetInstance()->GetModification("PlayerMod"));
        
        if (playerMod && playerMod->godModeEnabled) {
            // If god mode is enabled, don't take damage
            ATF_LOG_DEBUG("God mode: blocked %d damage", damage);
            return; // Don't call original function
        }
        
        // Call original function
        if (originalTakeDamage) {
            originalTakeDamage(player, damage, attacker);
        }
    }

} // namespace ATFMod
