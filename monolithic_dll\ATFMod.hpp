#pragma once

// NexusPro Monolithic DLL Modification Framework
// This header defines the main interface for game modifications
// Compatible with Visual Studio 2022

#include <windows.h>
#include <iostream>
#include <vector>
#include <unordered_map>
#include <functional>
#include <memory>

// Include ATF framework
#include "../ATF/ATF.hpp"
#include "../ATF/common/common.h"

// Forward declarations
class ATFModManager;
class HookManager;
class MemoryPatcher;

namespace NexusPro {

    // Hook types for different modification approaches
    enum class HookType {
        DETOUR,         // Function detour (redirect entire function)
        INLINE,         // Inline hook (patch function beginning)
        VTABLE,         // Virtual table hook
        IAT,            // Import Address Table hook
        MEMORY_PATCH    // Direct memory patching
    };

    // Modification priority levels
    enum class Priority {
        CRITICAL = 0,   // System-level modifications
        HIGH = 1,       // Core gameplay modifications
        NORMAL = 2,     // Standard modifications
        LOW = 3         // Cosmetic/UI modifications
    };

    // Hook information structure
    struct HookInfo {
        void* originalFunction;
        void* hookFunction;
        void* trampolineFunction;
        HookType type;
        Priority priority;
        std::string name;
        bool enabled;
        
        HookInfo() : originalFunction(nullptr), hookFunction(nullptr), 
                    trampolineFunction(nullptr), type(HookType::DETOUR), 
                    priority(Priority::NORMAL), enabled(false) {}
    };

    // Base class for all game modifications
    class IGameModification {
    public:
        virtual ~IGameModification() = default;
        virtual bool Initialize() = 0;
        virtual void Shutdown() = 0;
        virtual const char* GetName() const = 0;
        virtual Priority GetPriority() const = 0;
        virtual bool IsEnabled() const = 0;
        virtual void SetEnabled(bool enabled) = 0;
    };

    // Memory address utilities
    class MemoryUtils {
    public:
        static bool IsValidAddress(void* address);
        static bool SetMemoryProtection(void* address, size_t size, DWORD protection);
        static bool WriteMemory(void* address, const void* data, size_t size);
        static bool ReadMemory(void* address, void* buffer, size_t size);
        static void* FindPattern(const char* pattern, const char* mask, void* start = nullptr, size_t size = 0);
        static uintptr_t GetModuleBase(const char* moduleName = nullptr);
        static size_t GetModuleSize(const char* moduleName = nullptr);
    };

    // Hook manager for function interception
    class HookManager {
    private:
        std::unordered_map<std::string, std::unique_ptr<HookInfo>> hooks;
        bool initialized;

    public:
        HookManager();
        ~HookManager();

        bool Initialize();
        void Shutdown();

        // Hook management
        bool InstallHook(const std::string& name, void* target, void* detour, HookType type = HookType::DETOUR);
        bool RemoveHook(const std::string& name);
        bool EnableHook(const std::string& name);
        bool DisableHook(const std::string& name);
        
        // Get original function for calling
        template<typename T>
        T GetOriginalFunction(const std::string& name) {
            auto it = hooks.find(name);
            if (it != hooks.end() && it->second->trampolineFunction) {
                return reinterpret_cast<T>(it->second->trampolineFunction);
            }
            return nullptr;
        }

        // Utility functions
        bool IsHookInstalled(const std::string& name) const;
        std::vector<std::string> GetInstalledHooks() const;
    };

    // Memory patcher for direct memory modifications
    class MemoryPatcher {
    private:
        struct PatchInfo {
            void* address;
            std::vector<uint8_t> originalBytes;
            std::vector<uint8_t> patchBytes;
            bool applied;
        };
        
        std::unordered_map<std::string, PatchInfo> patches;

    public:
        bool AddPatch(const std::string& name, void* address, const std::vector<uint8_t>& patchBytes);
        bool ApplyPatch(const std::string& name);
        bool RemovePatch(const std::string& name);
        bool RestorePatch(const std::string& name);
        void RestoreAllPatches();
    };

    // Configuration manager
    class ConfigManager {
    private:
        std::unordered_map<std::string, std::string> settings;
        std::string configFile;

    public:
        ConfigManager(const std::string& filename = "ATFMod.ini");
        
        bool LoadConfig();
        bool SaveConfig();
        
        // Setting accessors
        std::string GetString(const std::string& key, const std::string& defaultValue = "");
        int GetInt(const std::string& key, int defaultValue = 0);
        bool GetBool(const std::string& key, bool defaultValue = false);
        float GetFloat(const std::string& key, float defaultValue = 0.0f);
        
        void SetString(const std::string& key, const std::string& value);
        void SetInt(const std::string& key, int value);
        void SetBool(const std::string& key, bool value);
        void SetFloat(const std::string& key, float value);
    };

    // Main modification manager
    class NexusProModManager {
    private:
        static NexusProModManager* instance;
        std::vector<std::unique_ptr<IGameModification>> modifications;
        std::unique_ptr<HookManager> hookManager;
        std::unique_ptr<MemoryPatcher> memoryPatcher;
        std::unique_ptr<ConfigManager> configManager;
        bool initialized;

        NexusProModManager();

    public:
        static NexusProModManager* GetInstance();
        ~NexusProModManager();

        // Initialization
        bool Initialize();
        void Shutdown();

        // Modification management
        bool RegisterModification(std::unique_ptr<IGameModification> mod);
        bool UnregisterModification(const std::string& name);
        IGameModification* GetModification(const std::string& name);
        
        // Manager accessors
        HookManager* GetHookManager() { return hookManager.get(); }
        MemoryPatcher* GetMemoryPatcher() { return memoryPatcher.get(); }
        ConfigManager* GetConfigManager() { return configManager.get(); }

        // Utility functions
        bool IsInitialized() const { return initialized; }
        std::vector<std::string> GetLoadedModifications() const;
    };

    // Logging system
    class Logger {
    public:
        enum Level {
            DEBUG = 0,
            INFO = 1,
            WARNING = 2,
            ERROR = 3
        };

    private:
        static Level currentLevel;
        static std::string logFile;

    public:
        static void SetLevel(Level level);
        static void SetLogFile(const std::string& filename);
        static void Log(Level level, const char* format, ...);
        
        // Convenience macros
        static void Debug(const char* format, ...);
        static void Info(const char* format, ...);
        static void Warning(const char* format, ...);
        static void Error(const char* format, ...);
    };

    // Convenience macros for logging
    #define NEXUS_LOG_DEBUG(...)   NexusPro::Logger::Debug(__VA_ARGS__)
    #define NEXUS_LOG_INFO(...)    NexusPro::Logger::Info(__VA_ARGS__)
    #define NEXUS_LOG_WARNING(...) NexusPro::Logger::Warning(__VA_ARGS__)
    #define NEXUS_LOG_ERROR(...)   NexusPro::Logger::Error(__VA_ARGS__)

    // Initialization and cleanup functions
    bool InitializeNexusPro();
    void ShutdownNexusPro();

} // namespace NexusPro

// DLL export functions
extern "C" {
    __declspec(dllexport) bool NexusPro_Initialize();
    __declspec(dllexport) void NexusPro_Shutdown();
    __declspec(dllexport) const char* NexusPro_GetVersion();
    __declspec(dllexport) bool NexusPro_LoadModification(const char* modName);
    __declspec(dllexport) bool NexusPro_UnloadModification(const char* modName);
    __declspec(dllexport) bool NexusPro_EnableModification(const char* modName, bool enable);
    __declspec(dllexport) bool NexusPro_IsModificationLoaded(const char* modName);
    __declspec(dllexport) int NexusPro_GetLoadedModifications(char* buffer, int bufferSize);
    __declspec(dllexport) bool NexusPro_ExecuteCommand(const char* command);
}
