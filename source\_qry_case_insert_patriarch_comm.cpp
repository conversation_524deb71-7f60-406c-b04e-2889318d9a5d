#include <_qry_case_insert_patriarch_comm.hpp>


START_ATF_NAMESPACE
    _qry_case_insert_patriarch_comm::_qry_case_insert_patriarch_comm()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_insert_patriarch_comm*);
        (org_ptr(0x1402d98f0L))(this);
    };
    void _qry_case_insert_patriarch_comm::ctor__qry_case_insert_patriarch_comm()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_insert_patriarch_comm*);
        (org_ptr(0x1402d98f0L))(this);
    };
    int _qry_case_insert_patriarch_comm::size()
    {
        using org_ptr = int (WINAPIV*)(struct _qry_case_insert_patriarch_comm*);
        return (org_ptr(0x1402d9940L))(this);
    };
END_ATF_NAMESPACE
