// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _worlddb_sf_delay_info
    {
        struct  _eff_list
        {
            char byEffectCode;
            unsigned __int16 wEffectIndex;
            unsigned int dwNextTime;
        public:
            _eff_list();
            void ctor__eff_list();
        };
        struct  _mas_list
        {
            char byEffectCode;
            char byMastery;
            unsigned int dwNextTime;
        public:
            _mas_list();
            void ctor__mas_list();
        };
        _eff_list EFF[10];
        _mas_list MAS[10];
    public:
        _worlddb_sf_delay_info();
        void ctor__worlddb_sf_delay_info();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_worlddb_sf_delay_info, 130>(), "_worlddb_sf_delay_info");
END_ATF_NAMESPACE
