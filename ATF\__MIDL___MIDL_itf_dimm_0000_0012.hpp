// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <$34FDA543096C642434F51FD97559BDFE.hpp>
#include <HIMCC__.hpp>
#include <HWND__.hpp>
#include <tagCANDIDATEFORM.hpp>
#include <tagCOMPOSITIONFORM.hpp>
#include <tagPOINT.hpp>


START_ATF_NAMESPACE
    struct __MIDL___MIDL_itf_dimm_0000_0012
    {
        HWND__ *hWnd;
        int fOpen;
        tagPOINT ptStatusWndPos;
        tagPOINT ptSoftKbdPos;
        unsigned int fdwConversion;
        unsigned int fdwSentence;
        $34FDA543096C642434F51FD97559BDFE lfFont;
        tagCOMPOSITIONFORM cfCompForm;
        tagCANDIDATEFORM cfCandForm[4];
        HIMCC__ *hCompStr;
        H<PERSON><PERSON>__ *hCandInfo;
        H<PERSON>CC__ *hGuideLine;
        HIMCC__ *hPrivate;
        unsigned int dwNumMsgBuf;
        HIMCC__ *hMsgBuf;
        unsigned int fdwInit;
        unsigned int dwReserve[3];
    };
END_ATF_NAMESPACE
