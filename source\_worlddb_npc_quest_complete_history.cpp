#include <_worlddb_npc_quest_complete_history.hpp>


START_ATF_NAMESPACE
    _worlddb_npc_quest_complete_history::_worlddb_npc_quest_complete_history()
    {
        using org_ptr = void (WINAPIV*)(struct _worlddb_npc_quest_complete_history*);
        (org_ptr(0x1401bf130L))(this);
    };
    void _worlddb_npc_quest_complete_history::ctor__worlddb_npc_quest_complete_history()
    {
        using org_ptr = void (WINAPIV*)(struct _worlddb_npc_quest_complete_history*);
        (org_ptr(0x1401bf130L))(this);
    };
    _worlddb_npc_quest_complete_history::__list::__list()
    {
        using org_ptr = void (WINAPIV*)(struct _worlddb_npc_quest_complete_history::__list*);
        (org_ptr(0x1401bf190L))(this);
    };
    void _worlddb_npc_quest_complete_history::__list::ctor___list()
    {
        using org_ptr = void (WINAPIV*)(struct _worlddb_npc_quest_complete_history::__list*);
        (org_ptr(0x1401bf190L))(this);
    };
END_ATF_NAMESPACE
