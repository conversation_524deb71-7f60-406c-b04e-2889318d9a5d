// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_POINTL.hpp>



START_ATF_NAMESPACE
    struct __MIDL___MIDL_itf_shobjidl_0230_0001
    {
        unsigned int dwMask;
        _<PERSON><PERSON><PERSON>TL ptMinSize;
        _POINTL ptMaxSize;
        _POINTL ptIntegral;
        _POINTL ptActual;
        wchar_t wszTitle[256];
        unsigned int dwModeFlags;
        unsigned int crBkgnd;
    };
END_ATF_NAMESPACE
