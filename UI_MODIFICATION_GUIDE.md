# NexusPro UI Modification Guide

## 🎯 **Changing "RF SERVER Now Loading" to "NexusProtection"**

### **Overview**
The UI Modification Module allows you to intercept and modify any text displayed in the game's user interface, including loading dialogs, window titles, and message boxes.

### **Quick Implementation**

#### **1. Automatic Setup (Recommended)**
The module automatically replaces "RF SERVER Now Loading" with "NexusProtection" when enabled:

```cpp
// The module is automatically registered in DllMain.cpp
// It will immediately start intercepting UI text when the DLL is injected
```

#### **2. Manual Configuration**
You can customize the replacement text:

```cpp
// Get the UI modification module
auto* uiMod = static_cast<UIModificationModule*>(
    NexusProModManager::GetInstance()->GetModification("UIModificationModule"));

// Set custom loading dialog text
uiMod->SetLoadingDialogText("NexusProtection");
uiMod->SetServerLoadingText("NexusProtection - Secure Connection");

// Enable NexusProtection branding across all UI elements
uiMod->EnableNexusProtectionBranding();
```

### **How It Works**

#### **Hook-Based Interception**
The module hooks Windows API functions that create and modify UI elements:

1. **CreateWindowExA/W** - Intercepts window creation
2. **SetWindowTextA/W** - Intercepts text changes
3. **MessageBoxA/W** - Intercepts message boxes

#### **Text Processing Flow**
```
Game calls CreateWindowExA("RF SERVER Now Loading")
    ↓
Hook intercepts the call
    ↓
Text is processed: "RF SERVER Now Loading" → "NexusProtection"
    ↓
Modified text is passed to original Windows API
    ↓
Dialog displays "NexusProtection"
```

### **Supported Text Replacements**

#### **Default Replacements**
When the module is enabled, these replacements are automatically active:

| Original Text | Replacement Text |
|---------------|------------------|
| "RF SERVER Now Loading" | "NexusProtection" |
| "RF Online" | "NexusProtection" |
| "Loading..." | "NexusProtection Loading..." |
| "Connecting to server" | "NexusProtection - Connecting" |
| "Please wait" | "NexusProtection - Please wait" |

#### **Custom Replacements**
You can add your own text replacements:

```cpp
// Add custom text replacement
uiMod->AddTextReplacement(
    "Original Text",           // Text to find
    "Replacement Text",        // Text to replace with
    UIElementType::LOADING_DIALOG,  // Type of UI element
    false,                     // Case sensitive (false = ignore case)
    true                       // Exact match (true = must match exactly)
);
```

### **Advanced Configuration**

#### **UI Element Types**
You can target specific types of UI elements:

```cpp
enum class UIElementType {
    LOADING_DIALOG,    // Loading screens and progress dialogs
    MESSAGE_BOX,       // Error messages and notifications
    WINDOW_TITLE,      // Window title bars
    BUTTON_TEXT,       // Button labels
    LABEL_TEXT,        // Static text labels
    TOOLTIP_TEXT,      // Hover tooltips
    STATUS_BAR,        // Status bar text
    MENU_ITEM          // Menu items
};
```

#### **Replacement Options**
```cpp
// Case-sensitive exact match
uiMod->AddTextReplacement("RF SERVER Now Loading", "NexusProtection", 
                         UIElementType::LOADING_DIALOG, true, true);

// Case-insensitive partial match
uiMod->AddTextReplacement("loading", "NexusProtection Loading", 
                         UIElementType::LOADING_DIALOG, false, false);
```

### **Real-Time Monitoring**

#### **Debug Logging**
Enable debug logging to see what text is being intercepted:

```cpp
// Enable debug logging
Logger::SetLevel(Logger::DEBUG);

// You'll see logs like:
// [DEBUG] CreateWindowExA: Original='RF SERVER Now Loading', Processed='NexusProtection'
// [DEBUG] SetWindowTextA: Original='Loading...', Processed='NexusProtection Loading...'
```

#### **Runtime Control**
You can enable/disable the module at runtime:

```cpp
// Disable UI modifications
uiMod->SetEnabled(false);

// Re-enable UI modifications
uiMod->SetEnabled(true);
```

### **Testing the Implementation**

#### **1. Build and Inject**
```cmd
# Build the DLL
cmake --build . --config Release

# Inject into RF Online
NexusProInjector.exe RFOnline.exe NexusProMod.dll
```

#### **2. Verify the Changes**
1. Start RF Online
2. Look for loading dialogs
3. Verify "RF SERVER Now Loading" appears as "NexusProtection"
4. Check the debug log for confirmation

#### **3. Troubleshooting**
If the text doesn't change:

1. **Check the log file** for hook installation messages
2. **Verify the exact text** - it must match exactly (unless using partial matching)
3. **Try different UI element types** - the dialog might be a different type
4. **Enable debug logging** to see what text is being intercepted

### **Advanced Customization**

#### **Multiple Replacements**
```cpp
// Set up comprehensive branding
uiMod->AddTextReplacement("RF SERVER Now Loading", "NexusProtection", UIElementType::LOADING_DIALOG);
uiMod->AddTextReplacement("RF Online", "NexusProtection", UIElementType::WINDOW_TITLE);
uiMod->AddTextReplacement("Connecting", "NexusProtection - Secure Connection", UIElementType::LOADING_DIALOG);
uiMod->AddTextReplacement("Error", "NexusProtection - Error", UIElementType::MESSAGE_BOX);
```

#### **Dynamic Text Generation**
```cpp
// You can also modify the LoadingDialogInterceptor for more complex logic
auto* interceptor = LoadingDialogInterceptor::GetInstance();
interceptor->SetCustomLoadingText("NexusProtection v1.0");
interceptor->SetCustomServerText("NexusProtection - Secure Server");
```

#### **Window-Specific Replacements**
```cpp
// Target specific windows
uiMod->SetWindowTitle("RF Online", "NexusProtection");
uiMod->SetMessageBoxText("Connection failed", "NexusProtection - Connection failed");
```

### **Configuration File**

#### **Save/Load Settings**
The module can save and load its configuration:

```ini
# NexusProMod.ini
[UIModification]
Enabled=true
LoadingText=NexusProtection
ServerText=NexusProtection - Secure Connection
BrandingEnabled=true

[TextReplacements]
Count=3
Replacement1_Original=RF SERVER Now Loading
Replacement1_New=NexusProtection
Replacement1_Type=LOADING_DIALOG
```

### **Performance Considerations**

#### **Minimal Overhead**
- Hook installation: ~1ms
- Text processing: <0.1ms per call
- Memory usage: <1MB additional

#### **Optimization Tips**
1. Use exact matches when possible (faster than partial matching)
2. Limit the number of text replacements
3. Disable the module when not needed

### **Security Notes**

#### **Anti-Detection**
The UI modification hooks are relatively safe because:
- They use standard Windows API hooks
- No direct game memory modification
- Minimal footprint in game process

#### **Stealth Mode**
For maximum stealth:
```cpp
// Only replace critical text
uiMod->AddTextReplacement("RF SERVER Now Loading", "NexusProtection", UIElementType::LOADING_DIALOG);

// Disable debug logging
Logger::SetLevel(Logger::ERROR);
```

### **Example: Complete Setup**

```cpp
// Complete example of setting up UI modifications
void SetupUIModifications() {
    auto* uiMod = static_cast<UIModificationModule*>(
        NexusProModManager::GetInstance()->GetModification("UIModificationModule"));
    
    if (!uiMod) {
        NEXUS_LOG_ERROR("UI Modification Module not found");
        return;
    }
    
    // Enable the module
    uiMod->SetEnabled(true);
    
    // Set up NexusProtection branding
    uiMod->EnableNexusProtectionBranding();
    
    // Add custom replacements
    uiMod->AddTextReplacement("RF SERVER Now Loading", "NexusProtection", UIElementType::LOADING_DIALOG);
    uiMod->AddTextReplacement("Connection Error", "NexusProtection - Connection Error", UIElementType::MESSAGE_BOX);
    
    // Set custom loading text
    uiMod->SetLoadingDialogText("NexusProtection v1.0");
    uiMod->SetServerLoadingText("NexusProtection - Secure Connection");
    
    NEXUS_LOG_INFO("UI modifications configured successfully");
}
```

This implementation provides a robust, flexible system for modifying any UI text in the game, with the specific capability to change "RF SERVER Now Loading" to "NexusProtection" as requested.
