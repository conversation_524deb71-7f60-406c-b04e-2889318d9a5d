// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>



START_ATF_NAMESPACE
    struct _xml_error
    {
        unsigned int _nLine;
        wchar_t *_pchBuf;
        unsigned int _cchBuf;
        unsigned int _ich;
        wchar_t *_pszFound;
        wchar_t *_pszExpected;
        unsigned int _reserved1;
        unsigned int _reserved2;
    };
END_ATF_NAMESPACE
