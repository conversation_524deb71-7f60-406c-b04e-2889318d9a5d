#include <GUILD_BATTLE__CNormalGuildBattleDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Detail
        {
            Info::GUILD_BATTLE__CNormalGuildBattleAddComplete2_ptr GUILD_BATTLE__CNormalGuildBattleAddComplete2_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleAddComplete2_clbk GUILD_BATTLE__CNormalGuildBattleAddComplete2_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleAskJoin4_ptr GUILD_BATTLE__CNormalGuildBattleAskJoin4_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleAskJoin4_clbk GUILD_BATTLE__CNormalGuildBattleAskJoin4_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleAskJoin6_ptr GUILD_BATTLE__CNormalGuildBattleAskJoin6_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleAskJoin6_clbk GUILD_BATTLE__CNormalGuildBattleAskJoin6_user(nullptr);
            
            
            Info::GUILD_BATTLE__CNormalGuildBattlector_CNormalGuildBattle8_ptr GUILD_BATTLE__CNormalGuildBattlector_CNormalGuildBattle8_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattlector_CNormalGuildBattle8_clbk GUILD_BATTLE__CNormalGuildBattlector_CNormalGuildBattle8_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleCleanUpBattle10_ptr GUILD_BATTLE__CNormalGuildBattleCleanUpBattle10_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleCleanUpBattle10_clbk GUILD_BATTLE__CNormalGuildBattleCleanUpBattle10_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleClear12_ptr GUILD_BATTLE__CNormalGuildBattleClear12_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleClear12_clbk GUILD_BATTLE__CNormalGuildBattleClear12_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleClearDBRecord14_ptr GUILD_BATTLE__CNormalGuildBattleClearDBRecord14_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleClearDBRecord14_clbk GUILD_BATTLE__CNormalGuildBattleClearDBRecord14_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleCreateLogFile16_ptr GUILD_BATTLE__CNormalGuildBattleCreateLogFile16_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleCreateLogFile16_clbk GUILD_BATTLE__CNormalGuildBattleCreateLogFile16_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleCreateLogger18_ptr GUILD_BATTLE__CNormalGuildBattleCreateLogger18_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleCreateLogger18_clbk GUILD_BATTLE__CNormalGuildBattleCreateLogger18_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleDecideColorInx20_ptr GUILD_BATTLE__CNormalGuildBattleDecideColorInx20_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleDecideColorInx20_clbk GUILD_BATTLE__CNormalGuildBattleDecideColorInx20_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleDecideWin22_ptr GUILD_BATTLE__CNormalGuildBattleDecideWin22_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleDecideWin22_clbk GUILD_BATTLE__CNormalGuildBattleDecideWin22_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleDividePvpPoint24_ptr GUILD_BATTLE__CNormalGuildBattleDividePvpPoint24_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleDividePvpPoint24_clbk GUILD_BATTLE__CNormalGuildBattleDividePvpPoint24_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleDropGravityStone26_ptr GUILD_BATTLE__CNormalGuildBattleDropGravityStone26_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleDropGravityStone26_clbk GUILD_BATTLE__CNormalGuildBattleDropGravityStone26_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGet1P28_ptr GUILD_BATTLE__CNormalGuildBattleGet1P28_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGet1P28_clbk GUILD_BATTLE__CNormalGuildBattleGet1P28_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGet2P30_ptr GUILD_BATTLE__CNormalGuildBattleGet2P30_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGet2P30_clbk GUILD_BATTLE__CNormalGuildBattleGet2P30_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGetBlue32_ptr GUILD_BATTLE__CNormalGuildBattleGetBlue32_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGetBlue32_clbk GUILD_BATTLE__CNormalGuildBattleGetBlue32_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGetField34_ptr GUILD_BATTLE__CNormalGuildBattleGetField34_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGetField34_clbk GUILD_BATTLE__CNormalGuildBattleGetField34_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGetGravityStone36_ptr GUILD_BATTLE__CNormalGuildBattleGetGravityStone36_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGetGravityStone36_clbk GUILD_BATTLE__CNormalGuildBattleGetGravityStone36_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGetGuild38_ptr GUILD_BATTLE__CNormalGuildBattleGetGuild38_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGetGuild38_clbk GUILD_BATTLE__CNormalGuildBattleGetGuild38_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGetGuildBattleNumber40_ptr GUILD_BATTLE__CNormalGuildBattleGetGuildBattleNumber40_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGetGuildBattleNumber40_clbk GUILD_BATTLE__CNormalGuildBattleGetGuildBattleNumber40_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGetID42_ptr GUILD_BATTLE__CNormalGuildBattleGetID42_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGetID42_clbk GUILD_BATTLE__CNormalGuildBattleGetID42_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGetInfo44_ptr GUILD_BATTLE__CNormalGuildBattleGetInfo44_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGetInfo44_clbk GUILD_BATTLE__CNormalGuildBattleGetInfo44_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGetLogger46_ptr GUILD_BATTLE__CNormalGuildBattleGetLogger46_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGetLogger46_clbk GUILD_BATTLE__CNormalGuildBattleGetLogger46_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGetObjType48_ptr GUILD_BATTLE__CNormalGuildBattleGetObjType48_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGetObjType48_clbk GUILD_BATTLE__CNormalGuildBattleGetObjType48_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGetRed50_ptr GUILD_BATTLE__CNormalGuildBattleGetRed50_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGetRed50_clbk GUILD_BATTLE__CNormalGuildBattleGetRed50_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGoal52_ptr GUILD_BATTLE__CNormalGuildBattleGoal52_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGoal52_clbk GUILD_BATTLE__CNormalGuildBattleGoal52_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLog54_ptr GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLog54_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLog54_clbk GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLog54_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogNotifyWeb56_ptr GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogNotifyWeb56_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogNotifyWeb56_clbk GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogNotifyWeb56_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogPushDBLog58_ptr GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogPushDBLog58_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogPushDBLog58_clbk GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogPushDBLog58_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleInit60_ptr GUILD_BATTLE__CNormalGuildBattleInit60_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleInit60_clbk GUILD_BATTLE__CNormalGuildBattleInit60_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleInit62_ptr GUILD_BATTLE__CNormalGuildBattleInit62_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleInit62_clbk GUILD_BATTLE__CNormalGuildBattleInit62_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleIsEmpty64_ptr GUILD_BATTLE__CNormalGuildBattleIsEmpty64_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleIsEmpty64_clbk GUILD_BATTLE__CNormalGuildBattleIsEmpty64_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleIsInBattle66_ptr GUILD_BATTLE__CNormalGuildBattleIsInBattle66_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleIsInBattle66_clbk GUILD_BATTLE__CNormalGuildBattleIsInBattle66_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleIsInBattleRegenState68_ptr GUILD_BATTLE__CNormalGuildBattleIsInBattleRegenState68_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleIsInBattleRegenState68_clbk GUILD_BATTLE__CNormalGuildBattleIsInBattleRegenState68_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleIsMemberGuild70_ptr GUILD_BATTLE__CNormalGuildBattleIsMemberGuild70_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleIsMemberGuild70_clbk GUILD_BATTLE__CNormalGuildBattleIsMemberGuild70_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleIsProc72_ptr GUILD_BATTLE__CNormalGuildBattleIsProc72_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleIsProc72_clbk GUILD_BATTLE__CNormalGuildBattleIsProc72_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleIsReStart74_ptr GUILD_BATTLE__CNormalGuildBattleIsReStart74_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleIsReStart74_clbk GUILD_BATTLE__CNormalGuildBattleIsReStart74_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleIsReadyOrCountState76_ptr GUILD_BATTLE__CNormalGuildBattleIsReadyOrCountState76_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleIsReadyOrCountState76_clbk GUILD_BATTLE__CNormalGuildBattleIsReadyOrCountState76_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleJoin78_ptr GUILD_BATTLE__CNormalGuildBattleJoin78_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleJoin78_clbk GUILD_BATTLE__CNormalGuildBattleJoin78_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleJudgeBattle80_ptr GUILD_BATTLE__CNormalGuildBattleJudgeBattle80_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleJudgeBattle80_clbk GUILD_BATTLE__CNormalGuildBattleJudgeBattle80_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleKill82_ptr GUILD_BATTLE__CNormalGuildBattleKill82_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleKill82_clbk GUILD_BATTLE__CNormalGuildBattleKill82_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleLeaveGuild84_ptr GUILD_BATTLE__CNormalGuildBattleLeaveGuild84_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleLeaveGuild84_clbk GUILD_BATTLE__CNormalGuildBattleLeaveGuild84_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleLogIn86_ptr GUILD_BATTLE__CNormalGuildBattleLogIn86_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleLogIn86_clbk GUILD_BATTLE__CNormalGuildBattleLogIn86_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleNetClose88_ptr GUILD_BATTLE__CNormalGuildBattleNetClose88_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleNetClose88_clbk GUILD_BATTLE__CNormalGuildBattleNetClose88_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleNotifyAllProcessEnd90_ptr GUILD_BATTLE__CNormalGuildBattleNotifyAllProcessEnd90_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleNotifyAllProcessEnd90_clbk GUILD_BATTLE__CNormalGuildBattleNotifyAllProcessEnd90_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleNotifyBallPosition92_ptr GUILD_BATTLE__CNormalGuildBattleNotifyBallPosition92_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleNotifyBallPosition92_clbk GUILD_BATTLE__CNormalGuildBattleNotifyBallPosition92_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleNotifyBattleResult94_ptr GUILD_BATTLE__CNormalGuildBattleNotifyBattleResult94_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleNotifyBattleResult94_clbk GUILD_BATTLE__CNormalGuildBattleNotifyBattleResult94_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleNotifyBeforeStart96_ptr GUILD_BATTLE__CNormalGuildBattleNotifyBeforeStart96_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleNotifyBeforeStart96_clbk GUILD_BATTLE__CNormalGuildBattleNotifyBeforeStart96_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleNotifyCommitteeMemberPosition98_ptr GUILD_BATTLE__CNormalGuildBattleNotifyCommitteeMemberPosition98_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleNotifyCommitteeMemberPosition98_clbk GUILD_BATTLE__CNormalGuildBattleNotifyCommitteeMemberPosition98_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleNotifyDestoryBall100_ptr GUILD_BATTLE__CNormalGuildBattleNotifyDestoryBall100_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleNotifyDestoryBall100_clbk GUILD_BATTLE__CNormalGuildBattleNotifyDestoryBall100_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleNotifyPassGravityStoneLimitTime102_ptr GUILD_BATTLE__CNormalGuildBattleNotifyPassGravityStoneLimitTime102_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleNotifyPassGravityStoneLimitTime102_clbk GUILD_BATTLE__CNormalGuildBattleNotifyPassGravityStoneLimitTime102_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleProcess104_ptr GUILD_BATTLE__CNormalGuildBattleProcess104_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleProcess104_clbk GUILD_BATTLE__CNormalGuildBattleProcess104_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattlePushDQSDrawRank106_ptr GUILD_BATTLE__CNormalGuildBattlePushDQSDrawRank106_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattlePushDQSDrawRank106_clbk GUILD_BATTLE__CNormalGuildBattlePushDQSDrawRank106_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattlePushDQSWinLoseRank108_ptr GUILD_BATTLE__CNormalGuildBattlePushDQSWinLoseRank108_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattlePushDQSWinLoseRank108_clbk GUILD_BATTLE__CNormalGuildBattlePushDQSWinLoseRank108_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleReStart110_ptr GUILD_BATTLE__CNormalGuildBattleReStart110_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleReStart110_clbk GUILD_BATTLE__CNormalGuildBattleReStart110_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleRewardGuildBattleMoney112_ptr GUILD_BATTLE__CNormalGuildBattleRewardGuildBattleMoney112_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleRewardGuildBattleMoney112_clbk GUILD_BATTLE__CNormalGuildBattleRewardGuildBattleMoney112_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleRewardItem114_ptr GUILD_BATTLE__CNormalGuildBattleRewardItem114_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleRewardItem114_clbk GUILD_BATTLE__CNormalGuildBattleRewardItem114_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleSave116_ptr GUILD_BATTLE__CNormalGuildBattleSave116_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleSave116_clbk GUILD_BATTLE__CNormalGuildBattleSave116_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleSendDrawResult118_ptr GUILD_BATTLE__CNormalGuildBattleSendDrawResult118_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleSendDrawResult118_clbk GUILD_BATTLE__CNormalGuildBattleSendDrawResult118_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleSendGoalMsg120_ptr GUILD_BATTLE__CNormalGuildBattleSendGoalMsg120_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleSendGoalMsg120_clbk GUILD_BATTLE__CNormalGuildBattleSendGoalMsg120_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleSendKillInform122_ptr GUILD_BATTLE__CNormalGuildBattleSendKillInform122_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleSendKillInform122_clbk GUILD_BATTLE__CNormalGuildBattleSendKillInform122_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleSendWebAddScheduleInfo124_ptr GUILD_BATTLE__CNormalGuildBattleSendWebAddScheduleInfo124_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleSendWebAddScheduleInfo124_clbk GUILD_BATTLE__CNormalGuildBattleSendWebAddScheduleInfo124_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleSendWebBattleEndInfo126_ptr GUILD_BATTLE__CNormalGuildBattleSendWebBattleEndInfo126_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleSendWebBattleEndInfo126_clbk GUILD_BATTLE__CNormalGuildBattleSendWebBattleEndInfo126_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleSendWebBattleStartInfo128_ptr GUILD_BATTLE__CNormalGuildBattleSendWebBattleStartInfo128_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleSendWebBattleStartInfo128_clbk GUILD_BATTLE__CNormalGuildBattleSendWebBattleStartInfo128_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleSendWinLoseResult130_ptr GUILD_BATTLE__CNormalGuildBattleSendWinLoseResult130_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleSendWinLoseResult130_clbk GUILD_BATTLE__CNormalGuildBattleSendWinLoseResult130_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleSetGotoRegenStart132_ptr GUILD_BATTLE__CNormalGuildBattleSetGotoRegenStart132_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleSetGotoRegenStart132_clbk GUILD_BATTLE__CNormalGuildBattleSetGotoRegenStart132_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleSetReadyState134_ptr GUILD_BATTLE__CNormalGuildBattleSetReadyState134_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleSetReadyState134_clbk GUILD_BATTLE__CNormalGuildBattleSetReadyState134_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleStart136_ptr GUILD_BATTLE__CNormalGuildBattleStart136_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleStart136_clbk GUILD_BATTLE__CNormalGuildBattleStart136_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleTakeGravityStone138_ptr GUILD_BATTLE__CNormalGuildBattleTakeGravityStone138_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleTakeGravityStone138_clbk GUILD_BATTLE__CNormalGuildBattleTakeGravityStone138_user(nullptr);
            
            
            Info::GUILD_BATTLE__CNormalGuildBattledtor_CNormalGuildBattle142_ptr GUILD_BATTLE__CNormalGuildBattledtor_CNormalGuildBattle142_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattledtor_CNormalGuildBattle142_clbk GUILD_BATTLE__CNormalGuildBattledtor_CNormalGuildBattle142_user(nullptr);
            
            void GUILD_BATTLE__CNormalGuildBattleAddComplete2_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, char byRet)
            {
               GUILD_BATTLE__CNormalGuildBattleAddComplete2_user(_this, byRet, GUILD_BATTLE__CNormalGuildBattleAddComplete2_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleAskJoin4_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, int n, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
            {
               GUILD_BATTLE__CNormalGuildBattleAskJoin4_user(_this, n, dwGuildSerial, dwCharacSerial, GUILD_BATTLE__CNormalGuildBattleAskJoin4_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleAskJoin6_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleAskJoin6_user(_this, GUILD_BATTLE__CNormalGuildBattleAskJoin6_next);
            };
            
            void GUILD_BATTLE__CNormalGuildBattlector_CNormalGuildBattle8_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, unsigned int dwID)
            {
               GUILD_BATTLE__CNormalGuildBattlector_CNormalGuildBattle8_user(_this, dwID, GUILD_BATTLE__CNormalGuildBattlector_CNormalGuildBattle8_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleCleanUpBattle10_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleCleanUpBattle10_user(_this, GUILD_BATTLE__CNormalGuildBattleCleanUpBattle10_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleClear12_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleClear12_user(_this, GUILD_BATTLE__CNormalGuildBattleClear12_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleClearDBRecord14_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleClearDBRecord14_user(_this, GUILD_BATTLE__CNormalGuildBattleClearDBRecord14_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleCreateLogFile16_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleCreateLogFile16_user(_this, GUILD_BATTLE__CNormalGuildBattleCreateLogFile16_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleCreateLogger18_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleCreateLogger18_user(_this, GUILD_BATTLE__CNormalGuildBattleCreateLogger18_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleDecideColorInx20_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleDecideColorInx20_user(_this, GUILD_BATTLE__CNormalGuildBattleDecideColorInx20_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleDecideWin22_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleDecideWin22_user(_this, GUILD_BATTLE__CNormalGuildBattleDecideWin22_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleDividePvpPoint24_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleDividePvpPoint24_user(_this, GUILD_BATTLE__CNormalGuildBattleDividePvpPoint24_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleDropGravityStone26_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, unsigned int dwCharacSerial)
            {
               return GUILD_BATTLE__CNormalGuildBattleDropGravityStone26_user(_this, dwCharacSerial, GUILD_BATTLE__CNormalGuildBattleDropGravityStone26_next);
            };
            struct GUILD_BATTLE::CNormalGuildBattleGuild* GUILD_BATTLE__CNormalGuildBattleGet1P28_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGet1P28_user(_this, GUILD_BATTLE__CNormalGuildBattleGet1P28_next);
            };
            struct GUILD_BATTLE::CNormalGuildBattleGuild* GUILD_BATTLE__CNormalGuildBattleGet2P30_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGet2P30_user(_this, GUILD_BATTLE__CNormalGuildBattleGet2P30_next);
            };
            struct GUILD_BATTLE::CNormalGuildBattleGuild* GUILD_BATTLE__CNormalGuildBattleGetBlue32_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGetBlue32_user(_this, GUILD_BATTLE__CNormalGuildBattleGetBlue32_next);
            };
            struct GUILD_BATTLE::CNormalGuildBattleField* GUILD_BATTLE__CNormalGuildBattleGetField34_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGetField34_user(_this, GUILD_BATTLE__CNormalGuildBattleGetField34_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleGetGravityStone36_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, uint16_t wIndex, unsigned int dwObjSerial, unsigned int dwCharacSerial)
            {
               return GUILD_BATTLE__CNormalGuildBattleGetGravityStone36_user(_this, wIndex, dwObjSerial, dwCharacSerial, GUILD_BATTLE__CNormalGuildBattleGetGravityStone36_next);
            };
            struct GUILD_BATTLE::CNormalGuildBattleGuild* GUILD_BATTLE__CNormalGuildBattleGetGuild38_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, unsigned int dwGuildSerial)
            {
               return GUILD_BATTLE__CNormalGuildBattleGetGuild38_user(_this, dwGuildSerial, GUILD_BATTLE__CNormalGuildBattleGetGuild38_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleGetGuildBattleNumber40_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGetGuildBattleNumber40_user(_this, GUILD_BATTLE__CNormalGuildBattleGetGuildBattleNumber40_next);
            };
            unsigned int GUILD_BATTLE__CNormalGuildBattleGetID42_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGetID42_user(_this, GUILD_BATTLE__CNormalGuildBattleGetID42_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleGetInfo44_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, struct _guild_battle_current_battle_info_result_zocl* kInfo)
            {
               return GUILD_BATTLE__CNormalGuildBattleGetInfo44_user(_this, kInfo, GUILD_BATTLE__CNormalGuildBattleGetInfo44_next);
            };
            struct GUILD_BATTLE::CNormalGuildBattleLogger* GUILD_BATTLE__CNormalGuildBattleGetLogger46_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGetLogger46_user(_this, GUILD_BATTLE__CNormalGuildBattleGetLogger46_next);
            };
            int GUILD_BATTLE__CNormalGuildBattleGetObjType48_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGetObjType48_user(_this, GUILD_BATTLE__CNormalGuildBattleGetObjType48_next);
            };
            struct GUILD_BATTLE::CNormalGuildBattleGuild* GUILD_BATTLE__CNormalGuildBattleGetRed50_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGetRed50_user(_this, GUILD_BATTLE__CNormalGuildBattleGetRed50_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleGoal52_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, unsigned int dwCharacSerial, int iPortalInx)
            {
               return GUILD_BATTLE__CNormalGuildBattleGoal52_user(_this, dwCharacSerial, iPortalInx, GUILD_BATTLE__CNormalGuildBattleGoal52_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLog54_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLog54_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLog54_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogNotifyWeb56_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, struct _qry_case_guild_battel_result_log* Sheet)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogNotifyWeb56_user(_this, Sheet, GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogNotifyWeb56_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogPushDBLog58_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, struct _qry_case_guild_battel_result_log* Sheet, struct GUILD_BATTLE::CNormalGuildBattleGuildMember* pkTopGoalMember, struct GUILD_BATTLE::CNormalGuildBattleGuildMember* pkTopKillMember)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogPushDBLog58_user(_this, Sheet, pkTopGoalMember, pkTopKillMember, GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogPushDBLog58_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleInit60_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, struct CGuild* pk1P, struct CGuild* pk2P, struct GUILD_BATTLE::CNormalGuildBattleField* pkField, char byNumber, struct GUILD_BATTLE::CNormalGuildBattleStateList* pkStateList)
            {
               GUILD_BATTLE__CNormalGuildBattleInit60_user(_this, pk1P, pk2P, pkField, byNumber, pkStateList, GUILD_BATTLE__CNormalGuildBattleInit60_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleInit62_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, bool bToday, unsigned int uiDayID, unsigned int dwID, unsigned int dwP1GuildSerial, unsigned int dwP2GuildSerial, unsigned int dwMapID, char byNumber)
            {
               return GUILD_BATTLE__CNormalGuildBattleInit62_user(_this, bToday, uiDayID, dwID, dwP1GuildSerial, dwP2GuildSerial, dwMapID, byNumber, GUILD_BATTLE__CNormalGuildBattleInit62_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleIsEmpty64_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleIsEmpty64_user(_this, GUILD_BATTLE__CNormalGuildBattleIsEmpty64_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleIsInBattle66_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleIsInBattle66_user(_this, GUILD_BATTLE__CNormalGuildBattleIsInBattle66_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleIsInBattleRegenState68_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleIsInBattleRegenState68_user(_this, GUILD_BATTLE__CNormalGuildBattleIsInBattleRegenState68_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleIsMemberGuild70_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, unsigned int dwGuildSerial)
            {
               return GUILD_BATTLE__CNormalGuildBattleIsMemberGuild70_user(_this, dwGuildSerial, GUILD_BATTLE__CNormalGuildBattleIsMemberGuild70_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleIsProc72_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleIsProc72_user(_this, GUILD_BATTLE__CNormalGuildBattleIsProc72_next);
            };
            int GUILD_BATTLE__CNormalGuildBattleIsReStart74_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
            {
               return GUILD_BATTLE__CNormalGuildBattleIsReStart74_user(_this, dwGuildSerial, dwCharacSerial, GUILD_BATTLE__CNormalGuildBattleIsReStart74_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleIsReadyOrCountState76_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleIsReadyOrCountState76_user(_this, GUILD_BATTLE__CNormalGuildBattleIsReadyOrCountState76_next);
            };
            int GUILD_BATTLE__CNormalGuildBattleJoin78_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
            {
               return GUILD_BATTLE__CNormalGuildBattleJoin78_user(_this, dwGuildSerial, dwCharacSerial, GUILD_BATTLE__CNormalGuildBattleJoin78_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleJudgeBattle80_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleJudgeBattle80_user(_this, GUILD_BATTLE__CNormalGuildBattleJudgeBattle80_next);
            };
            int GUILD_BATTLE__CNormalGuildBattleKill82_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, unsigned int dwSrcCharacSerial, unsigned int dwDestCharacSerial)
            {
               return GUILD_BATTLE__CNormalGuildBattleKill82_user(_this, dwSrcCharacSerial, dwDestCharacSerial, GUILD_BATTLE__CNormalGuildBattleKill82_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleLeaveGuild84_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, struct CPlayer* pkPlayer)
            {
               return GUILD_BATTLE__CNormalGuildBattleLeaveGuild84_user(_this, pkPlayer, GUILD_BATTLE__CNormalGuildBattleLeaveGuild84_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleLogIn86_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, int n, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
            {
               GUILD_BATTLE__CNormalGuildBattleLogIn86_user(_this, n, dwGuildSerial, dwCharacSerial, GUILD_BATTLE__CNormalGuildBattleLogIn86_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleNetClose88_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, unsigned int dwCharacSerial, struct CPlayer* pkPlayer)
            {
               return GUILD_BATTLE__CNormalGuildBattleNetClose88_user(_this, dwCharacSerial, pkPlayer, GUILD_BATTLE__CNormalGuildBattleNetClose88_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleNotifyAllProcessEnd90_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleNotifyAllProcessEnd90_user(_this, GUILD_BATTLE__CNormalGuildBattleNotifyAllProcessEnd90_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleNotifyBallPosition92_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleNotifyBallPosition92_user(_this, GUILD_BATTLE__CNormalGuildBattleNotifyBallPosition92_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleNotifyBattleResult94_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, char byResult)
            {
               GUILD_BATTLE__CNormalGuildBattleNotifyBattleResult94_user(_this, byResult, GUILD_BATTLE__CNormalGuildBattleNotifyBattleResult94_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleNotifyBeforeStart96_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleNotifyBeforeStart96_user(_this, GUILD_BATTLE__CNormalGuildBattleNotifyBeforeStart96_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleNotifyCommitteeMemberPosition98_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, unsigned int dwGuildSerial, unsigned int dwChracSerial)
            {
               GUILD_BATTLE__CNormalGuildBattleNotifyCommitteeMemberPosition98_user(_this, dwGuildSerial, dwChracSerial, GUILD_BATTLE__CNormalGuildBattleNotifyCommitteeMemberPosition98_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleNotifyDestoryBall100_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, unsigned int dwOwnerSerial)
            {
               GUILD_BATTLE__CNormalGuildBattleNotifyDestoryBall100_user(_this, dwOwnerSerial, GUILD_BATTLE__CNormalGuildBattleNotifyDestoryBall100_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleNotifyPassGravityStoneLimitTime102_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleNotifyPassGravityStoneLimitTime102_user(_this, GUILD_BATTLE__CNormalGuildBattleNotifyPassGravityStoneLimitTime102_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleProcess104_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleProcess104_user(_this, GUILD_BATTLE__CNormalGuildBattleProcess104_next);
            };
            void GUILD_BATTLE__CNormalGuildBattlePushDQSDrawRank106_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattlePushDQSDrawRank106_user(_this, GUILD_BATTLE__CNormalGuildBattlePushDQSDrawRank106_next);
            };
            void GUILD_BATTLE__CNormalGuildBattlePushDQSWinLoseRank108_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattlePushDQSWinLoseRank108_user(_this, GUILD_BATTLE__CNormalGuildBattlePushDQSWinLoseRank108_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleReStart110_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, struct CPlayer* pkPlayer, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
            {
               return GUILD_BATTLE__CNormalGuildBattleReStart110_user(_this, pkPlayer, dwGuildSerial, dwCharacSerial, GUILD_BATTLE__CNormalGuildBattleReStart110_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleRewardGuildBattleMoney112_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleRewardGuildBattleMoney112_user(_this, GUILD_BATTLE__CNormalGuildBattleRewardGuildBattleMoney112_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleRewardItem114_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleRewardItem114_user(_this, GUILD_BATTLE__CNormalGuildBattleRewardItem114_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleSave116_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleSave116_user(_this, GUILD_BATTLE__CNormalGuildBattleSave116_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleSendDrawResult118_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleSendDrawResult118_user(_this, GUILD_BATTLE__CNormalGuildBattleSendDrawResult118_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleSendGoalMsg120_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, bool b1P, char* wszGuildName, struct CPlayer* pkPlayer, int iPortalInx)
            {
               GUILD_BATTLE__CNormalGuildBattleSendGoalMsg120_user(_this, b1P, wszGuildName, pkPlayer, iPortalInx, GUILD_BATTLE__CNormalGuildBattleSendGoalMsg120_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleSendKillInform122_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleSendKillInform122_user(_this, GUILD_BATTLE__CNormalGuildBattleSendKillInform122_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleSendWebAddScheduleInfo124_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleSendWebAddScheduleInfo124_user(_this, GUILD_BATTLE__CNormalGuildBattleSendWebAddScheduleInfo124_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleSendWebBattleEndInfo126_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleSendWebBattleEndInfo126_user(_this, GUILD_BATTLE__CNormalGuildBattleSendWebBattleEndInfo126_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleSendWebBattleStartInfo128_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleSendWebBattleStartInfo128_user(_this, GUILD_BATTLE__CNormalGuildBattleSendWebBattleStartInfo128_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleSendWinLoseResult130_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleSendWinLoseResult130_user(_this, GUILD_BATTLE__CNormalGuildBattleSendWinLoseResult130_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleSetGotoRegenStart132_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleSetGotoRegenStart132_user(_this, GUILD_BATTLE__CNormalGuildBattleSetGotoRegenStart132_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleSetReadyState134_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleSetReadyState134_user(_this, GUILD_BATTLE__CNormalGuildBattleSetReadyState134_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleStart136_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, struct CPlayer* pkPlayer, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
            {
               return GUILD_BATTLE__CNormalGuildBattleStart136_user(_this, pkPlayer, dwGuildSerial, dwCharacSerial, GUILD_BATTLE__CNormalGuildBattleStart136_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleTakeGravityStone138_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this, int iPortalInx, unsigned int dwCharacSerial)
            {
               return GUILD_BATTLE__CNormalGuildBattleTakeGravityStone138_user(_this, iPortalInx, dwCharacSerial, GUILD_BATTLE__CNormalGuildBattleTakeGravityStone138_next);
            };
            
            void GUILD_BATTLE__CNormalGuildBattledtor_CNormalGuildBattle142_wrapper(struct GUILD_BATTLE::CNormalGuildBattle* _this)
            {
               GUILD_BATTLE__CNormalGuildBattledtor_CNormalGuildBattle142_user(_this, GUILD_BATTLE__CNormalGuildBattledtor_CNormalGuildBattle142_next);
            };
            
            ::std::array<hook_record, 70> CNormalGuildBattle_functions = 
            {
                _hook_record {
                    (LPVOID)0x1403e3910L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleAddComplete2_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleAddComplete2_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleAddComplete2_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)(char))&GUILD_BATTLE::CNormalGuildBattle::AddComplete)
                },
                _hook_record {
                    (LPVOID)0x1403e3f40L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleAskJoin4_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleAskJoin4_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleAskJoin4_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)(int, unsigned int, unsigned int))&GUILD_BATTLE::CNormalGuildBattle::AskJoin)
                },
                _hook_record {
                    (LPVOID)0x1403e49e0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleAskJoin6_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleAskJoin6_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleAskJoin6_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::AskJoin)
                },
                _hook_record {
                    (LPVOID)0x1403e2e40L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattlector_CNormalGuildBattle8_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattlector_CNormalGuildBattle8_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattlector_CNormalGuildBattle8_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)(unsigned int))&GUILD_BATTLE::CNormalGuildBattle::ctor_CNormalGuildBattle)
                },
                _hook_record {
                    (LPVOID)0x1403e6fe0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleCleanUpBattle10_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleCleanUpBattle10_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleCleanUpBattle10_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::CleanUpBattle)
                },
                _hook_record {
                    (LPVOID)0x1403e3660L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleClear12_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleClear12_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleClear12_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::Clear)
                },
                _hook_record {
                    (LPVOID)0x1403e3740L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleClearDBRecord14_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleClearDBRecord14_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleClearDBRecord14_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::ClearDBRecord)
                },
                _hook_record {
                    (LPVOID)0x1403e7040L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleCreateLogFile16_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleCreateLogFile16_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleCreateLogFile16_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::CreateLogFile)
                },
                _hook_record {
                    (LPVOID)0x1403d9020L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleCreateLogger18_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleCreateLogger18_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleCreateLogger18_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::CreateLogger)
                },
                _hook_record {
                    (LPVOID)0x1403e3e20L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleDecideColorInx20_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleDecideColorInx20_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleDecideColorInx20_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::DecideColorInx)
                },
                _hook_record {
                    (LPVOID)0x1403e76f0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleDecideWin22_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleDecideWin22_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleDecideWin22_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::DecideWin)
                },
                _hook_record {
                    (LPVOID)0x1403e6490L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleDividePvpPoint24_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleDividePvpPoint24_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleDividePvpPoint24_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::DividePvpPoint)
                },
                _hook_record {
                    (LPVOID)0x1403e5830L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleDropGravityStone26_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleDropGravityStone26_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleDropGravityStone26_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattle::*)(unsigned int))&GUILD_BATTLE::CNormalGuildBattle::DropGravityStone)
                },
                _hook_record {
                    (LPVOID)0x1403d9360L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGet1P28_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGet1P28_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGet1P28_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CNormalGuildBattleGuild*(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::Get1P)
                },
                _hook_record {
                    (LPVOID)0x1403d9380L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGet2P30_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGet2P30_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGet2P30_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CNormalGuildBattleGuild*(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::Get2P)
                },
                _hook_record {
                    (LPVOID)0x1403f30e0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetBlue32_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetBlue32_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGetBlue32_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CNormalGuildBattleGuild*(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::GetBlue)
                },
                _hook_record {
                    (LPVOID)0x1400a6a80L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetField34_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetField34_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGetField34_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CNormalGuildBattleField*(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::GetField)
                },
                _hook_record {
                    (LPVOID)0x1403e4b80L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetGravityStone36_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetGravityStone36_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGetGravityStone36_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattle::*)(uint16_t, unsigned int, unsigned int))&GUILD_BATTLE::CNormalGuildBattle::GetGravityStone)
                },
                _hook_record {
                    (LPVOID)0x1403e3ab0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetGuild38_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetGuild38_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGetGuild38_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CNormalGuildBattleGuild*(GUILD_BATTLE::CNormalGuildBattle::*)(unsigned int))&GUILD_BATTLE::CNormalGuildBattle::GetGuild)
                },
                _hook_record {
                    (LPVOID)0x1403d93a0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetGuildBattleNumber40_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetGuildBattleNumber40_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGetGuildBattleNumber40_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::GetGuildBattleNumber)
                },
                _hook_record {
                    (LPVOID)0x1403d9340L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetID42_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetID42_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGetID42_wrapper),
                    (LPVOID)cast_pointer_function((unsigned int(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::GetID)
                },
                _hook_record {
                    (LPVOID)0x1403e39a0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetInfo44_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetInfo44_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGetInfo44_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattle::*)(struct _guild_battle_current_battle_info_result_zocl*))&GUILD_BATTLE::CNormalGuildBattle::GetInfo)
                },
                _hook_record {
                    (LPVOID)0x1403f3100L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetLogger46_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetLogger46_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGetLogger46_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CNormalGuildBattleLogger*(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::GetLogger)
                },
                _hook_record {
                    (LPVOID)0x1403eb090L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetObjType48_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetObjType48_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGetObjType48_wrapper),
                    (LPVOID)cast_pointer_function((int(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::GetObjType)
                },
                _hook_record {
                    (LPVOID)0x1403f30c0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetRed50_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGetRed50_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGetRed50_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CNormalGuildBattleGuild*(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::GetRed)
                },
                _hook_record {
                    (LPVOID)0x1403e4ca0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGoal52_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGoal52_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGoal52_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattle::*)(unsigned int, int))&GUILD_BATTLE::CNormalGuildBattle::Goal)
                },
                _hook_record {
                    (LPVOID)0x1403e6e10L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLog54_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLog54_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLog54_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::GuildBattleResultLog)
                },
                _hook_record {
                    (LPVOID)0x1403e83e0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogNotifyWeb56_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogNotifyWeb56_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogNotifyWeb56_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)(struct _qry_case_guild_battel_result_log*))&GUILD_BATTLE::CNormalGuildBattle::GuildBattleResultLogNotifyWeb)
                },
                _hook_record {
                    (LPVOID)0x1403e7d30L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogPushDBLog58_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogPushDBLog58_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildBattleResultLogPushDBLog58_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)(struct _qry_case_guild_battel_result_log*, struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, struct GUILD_BATTLE::CNormalGuildBattleGuildMember*))&GUILD_BATTLE::CNormalGuildBattle::GuildBattleResultLogPushDBLog)
                },
                _hook_record {
                    (LPVOID)0x1403e30d0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleInit60_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleInit60_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleInit60_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)(struct CGuild*, struct CGuild*, struct GUILD_BATTLE::CNormalGuildBattleField*, char, struct GUILD_BATTLE::CNormalGuildBattleStateList*))&GUILD_BATTLE::CNormalGuildBattle::Init)
                },
                _hook_record {
                    (LPVOID)0x1403e3180L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleInit62_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleInit62_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleInit62_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattle::*)(bool, unsigned int, unsigned int, unsigned int, unsigned int, unsigned int, char))&GUILD_BATTLE::CNormalGuildBattle::Init)
                },
                _hook_record {
                    (LPVOID)0x1403d9630L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleIsEmpty64_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleIsEmpty64_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleIsEmpty64_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::IsEmpty)
                },
                _hook_record {
                    (LPVOID)0x14007c0a0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleIsInBattle66_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleIsInBattle66_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleIsInBattle66_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::IsInBattle)
                },
                _hook_record {
                    (LPVOID)0x1403d94b0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleIsInBattleRegenState68_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleIsInBattleRegenState68_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleIsInBattleRegenState68_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::IsInBattleRegenState)
                },
                _hook_record {
                    (LPVOID)0x1403d9690L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleIsMemberGuild70_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleIsMemberGuild70_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleIsMemberGuild70_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattle::*)(unsigned int))&GUILD_BATTLE::CNormalGuildBattle::IsMemberGuild)
                },
                _hook_record {
                    (LPVOID)0x1403d9200L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleIsProc72_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleIsProc72_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleIsProc72_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::IsProc)
                },
                _hook_record {
                    (LPVOID)0x1403e41a0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleIsReStart74_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleIsReStart74_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleIsReStart74_wrapper),
                    (LPVOID)cast_pointer_function((int(GUILD_BATTLE::CNormalGuildBattle::*)(unsigned int, unsigned int))&GUILD_BATTLE::CNormalGuildBattle::IsReStart)
                },
                _hook_record {
                    (LPVOID)0x14007bfd0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleIsReadyOrCountState76_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleIsReadyOrCountState76_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleIsReadyOrCountState76_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::IsReadyOrCountState)
                },
                _hook_record {
                    (LPVOID)0x1403e3b90L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleJoin78_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleJoin78_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleJoin78_wrapper),
                    (LPVOID)cast_pointer_function((int(GUILD_BATTLE::CNormalGuildBattle::*)(unsigned int, unsigned int))&GUILD_BATTLE::CNormalGuildBattle::Join)
                },
                _hook_record {
                    (LPVOID)0x1403e6400L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleJudgeBattle80_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleJudgeBattle80_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleJudgeBattle80_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::JudgeBattle)
                },
                _hook_record {
                    (LPVOID)0x1403e5c30L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleKill82_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleKill82_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleKill82_wrapper),
                    (LPVOID)cast_pointer_function((int(GUILD_BATTLE::CNormalGuildBattle::*)(unsigned int, unsigned int))&GUILD_BATTLE::CNormalGuildBattle::Kill)
                },
                _hook_record {
                    (LPVOID)0x1403e6070L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleLeaveGuild84_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleLeaveGuild84_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleLeaveGuild84_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattle::*)(struct CPlayer*))&GUILD_BATTLE::CNormalGuildBattle::LeaveGuild)
                },
                _hook_record {
                    (LPVOID)0x1403e4050L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleLogIn86_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleLogIn86_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleLogIn86_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)(int, unsigned int, unsigned int))&GUILD_BATTLE::CNormalGuildBattle::LogIn)
                },
                _hook_record {
                    (LPVOID)0x1403e5940L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleNetClose88_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleNetClose88_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleNetClose88_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattle::*)(unsigned int, struct CPlayer*))&GUILD_BATTLE::CNormalGuildBattle::NetClose)
                },
                _hook_record {
                    (LPVOID)0x1403e57a0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleNotifyAllProcessEnd90_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleNotifyAllProcessEnd90_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleNotifyAllProcessEnd90_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::NotifyAllProcessEnd)
                },
                _hook_record {
                    (LPVOID)0x1403e53e0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleNotifyBallPosition92_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleNotifyBallPosition92_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleNotifyBallPosition92_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::NotifyBallPosition)
                },
                _hook_record {
                    (LPVOID)0x1403e6a90L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleNotifyBattleResult94_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleNotifyBattleResult94_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleNotifyBattleResult94_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)(char))&GUILD_BATTLE::CNormalGuildBattle::NotifyBattleResult)
                },
                _hook_record {
                    (LPVOID)0x1403e50e0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleNotifyBeforeStart96_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleNotifyBeforeStart96_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleNotifyBeforeStart96_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::NotifyBeforeStart)
                },
                _hook_record {
                    (LPVOID)0x1403e55b0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleNotifyCommitteeMemberPosition98_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleNotifyCommitteeMemberPosition98_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleNotifyCommitteeMemberPosition98_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)(unsigned int, unsigned int))&GUILD_BATTLE::CNormalGuildBattle::NotifyCommitteeMemberPosition)
                },
                _hook_record {
                    (LPVOID)0x1403e5510L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleNotifyDestoryBall100_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleNotifyDestoryBall100_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleNotifyDestoryBall100_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)(unsigned int))&GUILD_BATTLE::CNormalGuildBattle::NotifyDestoryBall)
                },
                _hook_record {
                    (LPVOID)0x1403e5700L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleNotifyPassGravityStoneLimitTime102_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleNotifyPassGravityStoneLimitTime102_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleNotifyPassGravityStoneLimitTime102_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::NotifyPassGravityStoneLimitTime)
                },
                _hook_record {
                    (LPVOID)0x1403d92f0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleProcess104_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleProcess104_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleProcess104_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::Process)
                },
                _hook_record {
                    (LPVOID)0x1403e7b70L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattlePushDQSDrawRank106_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattlePushDQSDrawRank106_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattlePushDQSDrawRank106_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::PushDQSDrawRank)
                },
                _hook_record {
                    (LPVOID)0x1403e7c40L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattlePushDQSWinLoseRank108_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattlePushDQSWinLoseRank108_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattlePushDQSWinLoseRank108_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::PushDQSWinLoseRank)
                },
                _hook_record {
                    (LPVOID)0x1403e42a0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleReStart110_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleReStart110_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleReStart110_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattle::*)(struct CPlayer*, unsigned int, unsigned int))&GUILD_BATTLE::CNormalGuildBattle::ReStart)
                },
                _hook_record {
                    (LPVOID)0x1403e6840L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleRewardGuildBattleMoney112_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleRewardGuildBattleMoney112_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleRewardGuildBattleMoney112_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::RewardGuildBattleMoney)
                },
                _hook_record {
                    (LPVOID)0x1403e6960L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleRewardItem114_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleRewardItem114_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleRewardItem114_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::RewardItem)
                },
                _hook_record {
                    (LPVOID)0x1403e37c0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSave116_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSave116_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleSave116_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::Save)
                },
                _hook_record {
                    (LPVOID)0x1403e78e0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSendDrawResult118_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSendDrawResult118_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleSendDrawResult118_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::SendDrawResult)
                },
                _hook_record {
                    (LPVOID)0x1403e71c0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSendGoalMsg120_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSendGoalMsg120_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleSendGoalMsg120_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)(bool, char*, struct CPlayer*, int))&GUILD_BATTLE::CNormalGuildBattle::SendGoalMsg)
                },
                _hook_record {
                    (LPVOID)0x1403e7590L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSendKillInform122_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSendKillInform122_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleSendKillInform122_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::SendKillInform)
                },
                _hook_record {
                    (LPVOID)0x1403e8690L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSendWebAddScheduleInfo124_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSendWebAddScheduleInfo124_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleSendWebAddScheduleInfo124_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::SendWebAddScheduleInfo)
                },
                _hook_record {
                    (LPVOID)0x1403e6d50L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSendWebBattleEndInfo126_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSendWebBattleEndInfo126_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleSendWebBattleEndInfo126_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::SendWebBattleEndInfo)
                },
                _hook_record {
                    (LPVOID)0x1403e6c90L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSendWebBattleStartInfo128_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSendWebBattleStartInfo128_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleSendWebBattleStartInfo128_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::SendWebBattleStartInfo)
                },
                _hook_record {
                    (LPVOID)0x1403e79f0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSendWinLoseResult130_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSendWinLoseResult130_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleSendWinLoseResult130_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::SendWinLoseResult)
                },
                _hook_record {
                    (LPVOID)0x1403f3240L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSetGotoRegenStart132_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSetGotoRegenStart132_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleSetGotoRegenStart132_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::SetGotoRegenStart)
                },
                _hook_record {
                    (LPVOID)0x1403e3b30L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSetReadyState134_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleSetReadyState134_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleSetReadyState134_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::SetReadyState)
                },
                _hook_record {
                    (LPVOID)0x1403e4640L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleStart136_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleStart136_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleStart136_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattle::*)(struct CPlayer*, unsigned int, unsigned int))&GUILD_BATTLE::CNormalGuildBattle::Start)
                },
                _hook_record {
                    (LPVOID)0x1403e4a60L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleTakeGravityStone138_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleTakeGravityStone138_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleTakeGravityStone138_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattle::*)(int, unsigned int))&GUILD_BATTLE::CNormalGuildBattle::TakeGravityStone)
                },
                _hook_record {
                    (LPVOID)0x1403e2fb0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattledtor_CNormalGuildBattle142_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattledtor_CNormalGuildBattle142_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattledtor_CNormalGuildBattle142_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattle::*)())&GUILD_BATTLE::CNormalGuildBattle::dtor_CNormalGuildBattle)
                },
            };
        }; // end namespace Detail
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE
