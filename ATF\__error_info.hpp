// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct __error_info
    {
        char szFileName[128];
        char szQuestTitle[128];
        int nLine;
        char szNextStr[1280];
        char szLastSection[128];
        char szLastCommand[128];
        char szLoadErrMsg[256];
    public:
        void SetFileName(char* pszFileName);
        void SetQuestTitle(char* pszTitle);
        __error_info();
        void ctor___error_info();
        void init();
    };    
    static_assert(ATF::checkSize<__error_info, 2052>(), "__error_info");
END_ATF_NAMESPACE
