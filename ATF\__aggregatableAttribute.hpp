// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace vc_attributes
    {
        template<>
        struct aggregatableAttribute
        {
            template<>
            enum type_e
            {
                never = 0x0,
                allowed = 0x1,
                always = 0x2,
            };
            type_e type;
        };
    }; // end namespace vc_attributes
END_ATF_NAMESPACE
