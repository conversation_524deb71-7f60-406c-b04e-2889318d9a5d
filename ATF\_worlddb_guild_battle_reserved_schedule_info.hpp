// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _worlddb_guild_battle_reserved_schedule_info
    {
        struct __list
        {
            unsigned int dw1PGuildSerial;
            char wsz1PName[17];
            char by1PRace;
            unsigned int dw2PGuildSerial;
            char wsz2PName[17];
            char by2PRace;
            char byStartHour;
            char byStartMin;
            char byEndHour;
            char byEndMin;
        };
        unsigned __int16 wCount;
        __list list[46];
    };
END_ATF_NAMESPACE
