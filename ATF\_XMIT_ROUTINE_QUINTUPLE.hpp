// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    const struct _XMIT_ROUTINE_QUINTUPLE
    {
        void (WINAPIV *pfnTranslateToXmit)(_MIDL_STUB_MESSAGE *);
        void (WINAPIV *pfnTranslateFromXmit)(_MIDL_STUB_MESSAGE *);
        void (WINAPIV *pfnFreeXmit)(_MIDL_STUB_MESSAGE *);
        void (WINAPIV *pfnFreeInst)(_MIDL_STUB_MESSAGE *);
    };
END_ATF_NAMESPACE
