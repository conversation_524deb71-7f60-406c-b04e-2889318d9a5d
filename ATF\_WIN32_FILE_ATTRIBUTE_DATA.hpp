// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_FILETIME.hpp>


START_ATF_NAMESPACE
    struct _WIN32_FILE_ATTRIBUTE_DATA
    {
        unsigned int dwFileAttributes;
        _FILETIME ftCreationTime;
        _FILETIME ftLastAccessTime;
        _FILETIME ftLastWriteTime;
        unsigned int nFileSizeHigh;
        unsigned int nFileSizeLow;
    };
END_ATF_NAMESPACE
