#include <_personal_amine_make_storage_zocl.hpp>


START_ATF_NAMESPACE
    _personal_amine_make_storage_zocl::_personal_amine_make_storage_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_amine_make_storage_zocl*);
        (org_ptr(0x1402e1c80L))(this);
    };
    void _personal_amine_make_storage_zocl::ctor__personal_amine_make_storage_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_amine_make_storage_zocl*);
        (org_ptr(0x1402e1c80L))(this);
    };
    int _personal_amine_make_storage_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _personal_amine_make_storage_zocl*);
        return (org_ptr(0x1402e1ca0L))(this);
    };
END_ATF_NAMESPACE
