// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _worlddb_guild_member_info
    {
        struct __guild_member_info
        {
            unsigned int dwSerial;
            char wszName[17];
            char byClassInGuild;
            char byLv;
            unsigned int dwPvpPoint;
            unsigned __int16 wRank;
        };
        unsigned __int16 wMemberCount;
        __guild_member_info MemberData[50];
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_worlddb_guild_member_info, 1604>(), "_worlddb_guild_member_info");
END_ATF_NAMESPACE
