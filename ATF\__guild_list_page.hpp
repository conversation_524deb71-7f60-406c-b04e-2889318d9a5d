// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct __guild_list_page
    {
        struct __list
        {
            char byGrade;
            char wszGuildName[17];
            char wszMasterName[17];
        };
        char byListCnt;
        __list GuildList[4];
    public:
        __guild_list_page();
        void ctor___guild_list_page();
    };
END_ATF_NAMESPACE
