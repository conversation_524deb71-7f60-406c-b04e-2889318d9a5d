// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_dummy_position.hpp>


START_ATF_NAMESPACE
    struct __dummy_block
    {
        char *pszBlockName;
        int nSubDummyNum;
        _dummy_position *pSubDummy[32];
    public:
        __dummy_block();
        void ctor___dummy_block();
    };
END_ATF_NAMESPACE
