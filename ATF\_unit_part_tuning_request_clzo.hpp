// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_tuning_data.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _unit_part_tuning_request_clzo
    {
        char bySlotIndex;
        char byTuningNum;
        int bUseNPCLinkIntem;
        _tuning_data TuningList[6];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
