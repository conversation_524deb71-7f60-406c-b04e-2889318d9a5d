#include <GUILD_BATTLE__CNormalGuildBattleGuildDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Detail
        {
            Info::GUILD_BATTLE__CNormalGuildBattleGuildAskJoin2_ptr GUILD_BATTLE__CNormalGuildBattleGuildAskJoin2_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildAskJoin2_clbk GUILD_BATTLE__CNormalGuildBattleGuildAskJoin2_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildAskJoin4_ptr GUILD_BATTLE__CNormalGuildBattleGuildAskJoin4_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildAskJoin4_clbk GUILD_BATTLE__CNormalGuildBattleGuildAskJoin4_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildAskJoin6_ptr GUILD_BATTLE__CNormalGuildBattleGuildAskJoin6_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildAskJoin6_clbk GUILD_BATTLE__CNormalGuildBattleGuildAskJoin6_user(nullptr);
            
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildctor_CNormalGuildBattleGuild8_ptr GUILD_BATTLE__CNormalGuildBattleGuildctor_CNormalGuildBattleGuild8_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildctor_CNormalGuildBattleGuild8_clbk GUILD_BATTLE__CNormalGuildBattleGuildctor_CNormalGuildBattleGuild8_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildCleanUpBattle10_ptr GUILD_BATTLE__CNormalGuildBattleGuildCleanUpBattle10_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildCleanUpBattle10_clbk GUILD_BATTLE__CNormalGuildBattleGuildCleanUpBattle10_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildClear12_ptr GUILD_BATTLE__CNormalGuildBattleGuildClear12_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildClear12_clbk GUILD_BATTLE__CNormalGuildBattleGuildClear12_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildClearInBattleState14_ptr GUILD_BATTLE__CNormalGuildBattleGuildClearInBattleState14_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildClearInBattleState14_clbk GUILD_BATTLE__CNormalGuildBattleGuildClearInBattleState14_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildDecPvpPoint16_ptr GUILD_BATTLE__CNormalGuildBattleGuildDecPvpPoint16_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildDecPvpPoint16_clbk GUILD_BATTLE__CNormalGuildBattleGuildDecPvpPoint16_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetANSIGuildName18_ptr GUILD_BATTLE__CNormalGuildBattleGuildGetANSIGuildName18_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetANSIGuildName18_clbk GUILD_BATTLE__CNormalGuildBattleGuildGetANSIGuildName18_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetColorInx20_ptr GUILD_BATTLE__CNormalGuildBattleGuildGetColorInx20_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetColorInx20_clbk GUILD_BATTLE__CNormalGuildBattleGuildGetColorInx20_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetColorName22_ptr GUILD_BATTLE__CNormalGuildBattleGuildGetColorName22_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetColorName22_clbk GUILD_BATTLE__CNormalGuildBattleGuildGetColorName22_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetEmptyMember24_ptr GUILD_BATTLE__CNormalGuildBattleGuildGetEmptyMember24_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetEmptyMember24_clbk GUILD_BATTLE__CNormalGuildBattleGuildGetEmptyMember24_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetGoalCnt26_ptr GUILD_BATTLE__CNormalGuildBattleGuildGetGoalCnt26_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetGoalCnt26_clbk GUILD_BATTLE__CNormalGuildBattleGuildGetGoalCnt26_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetGuild28_ptr GUILD_BATTLE__CNormalGuildBattleGuildGetGuild28_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetGuild28_clbk GUILD_BATTLE__CNormalGuildBattleGuildGetGuild28_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetGuildName30_ptr GUILD_BATTLE__CNormalGuildBattleGuildGetGuildName30_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetGuildName30_clbk GUILD_BATTLE__CNormalGuildBattleGuildGetGuildName30_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetGuildRace32_ptr GUILD_BATTLE__CNormalGuildBattleGuildGetGuildRace32_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetGuildRace32_clbk GUILD_BATTLE__CNormalGuildBattleGuildGetGuildRace32_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetGuildSerial34_ptr GUILD_BATTLE__CNormalGuildBattleGuildGetGuildSerial34_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetGuildSerial34_clbk GUILD_BATTLE__CNormalGuildBattleGuildGetGuildSerial34_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetJoinMemberCnt36_ptr GUILD_BATTLE__CNormalGuildBattleGuildGetJoinMemberCnt36_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetJoinMemberCnt36_clbk GUILD_BATTLE__CNormalGuildBattleGuildGetJoinMemberCnt36_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetKillCountSum38_ptr GUILD_BATTLE__CNormalGuildBattleGuildGetKillCountSum38_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetKillCountSum38_clbk GUILD_BATTLE__CNormalGuildBattleGuildGetKillCountSum38_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetMaxJoinMemberCount40_ptr GUILD_BATTLE__CNormalGuildBattleGuildGetMaxJoinMemberCount40_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetMaxJoinMemberCount40_clbk GUILD_BATTLE__CNormalGuildBattleGuildGetMaxJoinMemberCount40_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetMember42_ptr GUILD_BATTLE__CNormalGuildBattleGuildGetMember42_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetMember42_clbk GUILD_BATTLE__CNormalGuildBattleGuildGetMember42_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPlayer44_ptr GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPlayer44_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPlayer44_clbk GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPlayer44_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPtr46_ptr GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPtr46_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPtr46_clbk GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPtr46_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetScore48_ptr GUILD_BATTLE__CNormalGuildBattleGuildGetScore48_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetScore48_clbk GUILD_BATTLE__CNormalGuildBattleGuildGetScore48_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetTopGoalMember50_ptr GUILD_BATTLE__CNormalGuildBattleGuildGetTopGoalMember50_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetTopGoalMember50_clbk GUILD_BATTLE__CNormalGuildBattleGuildGetTopGoalMember50_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetTopKillMember52_ptr GUILD_BATTLE__CNormalGuildBattleGuildGetTopKillMember52_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGetTopKillMember52_clbk GUILD_BATTLE__CNormalGuildBattleGuildGetTopKillMember52_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGoal54_ptr GUILD_BATTLE__CNormalGuildBattleGuildGoal54_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildGoal54_clbk GUILD_BATTLE__CNormalGuildBattleGuildGoal54_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildIncPvpPoint56_ptr GUILD_BATTLE__CNormalGuildBattleGuildIncPvpPoint56_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildIncPvpPoint56_clbk GUILD_BATTLE__CNormalGuildBattleGuildIncPvpPoint56_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildIsJoinMember58_ptr GUILD_BATTLE__CNormalGuildBattleGuildIsJoinMember58_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildIsJoinMember58_clbk GUILD_BATTLE__CNormalGuildBattleGuildIsJoinMember58_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildIsMember60_ptr GUILD_BATTLE__CNormalGuildBattleGuildIsMember60_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildIsMember60_clbk GUILD_BATTLE__CNormalGuildBattleGuildIsMember60_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildIsReStart62_ptr GUILD_BATTLE__CNormalGuildBattleGuildIsReStart62_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildIsReStart62_clbk GUILD_BATTLE__CNormalGuildBattleGuildIsReStart62_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildJoin64_ptr GUILD_BATTLE__CNormalGuildBattleGuildJoin64_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildJoin64_clbk GUILD_BATTLE__CNormalGuildBattleGuildJoin64_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildKill66_ptr GUILD_BATTLE__CNormalGuildBattleGuildKill66_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildKill66_clbk GUILD_BATTLE__CNormalGuildBattleGuildKill66_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildLeaveGuild68_ptr GUILD_BATTLE__CNormalGuildBattleGuildLeaveGuild68_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildLeaveGuild68_clbk GUILD_BATTLE__CNormalGuildBattleGuildLeaveGuild68_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildLogIn70_ptr GUILD_BATTLE__CNormalGuildBattleGuildLogIn70_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildLogIn70_clbk GUILD_BATTLE__CNormalGuildBattleGuildLogIn70_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMoveMap72_ptr GUILD_BATTLE__CNormalGuildBattleGuildMoveMap72_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMoveMap72_clbk GUILD_BATTLE__CNormalGuildBattleGuildMoveMap72_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMoveMember74_ptr GUILD_BATTLE__CNormalGuildBattleGuildMoveMember74_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMoveMember74_clbk GUILD_BATTLE__CNormalGuildBattleGuildMoveMember74_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildNetClose76_ptr GUILD_BATTLE__CNormalGuildBattleGuildNetClose76_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildNetClose76_clbk GUILD_BATTLE__CNormalGuildBattleGuildNetClose76_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildNotifyLeftMinuteBeforeStart78_ptr GUILD_BATTLE__CNormalGuildBattleGuildNotifyLeftMinuteBeforeStart78_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildNotifyLeftMinuteBeforeStart78_clbk GUILD_BATTLE__CNormalGuildBattleGuildNotifyLeftMinuteBeforeStart78_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildReturnBindPosAll80_ptr GUILD_BATTLE__CNormalGuildBattleGuildReturnBindPosAll80_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildReturnBindPosAll80_clbk GUILD_BATTLE__CNormalGuildBattleGuildReturnBindPosAll80_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildReturnHQPosAll82_ptr GUILD_BATTLE__CNormalGuildBattleGuildReturnHQPosAll82_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildReturnHQPosAll82_clbk GUILD_BATTLE__CNormalGuildBattleGuildReturnHQPosAll82_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildReturnStartPosAll84_ptr GUILD_BATTLE__CNormalGuildBattleGuildReturnStartPosAll84_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildReturnStartPosAll84_clbk GUILD_BATTLE__CNormalGuildBattleGuildReturnStartPosAll84_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildRewardItem86_ptr GUILD_BATTLE__CNormalGuildBattleGuildRewardItem86_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildRewardItem86_clbk GUILD_BATTLE__CNormalGuildBattleGuildRewardItem86_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSendDeleteNotifyPositionMember88_ptr GUILD_BATTLE__CNormalGuildBattleGuildSendDeleteNotifyPositionMember88_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSendDeleteNotifyPositionMember88_clbk GUILD_BATTLE__CNormalGuildBattleGuildSendDeleteNotifyPositionMember88_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSendGetGravityStone90_ptr GUILD_BATTLE__CNormalGuildBattleGuildSendGetGravityStone90_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSendGetGravityStone90_clbk GUILD_BATTLE__CNormalGuildBattleGuildSendGetGravityStone90_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSendMsg92_ptr GUILD_BATTLE__CNormalGuildBattleGuildSendMsg92_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSendMsg92_clbk GUILD_BATTLE__CNormalGuildBattleGuildSendMsg92_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSendMsg94_ptr GUILD_BATTLE__CNormalGuildBattleGuildSendMsg94_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSendMsg94_clbk GUILD_BATTLE__CNormalGuildBattleGuildSendMsg94_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSendMsg96_ptr GUILD_BATTLE__CNormalGuildBattleGuildSendMsg96_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSendMsg96_clbk GUILD_BATTLE__CNormalGuildBattleGuildSendMsg96_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSendOhterNotifyCommitteeMemberPosition98_ptr GUILD_BATTLE__CNormalGuildBattleGuildSendOhterNotifyCommitteeMemberPosition98_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSendOhterNotifyCommitteeMemberPosition98_clbk GUILD_BATTLE__CNormalGuildBattleGuildSendOhterNotifyCommitteeMemberPosition98_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSendRegenBall100_ptr GUILD_BATTLE__CNormalGuildBattleGuildSendRegenBall100_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSendRegenBall100_clbk GUILD_BATTLE__CNormalGuildBattleGuildSendRegenBall100_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSendSelfNotifyCommitteeMemberPositionList102_ptr GUILD_BATTLE__CNormalGuildBattleGuildSendSelfNotifyCommitteeMemberPositionList102_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSendSelfNotifyCommitteeMemberPositionList102_clbk GUILD_BATTLE__CNormalGuildBattleGuildSendSelfNotifyCommitteeMemberPositionList102_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSendStartNotifyCommitteeMemberPosition104_ptr GUILD_BATTLE__CNormalGuildBattleGuildSendStartNotifyCommitteeMemberPosition104_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSendStartNotifyCommitteeMemberPosition104_clbk GUILD_BATTLE__CNormalGuildBattleGuildSendStartNotifyCommitteeMemberPosition104_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSetColorInx106_ptr GUILD_BATTLE__CNormalGuildBattleGuildSetColorInx106_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSetColorInx106_clbk GUILD_BATTLE__CNormalGuildBattleGuildSetColorInx106_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSetGuild108_ptr GUILD_BATTLE__CNormalGuildBattleGuildSetGuild108_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSetGuild108_clbk GUILD_BATTLE__CNormalGuildBattleGuildSetGuild108_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSetReStartFlag110_ptr GUILD_BATTLE__CNormalGuildBattleGuildSetReStartFlag110_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildSetReStartFlag110_clbk GUILD_BATTLE__CNormalGuildBattleGuildSetReStartFlag110_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildUpdateScore112_ptr GUILD_BATTLE__CNormalGuildBattleGuildUpdateScore112_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildUpdateScore112_clbk GUILD_BATTLE__CNormalGuildBattleGuildUpdateScore112_user(nullptr);
            
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuilddtor_CNormalGuildBattleGuild114_ptr GUILD_BATTLE__CNormalGuildBattleGuilddtor_CNormalGuildBattleGuild114_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuilddtor_CNormalGuildBattleGuild114_clbk GUILD_BATTLE__CNormalGuildBattleGuilddtor_CNormalGuildBattleGuild114_user(nullptr);
            
            void GUILD_BATTLE__CNormalGuildBattleGuildAskJoin2_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, char* wszDestGuildName)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildAskJoin2_user(_this, wszDestGuildName, GUILD_BATTLE__CNormalGuildBattleGuildAskJoin2_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildAskJoin4_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, int n, char* wszDestGuildName, struct GUILD_BATTLE::CNormalGuildBattleLogger* kLogger)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildAskJoin4_user(_this, n, wszDestGuildName, kLogger, GUILD_BATTLE__CNormalGuildBattleGuildAskJoin4_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildAskJoin6_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, int n, unsigned int dwSerial, char GuildBattleNumber, char* wszDestGuild, struct GUILD_BATTLE::CNormalGuildBattleLogger* kLogger)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildAskJoin6_user(_this, n, dwSerial, GuildBattleNumber, wszDestGuild, kLogger, GUILD_BATTLE__CNormalGuildBattleGuildAskJoin6_next);
            };
            
            void GUILD_BATTLE__CNormalGuildBattleGuildctor_CNormalGuildBattleGuild8_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, char byID)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildctor_CNormalGuildBattleGuild8_user(_this, byID, GUILD_BATTLE__CNormalGuildBattleGuildctor_CNormalGuildBattleGuild8_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildCleanUpBattle10_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildCleanUpBattle10_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildCleanUpBattle10_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildClear12_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildClear12_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildClear12_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildClearInBattleState14_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildClearInBattleState14_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildClearInBattleState14_next);
            };
            long double GUILD_BATTLE__CNormalGuildBattleGuildDecPvpPoint16_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, struct GUILD_BATTLE::CNormalGuildBattleLogger* kLogger)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildDecPvpPoint16_user(_this, kLogger, GUILD_BATTLE__CNormalGuildBattleGuildDecPvpPoint16_next);
            };
            char* GUILD_BATTLE__CNormalGuildBattleGuildGetANSIGuildName18_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildGetANSIGuildName18_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildGetANSIGuildName18_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleGuildGetColorInx20_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildGetColorInx20_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildGetColorInx20_next);
            };
            char* GUILD_BATTLE__CNormalGuildBattleGuildGetColorName22_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildGetColorName22_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildGetColorName22_next);
            };
            int GUILD_BATTLE__CNormalGuildBattleGuildGetEmptyMember24_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildGetEmptyMember24_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildGetEmptyMember24_next);
            };
            unsigned int GUILD_BATTLE__CNormalGuildBattleGuildGetGoalCnt26_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildGetGoalCnt26_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildGetGoalCnt26_next);
            };
            struct CGuild* GUILD_BATTLE__CNormalGuildBattleGuildGetGuild28_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildGetGuild28_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildGetGuild28_next);
            };
            char* GUILD_BATTLE__CNormalGuildBattleGuildGetGuildName30_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildGetGuildName30_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildGetGuildName30_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleGuildGetGuildRace32_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildGetGuildRace32_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildGetGuildRace32_next);
            };
            unsigned int GUILD_BATTLE__CNormalGuildBattleGuildGetGuildSerial34_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildGetGuildSerial34_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildGetGuildSerial34_next);
            };
            unsigned int GUILD_BATTLE__CNormalGuildBattleGuildGetJoinMemberCnt36_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildGetJoinMemberCnt36_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildGetJoinMemberCnt36_next);
            };
            unsigned int GUILD_BATTLE__CNormalGuildBattleGuildGetKillCountSum38_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildGetKillCountSum38_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildGetKillCountSum38_next);
            };
            unsigned int GUILD_BATTLE__CNormalGuildBattleGuildGetMaxJoinMemberCount40_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildGetMaxJoinMemberCount40_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildGetMaxJoinMemberCount40_next);
            };
            int GUILD_BATTLE__CNormalGuildBattleGuildGetMember42_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, unsigned int dwSerial)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildGetMember42_user(_this, dwSerial, GUILD_BATTLE__CNormalGuildBattleGuildGetMember42_next);
            };
            struct CPlayer* GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPlayer44_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, unsigned int dwSerial)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPlayer44_user(_this, dwSerial, GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPlayer44_next);
            };
            struct GUILD_BATTLE::CNormalGuildBattleGuildMember* GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPtr46_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, unsigned int dwSerial)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPtr46_user(_this, dwSerial, GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPtr46_next);
            };
            unsigned int GUILD_BATTLE__CNormalGuildBattleGuildGetScore48_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildGetScore48_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildGetScore48_next);
            };
            struct GUILD_BATTLE::CNormalGuildBattleGuildMember* GUILD_BATTLE__CNormalGuildBattleGuildGetTopGoalMember50_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildGetTopGoalMember50_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildGetTopGoalMember50_next);
            };
            struct GUILD_BATTLE::CNormalGuildBattleGuildMember* GUILD_BATTLE__CNormalGuildBattleGuildGetTopKillMember52_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildGetTopKillMember52_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildGetTopKillMember52_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildGoal54_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, struct GUILD_BATTLE::CNormalGuildBattleGuildMember* pkMember)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildGoal54_user(_this, pkMember, GUILD_BATTLE__CNormalGuildBattleGuildGoal54_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildIncPvpPoint56_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, long double dTotalInc, char byWin, struct GUILD_BATTLE::CNormalGuildBattleLogger* kLogger)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildIncPvpPoint56_user(_this, dTotalInc, byWin, kLogger, GUILD_BATTLE__CNormalGuildBattleGuildIncPvpPoint56_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleGuildIsJoinMember58_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, unsigned int dwSerial)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildIsJoinMember58_user(_this, dwSerial, GUILD_BATTLE__CNormalGuildBattleGuildIsJoinMember58_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleGuildIsMember60_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, unsigned int dwSerial)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildIsMember60_user(_this, dwSerial, GUILD_BATTLE__CNormalGuildBattleGuildIsMember60_next);
            };
            int GUILD_BATTLE__CNormalGuildBattleGuildIsReStart62_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, unsigned int dwSerial)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildIsReStart62_user(_this, dwSerial, GUILD_BATTLE__CNormalGuildBattleGuildIsReStart62_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleGuildJoin64_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, unsigned int dwSerial, char GuildBattleNumber, int* iMemberInx, struct GUILD_BATTLE::CNormalGuildBattleLogger* kLogger)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildJoin64_user(_this, dwSerial, GuildBattleNumber, iMemberInx, kLogger, GUILD_BATTLE__CNormalGuildBattleGuildJoin64_next);
            };
            int GUILD_BATTLE__CNormalGuildBattleGuildKill66_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, struct GUILD_BATTLE::CNormalGuildBattleGuildMember* pkSrcMember, struct GUILD_BATTLE::CNormalGuildBattleGuildMember* pkDestMember)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildKill66_user(_this, pkSrcMember, pkDestMember, GUILD_BATTLE__CNormalGuildBattleGuildKill66_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildLeaveGuild68_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, unsigned int dwSerial, bool bInGuildBattle, struct GUILD_BATTLE::CNormalGuildBattleLogger* kLogger)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildLeaveGuild68_user(_this, dwSerial, bInGuildBattle, kLogger, GUILD_BATTLE__CNormalGuildBattleGuildLeaveGuild68_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildLogIn70_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, int n, unsigned int dwSerial, char GuildBattleNumber, char* wszDestGuild, unsigned int uiID, struct GUILD_BATTLE::CNormalGuildBattleField* pkField, struct GUILD_BATTLE::CNormalGuildBattleLogger* kLogger)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildLogIn70_user(_this, n, dwSerial, GuildBattleNumber, wszDestGuild, uiID, pkField, kLogger, GUILD_BATTLE__CNormalGuildBattleGuildLogIn70_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildMoveMap72_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, unsigned int uiID, struct GUILD_BATTLE::CNormalGuildBattleField* pkField)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildMoveMap72_user(_this, uiID, pkField, GUILD_BATTLE__CNormalGuildBattleGuildMoveMap72_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleGuildMoveMember74_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, int iMember, unsigned int uiID, struct GUILD_BATTLE::CNormalGuildBattleField* pkField, struct GUILD_BATTLE::CNormalGuildBattleLogger* kLogger)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildMoveMember74_user(_this, iMember, uiID, pkField, kLogger, GUILD_BATTLE__CNormalGuildBattleGuildMoveMember74_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleGuildNetClose76_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, bool bInGuildBattle, unsigned int dwSerial, struct GUILD_BATTLE::CNormalGuildBattleLogger* kLogger)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildNetClose76_user(_this, bInGuildBattle, dwSerial, kLogger, GUILD_BATTLE__CNormalGuildBattleGuildNetClose76_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildNotifyLeftMinuteBeforeStart78_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, char byLeftMin)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildNotifyLeftMinuteBeforeStart78_user(_this, byLeftMin, GUILD_BATTLE__CNormalGuildBattleGuildNotifyLeftMinuteBeforeStart78_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildReturnBindPosAll80_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildReturnBindPosAll80_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildReturnBindPosAll80_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleGuildReturnHQPosAll82_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildReturnHQPosAll82_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildReturnHQPosAll82_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleGuildReturnStartPosAll84_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, struct GUILD_BATTLE::CNormalGuildBattleField* pkField)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildReturnStartPosAll84_user(_this, pkField, GUILD_BATTLE__CNormalGuildBattleGuildReturnStartPosAll84_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildRewardItem86_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, struct GUILD_BATTLE::CNormalGuildBattleLogger* kLogger)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildRewardItem86_user(_this, kLogger, GUILD_BATTLE__CNormalGuildBattleGuildRewardItem86_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildSendDeleteNotifyPositionMember88_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, int iMemberInx)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildSendDeleteNotifyPositionMember88_user(_this, iMemberInx, GUILD_BATTLE__CNormalGuildBattleGuildSendDeleteNotifyPositionMember88_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildSendGetGravityStone90_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, struct GUILD_BATTLE::CNormalGuildBattleGuild* pkTakeGuild, struct CPlayer* pkPlayer, int iTakePortalInx)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildSendGetGravityStone90_user(_this, pkTakeGuild, pkPlayer, iTakePortalInx, GUILD_BATTLE__CNormalGuildBattleGuildSendGetGravityStone90_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildSendMsg92_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, char* byType, char* pMsg, unsigned int uiSize)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildSendMsg92_user(_this, byType, pMsg, uiSize, GUILD_BATTLE__CNormalGuildBattleGuildSendMsg92_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildSendMsg94_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, char* byType, char* pMsg, unsigned int uiSize, int iExeceptMemberInx)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildSendMsg94_user(_this, byType, pMsg, uiSize, iExeceptMemberInx, GUILD_BATTLE__CNormalGuildBattleGuildSendMsg94_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildSendMsg96_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, char* byType, char* pMsg, unsigned int uiSize, unsigned int dwSerial)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildSendMsg96_user(_this, byType, pMsg, uiSize, dwSerial, GUILD_BATTLE__CNormalGuildBattleGuildSendMsg96_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildSendOhterNotifyCommitteeMemberPosition98_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, struct CPlayer* pkPlayer)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildSendOhterNotifyCommitteeMemberPosition98_user(_this, pkPlayer, GUILD_BATTLE__CNormalGuildBattleGuildSendOhterNotifyCommitteeMemberPosition98_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildSendRegenBall100_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, int iPortalInx)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildSendRegenBall100_user(_this, iPortalInx, GUILD_BATTLE__CNormalGuildBattleGuildSendRegenBall100_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildSendSelfNotifyCommitteeMemberPositionList102_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, struct CPlayer* pkPlayer)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildSendSelfNotifyCommitteeMemberPositionList102_user(_this, pkPlayer, GUILD_BATTLE__CNormalGuildBattleGuildSendSelfNotifyCommitteeMemberPositionList102_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildSendStartNotifyCommitteeMemberPosition104_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, int iMember)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildSendStartNotifyCommitteeMemberPosition104_user(_this, iMember, GUILD_BATTLE__CNormalGuildBattleGuildSendStartNotifyCommitteeMemberPosition104_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildSetColorInx106_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, char byInx)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildSetColorInx106_user(_this, byInx, GUILD_BATTLE__CNormalGuildBattleGuildSetColorInx106_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildSetGuild108_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, struct CGuild* pkGuild)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildSetGuild108_user(_this, pkGuild, GUILD_BATTLE__CNormalGuildBattleGuildSetGuild108_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleGuildSetReStartFlag110_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this, unsigned int dwSerial)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildSetReStartFlag110_user(_this, dwSerial, GUILD_BATTLE__CNormalGuildBattleGuildSetReStartFlag110_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildUpdateScore112_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildUpdateScore112_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildUpdateScore112_next);
            };
            
            void GUILD_BATTLE__CNormalGuildBattleGuilddtor_CNormalGuildBattleGuild114_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuild* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleGuilddtor_CNormalGuildBattleGuild114_user(_this, GUILD_BATTLE__CNormalGuildBattleGuilddtor_CNormalGuildBattleGuild114_next);
            };
            
            ::std::array<hook_record, 57> CNormalGuildBattleGuild_functions = 
            {
                _hook_record {
                    (LPVOID)0x1403e1ed0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildAskJoin2_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildAskJoin2_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildAskJoin2_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(char*))&GUILD_BATTLE::CNormalGuildBattleGuild::AskJoin)
                },
                _hook_record {
                    (LPVOID)0x1403e2c80L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildAskJoin4_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildAskJoin4_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildAskJoin4_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(int, char*, struct GUILD_BATTLE::CNormalGuildBattleLogger*))&GUILD_BATTLE::CNormalGuildBattleGuild::AskJoin)
                },
                _hook_record {
                    (LPVOID)0x1403e0d20L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildAskJoin6_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildAskJoin6_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildAskJoin6_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(int, unsigned int, char, char*, struct GUILD_BATTLE::CNormalGuildBattleLogger*))&GUILD_BATTLE::CNormalGuildBattleGuild::AskJoin)
                },
                _hook_record {
                    (LPVOID)0x1403e04c0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildctor_CNormalGuildBattleGuild8_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildctor_CNormalGuildBattleGuild8_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildctor_CNormalGuildBattleGuild8_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(char))&GUILD_BATTLE::CNormalGuildBattleGuild::ctor_CNormalGuildBattleGuild)
                },
                _hook_record {
                    (LPVOID)0x1403e1e50L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildCleanUpBattle10_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildCleanUpBattle10_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildCleanUpBattle10_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::CleanUpBattle)
                },
                _hook_record {
                    (LPVOID)0x1403e0600L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildClear12_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildClear12_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildClear12_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::Clear)
                },
                _hook_record {
                    (LPVOID)0x1403e1680L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildClearInBattleState14_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildClearInBattleState14_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildClearInBattleState14_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::ClearInBattleState)
                },
                _hook_record {
                    (LPVOID)0x1403e1860L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildDecPvpPoint16_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildDecPvpPoint16_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildDecPvpPoint16_wrapper),
                    (LPVOID)cast_pointer_function((long double(GUILD_BATTLE::CNormalGuildBattleGuild::*)(struct GUILD_BATTLE::CNormalGuildBattleLogger*))&GUILD_BATTLE::CNormalGuildBattleGuild::DecPvpPoint)
                },
                _hook_record {
                    (LPVOID)0x1403e0770L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetANSIGuildName18_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetANSIGuildName18_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildGetANSIGuildName18_wrapper),
                    (LPVOID)cast_pointer_function((char*(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::GetANSIGuildName)
                },
                _hook_record {
                    (LPVOID)0x1403eafc0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetColorInx20_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetColorInx20_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildGetColorInx20_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::GetColorInx)
                },
                _hook_record {
                    (LPVOID)0x1403eb320L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetColorName22_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetColorName22_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildGetColorName22_wrapper),
                    (LPVOID)cast_pointer_function((char*(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::GetColorName)
                },
                _hook_record {
                    (LPVOID)0x1403e29e0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetEmptyMember24_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetEmptyMember24_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildGetEmptyMember24_wrapper),
                    (LPVOID)cast_pointer_function((int(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::GetEmptyMember)
                },
                _hook_record {
                    (LPVOID)0x1403eb150L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetGoalCnt26_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetGoalCnt26_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildGetGoalCnt26_wrapper),
                    (LPVOID)cast_pointer_function((unsigned int(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::GetGoalCnt)
                },
                _hook_record {
                    (LPVOID)0x1403eb130L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetGuild28_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetGuild28_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildGetGuild28_wrapper),
                    (LPVOID)cast_pointer_function((struct CGuild*(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::GetGuild)
                },
                _hook_record {
                    (LPVOID)0x1403e0710L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetGuildName30_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetGuildName30_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildGetGuildName30_wrapper),
                    (LPVOID)cast_pointer_function((char*(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName)
                },
                _hook_record {
                    (LPVOID)0x1403e0830L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetGuildRace32_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetGuildRace32_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildGetGuildRace32_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildRace)
                },
                _hook_record {
                    (LPVOID)0x1403e07d0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetGuildSerial34_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetGuildSerial34_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildGetGuildSerial34_wrapper),
                    (LPVOID)cast_pointer_function((unsigned int(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial)
                },
                _hook_record {
                    (LPVOID)0x1403e2b20L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetJoinMemberCnt36_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetJoinMemberCnt36_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildGetJoinMemberCnt36_wrapper),
                    (LPVOID)cast_pointer_function((unsigned int(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::GetJoinMemberCnt)
                },
                _hook_record {
                    (LPVOID)0x1403eb3f0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetKillCountSum38_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetKillCountSum38_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildGetKillCountSum38_wrapper),
                    (LPVOID)cast_pointer_function((unsigned int(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::GetKillCountSum)
                },
                _hook_record {
                    (LPVOID)0x1403eb410L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetMaxJoinMemberCount40_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetMaxJoinMemberCount40_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildGetMaxJoinMemberCount40_wrapper),
                    (LPVOID)cast_pointer_function((unsigned int(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::GetMaxJoinMemberCount)
                },
                _hook_record {
                    (LPVOID)0x1403e2a60L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetMember42_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetMember42_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildGetMember42_wrapper),
                    (LPVOID)cast_pointer_function((int(GUILD_BATTLE::CNormalGuildBattleGuild::*)(unsigned int))&GUILD_BATTLE::CNormalGuildBattleGuild::GetMember)
                },
                _hook_record {
                    (LPVOID)0x1403e0890L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPlayer44_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPlayer44_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPlayer44_wrapper),
                    (LPVOID)cast_pointer_function((struct CPlayer*(GUILD_BATTLE::CNormalGuildBattleGuild::*)(unsigned int))&GUILD_BATTLE::CNormalGuildBattleGuild::GetMemberPlayer)
                },
                _hook_record {
                    (LPVOID)0x1403e0a00L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPtr46_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPtr46_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildGetMemberPtr46_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CNormalGuildBattleGuildMember*(GUILD_BATTLE::CNormalGuildBattleGuild::*)(unsigned int))&GUILD_BATTLE::CNormalGuildBattleGuild::GetMemberPtr)
                },
                _hook_record {
                    (LPVOID)0x1403eb170L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetScore48_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetScore48_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildGetScore48_wrapper),
                    (LPVOID)cast_pointer_function((unsigned int(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::GetScore)
                },
                _hook_record {
                    (LPVOID)0x1403e0b00L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetTopGoalMember50_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetTopGoalMember50_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildGetTopGoalMember50_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CNormalGuildBattleGuildMember*(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::GetTopGoalMember)
                },
                _hook_record {
                    (LPVOID)0x1403e0a70L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetTopKillMember52_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGetTopKillMember52_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildGetTopKillMember52_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CNormalGuildBattleGuildMember*(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::GetTopKillMember)
                },
                _hook_record {
                    (LPVOID)0x1403e1600L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGoal54_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildGoal54_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildGoal54_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*))&GUILD_BATTLE::CNormalGuildBattleGuild::Goal)
                },
                _hook_record {
                    (LPVOID)0x1403e1730L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildIncPvpPoint56_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildIncPvpPoint56_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildIncPvpPoint56_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(long double, char, struct GUILD_BATTLE::CNormalGuildBattleLogger*))&GUILD_BATTLE::CNormalGuildBattleGuild::IncPvpPoint)
                },
                _hook_record {
                    (LPVOID)0x1403e2bc0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildIsJoinMember58_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildIsJoinMember58_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildIsJoinMember58_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleGuild::*)(unsigned int))&GUILD_BATTLE::CNormalGuildBattleGuild::IsJoinMember)
                },
                _hook_record {
                    (LPVOID)0x1403eb1b0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildIsMember60_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildIsMember60_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildIsMember60_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleGuild::*)(unsigned int))&GUILD_BATTLE::CNormalGuildBattleGuild::IsMember)
                },
                _hook_record {
                    (LPVOID)0x1403e0970L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildIsReStart62_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildIsReStart62_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildIsReStart62_wrapper),
                    (LPVOID)cast_pointer_function((int(GUILD_BATTLE::CNormalGuildBattleGuild::*)(unsigned int))&GUILD_BATTLE::CNormalGuildBattleGuild::IsReStart)
                },
                _hook_record {
                    (LPVOID)0x1403e0b90L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildJoin64_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildJoin64_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildJoin64_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattleGuild::*)(unsigned int, char, int*, struct GUILD_BATTLE::CNormalGuildBattleLogger*))&GUILD_BATTLE::CNormalGuildBattleGuild::Join)
                },
                _hook_record {
                    (LPVOID)0x1403e1560L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildKill66_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildKill66_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildKill66_wrapper),
                    (LPVOID)cast_pointer_function((int(GUILD_BATTLE::CNormalGuildBattleGuild::*)(struct GUILD_BATTLE::CNormalGuildBattleGuildMember*, struct GUILD_BATTLE::CNormalGuildBattleGuildMember*))&GUILD_BATTLE::CNormalGuildBattleGuild::Kill)
                },
                _hook_record {
                    (LPVOID)0x1403e1460L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildLeaveGuild68_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildLeaveGuild68_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildLeaveGuild68_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(unsigned int, bool, struct GUILD_BATTLE::CNormalGuildBattleLogger*))&GUILD_BATTLE::CNormalGuildBattleGuild::LeaveGuild)
                },
                _hook_record {
                    (LPVOID)0x1403e0dd0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildLogIn70_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildLogIn70_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildLogIn70_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(int, unsigned int, char, char*, unsigned int, struct GUILD_BATTLE::CNormalGuildBattleField*, struct GUILD_BATTLE::CNormalGuildBattleLogger*))&GUILD_BATTLE::CNormalGuildBattleGuild::LogIn)
                },
                _hook_record {
                    (LPVOID)0x1403e0f60L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMoveMap72_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMoveMap72_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMoveMap72_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(unsigned int, struct GUILD_BATTLE::CNormalGuildBattleField*))&GUILD_BATTLE::CNormalGuildBattleGuild::MoveMap)
                },
                _hook_record {
                    (LPVOID)0x1403e1140L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMoveMember74_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMoveMember74_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMoveMember74_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleGuild::*)(int, unsigned int, struct GUILD_BATTLE::CNormalGuildBattleField*, struct GUILD_BATTLE::CNormalGuildBattleLogger*))&GUILD_BATTLE::CNormalGuildBattleGuild::MoveMember)
                },
                _hook_record {
                    (LPVOID)0x1403e13b0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildNetClose76_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildNetClose76_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildNetClose76_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleGuild::*)(bool, unsigned int, struct GUILD_BATTLE::CNormalGuildBattleLogger*))&GUILD_BATTLE::CNormalGuildBattleGuild::NetClose)
                },
                _hook_record {
                    (LPVOID)0x1403e2050L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildNotifyLeftMinuteBeforeStart78_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildNotifyLeftMinuteBeforeStart78_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildNotifyLeftMinuteBeforeStart78_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(char))&GUILD_BATTLE::CNormalGuildBattleGuild::NotifyLeftMinuteBeforeStart)
                },
                _hook_record {
                    (LPVOID)0x1403e1cc0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildReturnBindPosAll80_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildReturnBindPosAll80_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildReturnBindPosAll80_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::ReturnBindPosAll)
                },
                _hook_record {
                    (LPVOID)0x1403e1b50L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildReturnHQPosAll82_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildReturnHQPosAll82_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildReturnHQPosAll82_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::ReturnHQPosAll)
                },
                _hook_record {
                    (LPVOID)0x1403e1d50L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildReturnStartPosAll84_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildReturnStartPosAll84_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildReturnStartPosAll84_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleGuild::*)(struct GUILD_BATTLE::CNormalGuildBattleField*))&GUILD_BATTLE::CNormalGuildBattleGuild::ReturnStartPosAll)
                },
                _hook_record {
                    (LPVOID)0x1403e19b0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildRewardItem86_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildRewardItem86_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildRewardItem86_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(struct GUILD_BATTLE::CNormalGuildBattleLogger*))&GUILD_BATTLE::CNormalGuildBattleGuild::RewardItem)
                },
                _hook_record {
                    (LPVOID)0x1403e2d80L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSendDeleteNotifyPositionMember88_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSendDeleteNotifyPositionMember88_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildSendDeleteNotifyPositionMember88_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(int))&GUILD_BATTLE::CNormalGuildBattleGuild::SendDeleteNotifyPositionMember)
                },
                _hook_record {
                    (LPVOID)0x1403e2550L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSendGetGravityStone90_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSendGetGravityStone90_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildSendGetGravityStone90_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(struct GUILD_BATTLE::CNormalGuildBattleGuild*, struct CPlayer*, int))&GUILD_BATTLE::CNormalGuildBattleGuild::SendGetGravityStone)
                },
                _hook_record {
                    (LPVOID)0x1403e2730L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSendMsg92_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSendMsg92_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildSendMsg92_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(char*, char*, unsigned int))&GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg)
                },
                _hook_record {
                    (LPVOID)0x1403e2800L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSendMsg94_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSendMsg94_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildSendMsg94_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(char*, char*, unsigned int, int))&GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg)
                },
                _hook_record {
                    (LPVOID)0x1403e28e0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSendMsg96_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSendMsg96_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildSendMsg96_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(char*, char*, unsigned int, unsigned int))&GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg)
                },
                _hook_record {
                    (LPVOID)0x1403e22d0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSendOhterNotifyCommitteeMemberPosition98_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSendOhterNotifyCommitteeMemberPosition98_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildSendOhterNotifyCommitteeMemberPosition98_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(struct CPlayer*))&GUILD_BATTLE::CNormalGuildBattleGuild::SendOhterNotifyCommitteeMemberPosition)
                },
                _hook_record {
                    (LPVOID)0x1403e2150L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSendRegenBall100_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSendRegenBall100_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildSendRegenBall100_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(int))&GUILD_BATTLE::CNormalGuildBattleGuild::SendRegenBall)
                },
                _hook_record {
                    (LPVOID)0x1403e23d0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSendSelfNotifyCommitteeMemberPositionList102_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSendSelfNotifyCommitteeMemberPositionList102_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildSendSelfNotifyCommitteeMemberPositionList102_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(struct CPlayer*))&GUILD_BATTLE::CNormalGuildBattleGuild::SendSelfNotifyCommitteeMemberPositionList)
                },
                _hook_record {
                    (LPVOID)0x1403e2250L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSendStartNotifyCommitteeMemberPosition104_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSendStartNotifyCommitteeMemberPosition104_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildSendStartNotifyCommitteeMemberPosition104_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(int))&GUILD_BATTLE::CNormalGuildBattleGuild::SendStartNotifyCommitteeMemberPosition)
                },
                _hook_record {
                    (LPVOID)0x1403eb190L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSetColorInx106_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSetColorInx106_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildSetColorInx106_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(char))&GUILD_BATTLE::CNormalGuildBattleGuild::SetColorInx)
                },
                _hook_record {
                    (LPVOID)0x1403eb0a0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSetGuild108_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSetGuild108_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildSetGuild108_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)(struct CGuild*))&GUILD_BATTLE::CNormalGuildBattleGuild::SetGuild)
                },
                _hook_record {
                    (LPVOID)0x1403e0900L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSetReStartFlag110_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildSetReStartFlag110_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildSetReStartFlag110_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleGuild::*)(unsigned int))&GUILD_BATTLE::CNormalGuildBattleGuild::SetReStartFlag)
                },
                _hook_record {
                    (LPVOID)0x1403eae20L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildUpdateScore112_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildUpdateScore112_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildUpdateScore112_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::UpdateScore)
                },
                _hook_record {
                    (LPVOID)0x1403e05a0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuilddtor_CNormalGuildBattleGuild114_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuilddtor_CNormalGuildBattleGuild114_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuilddtor_CNormalGuildBattleGuild114_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuild::*)())&GUILD_BATTLE::CNormalGuildBattleGuild::dtor_CNormalGuildBattleGuild)
                },
            };
        }; // end namespace Detail
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE
