// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_GUID.hpp>
#include <_WSANSClassInfoW.hpp>



START_ATF_NAMESPACE
    struct _WSAServiceClassInfoW
    {
        _GUID *lpServiceClassId;
        wchar_t *lpszServiceClassName;
        unsigned int dwCount;
        _WSANSClassInfoW *lpClassInfos;
    };
END_ATF_NAMESPACE
