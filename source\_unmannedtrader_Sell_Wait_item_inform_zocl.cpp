#include <_unmannedtrader_Sell_Wait_item_inform_zocl.hpp>


START_ATF_NAMESPACE
    _unmannedtrader_Sell_Wait_item_inform_zocl::_unmannedtrader_Sell_Wait_item_inform_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _unmannedtrader_Sell_Wait_item_inform_zocl*);
        (org_ptr(0x1403601c0L))(this);
    };
    void _unmannedtrader_Sell_Wait_item_inform_zocl::ctor__unmannedtrader_Sell_Wait_item_inform_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _unmannedtrader_Sell_Wait_item_inform_zocl*);
        (org_ptr(0x1403601c0L))(this);
    };
    int _unmannedtrader_Sell_Wait_item_inform_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _unmannedtrader_Sell_Wait_item_inform_zocl*);
        return (org_ptr(0x1403601e0L))(this);
    };
    
END_ATF_NAMESPACE
