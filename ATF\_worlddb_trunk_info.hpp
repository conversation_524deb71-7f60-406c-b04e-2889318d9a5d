// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _worlddb_trunk_info
    {
        struct __trunk_key
        {
            int lK;
            unsigned __int64 dwD;
            unsigned int dwU;
            char byRace;
            unsigned int dwT;
            unsigned __int64 lnUID;
        };
        char wszPasswd[13];
        long double dDalant;
        long double dGold;
        char byHintIndex;
        char wszHintAnswer[17];
        char bySlotNum;
        __trunk_key trunkKey[100];
        char byExtSlotNum;
        __trunk_key ExttrunkKey[40];
    };
END_ATF_NAMESPACE
