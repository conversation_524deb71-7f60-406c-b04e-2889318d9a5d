#include <_personal_automine_install_zocl.hpp>


START_ATF_NAMESPACE
    _personal_automine_install_zocl::_personal_automine_install_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_install_zocl*);
        (org_ptr(0x1402de230L))(this);
    };
    void _personal_automine_install_zocl::ctor__personal_automine_install_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_install_zocl*);
        (org_ptr(0x1402de230L))(this);
    };
    int _personal_automine_install_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _personal_automine_install_zocl*);
        return (org_ptr(0x1402de280L))(this);
    };
END_ATF_NAMESPACE
