#include <Define_the_symbol__ATL_MIXED__Thank_you.hpp>


START_ATF_NAMESPACE
    Define_the_symbol__ATL_MIXED::Thank_you::Thank_you()
    {
        using org_ptr = void (WINAPIV*)(struct Define_the_symbol__ATL_MIXED::Thank_you*);
        (org_ptr(0x1400279e0L))(this);
    };
    void Define_the_symbol__ATL_MIXED::Thank_you::ctor_Thank_you()
    {
        using org_ptr = void (WINAPIV*)(struct Define_the_symbol__ATL_MIXED::Thank_you*);
        (org_ptr(0x1400279e0L))(this);
    };
    void Define_the_symbol__ATL_MIXED::Thank_you::one()
    {
        using org_ptr = void (WINAPIV*)(struct Define_the_symbol__ATL_MIXED::Thank_you*);
        (org_ptr(0x140027a10L))(this);
    };
END_ATF_NAMESPACE
