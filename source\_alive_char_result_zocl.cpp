#include <_alive_char_result_zocl.hpp>


START_ATF_NAMESPACE
    _alive_char_result_zocl::_alive_char_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _alive_char_result_zocl*);
        (org_ptr(0x140120dd0L))(this);
    };
    void _alive_char_result_zocl::ctor__alive_char_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _alive_char_result_zocl*);
        (org_ptr(0x140120dd0L))(this);
    };
END_ATF_NAMESPACE
