#include <GUILD_BATTLE__CCurrentGuildBattleInfoManagerDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Detail
        {
            
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerctor_CCurrentGuildBattleInfoManager2_ptr GUILD_BATTLE__CCurrentGuildBattleInfoManagerctor_CCurrentGuildBattleInfoManager2_next(nullptr);
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerctor_CCurrentGuildBattleInfoManager2_clbk GUILD_BATTLE__CCurrentGuildBattleInfoManagerctor_CCurrentGuildBattleInfoManager2_user(nullptr);
            
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerCleanUp4_ptr GUILD_BATTLE__CCurrentGuildBattleInfoManagerCleanUp4_next(nullptr);
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerCleanUp4_clbk GUILD_BATTLE__CCurrentGuildBattleInfoManagerCleanUp4_user(nullptr);
            
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerClear6_ptr GUILD_BATTLE__CCurrentGuildBattleInfoManagerClear6_next(nullptr);
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerClear6_clbk GUILD_BATTLE__CCurrentGuildBattleInfoManagerClear6_user(nullptr);
            
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerDestroy8_ptr GUILD_BATTLE__CCurrentGuildBattleInfoManagerDestroy8_next(nullptr);
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerDestroy8_clbk GUILD_BATTLE__CCurrentGuildBattleInfoManagerDestroy8_user(nullptr);
            
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerGetLeftTime10_ptr GUILD_BATTLE__CCurrentGuildBattleInfoManagerGetLeftTime10_next(nullptr);
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerGetLeftTime10_clbk GUILD_BATTLE__CCurrentGuildBattleInfoManagerGetLeftTime10_user(nullptr);
            
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerInit12_ptr GUILD_BATTLE__CCurrentGuildBattleInfoManagerInit12_next(nullptr);
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerInit12_clbk GUILD_BATTLE__CCurrentGuildBattleInfoManagerInit12_user(nullptr);
            
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerInstance14_ptr GUILD_BATTLE__CCurrentGuildBattleInfoManagerInstance14_next(nullptr);
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerInstance14_clbk GUILD_BATTLE__CCurrentGuildBattleInfoManagerInstance14_user(nullptr);
            
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerSend16_ptr GUILD_BATTLE__CCurrentGuildBattleInfoManagerSend16_next(nullptr);
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerSend16_clbk GUILD_BATTLE__CCurrentGuildBattleInfoManagerSend16_user(nullptr);
            
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerSet18_ptr GUILD_BATTLE__CCurrentGuildBattleInfoManagerSet18_next(nullptr);
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerSet18_clbk GUILD_BATTLE__CCurrentGuildBattleInfoManagerSet18_user(nullptr);
            
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateGoalCnt20_ptr GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateGoalCnt20_next(nullptr);
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateGoalCnt20_clbk GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateGoalCnt20_user(nullptr);
            
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateScore22_ptr GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateScore22_next(nullptr);
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateScore22_clbk GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateScore22_user(nullptr);
            
            
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerdtor_CCurrentGuildBattleInfoManager26_ptr GUILD_BATTLE__CCurrentGuildBattleInfoManagerdtor_CCurrentGuildBattleInfoManager26_next(nullptr);
            Info::GUILD_BATTLE__CCurrentGuildBattleInfoManagerdtor_CCurrentGuildBattleInfoManager26_clbk GUILD_BATTLE__CCurrentGuildBattleInfoManagerdtor_CCurrentGuildBattleInfoManager26_user(nullptr);
            
            
            void GUILD_BATTLE__CCurrentGuildBattleInfoManagerctor_CCurrentGuildBattleInfoManager2_wrapper(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager* _this)
            {
               GUILD_BATTLE__CCurrentGuildBattleInfoManagerctor_CCurrentGuildBattleInfoManager2_user(_this, GUILD_BATTLE__CCurrentGuildBattleInfoManagerctor_CCurrentGuildBattleInfoManager2_next);
            };
            void GUILD_BATTLE__CCurrentGuildBattleInfoManagerCleanUp4_wrapper(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager* _this)
            {
               GUILD_BATTLE__CCurrentGuildBattleInfoManagerCleanUp4_user(_this, GUILD_BATTLE__CCurrentGuildBattleInfoManagerCleanUp4_next);
            };
            void GUILD_BATTLE__CCurrentGuildBattleInfoManagerClear6_wrapper(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager* _this, unsigned int uiMapID)
            {
               GUILD_BATTLE__CCurrentGuildBattleInfoManagerClear6_user(_this, uiMapID, GUILD_BATTLE__CCurrentGuildBattleInfoManagerClear6_next);
            };
            void GUILD_BATTLE__CCurrentGuildBattleInfoManagerDestroy8_wrapper()
            {
               GUILD_BATTLE__CCurrentGuildBattleInfoManagerDestroy8_user(GUILD_BATTLE__CCurrentGuildBattleInfoManagerDestroy8_next);
            };
            char GUILD_BATTLE__CCurrentGuildBattleInfoManagerGetLeftTime10_wrapper(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager* _this, unsigned int uiMapID)
            {
               return GUILD_BATTLE__CCurrentGuildBattleInfoManagerGetLeftTime10_user(_this, uiMapID, GUILD_BATTLE__CCurrentGuildBattleInfoManagerGetLeftTime10_next);
            };
            bool GUILD_BATTLE__CCurrentGuildBattleInfoManagerInit12_wrapper(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager* _this)
            {
               return GUILD_BATTLE__CCurrentGuildBattleInfoManagerInit12_user(_this, GUILD_BATTLE__CCurrentGuildBattleInfoManagerInit12_next);
            };
            struct GUILD_BATTLE::CCurrentGuildBattleInfoManager* GUILD_BATTLE__CCurrentGuildBattleInfoManagerInstance14_wrapper()
            {
               return GUILD_BATTLE__CCurrentGuildBattleInfoManagerInstance14_user(GUILD_BATTLE__CCurrentGuildBattleInfoManagerInstance14_next);
            };
            void GUILD_BATTLE__CCurrentGuildBattleInfoManagerSend16_wrapper(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager* _this, int n, unsigned int uiMapID)
            {
               GUILD_BATTLE__CCurrentGuildBattleInfoManagerSend16_user(_this, n, uiMapID, GUILD_BATTLE__CCurrentGuildBattleInfoManagerSend16_next);
            };
            bool GUILD_BATTLE__CCurrentGuildBattleInfoManagerSet18_wrapper(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager* _this, unsigned int uiMapID, struct GUILD_BATTLE::CNormalGuildBattle* pkBattle)
            {
               return GUILD_BATTLE__CCurrentGuildBattleInfoManagerSet18_user(_this, uiMapID, pkBattle, GUILD_BATTLE__CCurrentGuildBattleInfoManagerSet18_next);
            };
            void GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateGoalCnt20_wrapper(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager* _this, unsigned int uiMapID, char byColorInx, unsigned int dwGoalCnt)
            {
               GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateGoalCnt20_user(_this, uiMapID, byColorInx, dwGoalCnt, GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateGoalCnt20_next);
            };
            void GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateScore22_wrapper(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager* _this, unsigned int uiMapID, char byColorInx, unsigned int dwScore)
            {
               GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateScore22_user(_this, uiMapID, byColorInx, dwScore, GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateScore22_next);
            };
            
            void GUILD_BATTLE__CCurrentGuildBattleInfoManagerdtor_CCurrentGuildBattleInfoManager26_wrapper(struct GUILD_BATTLE::CCurrentGuildBattleInfoManager* _this)
            {
               GUILD_BATTLE__CCurrentGuildBattleInfoManagerdtor_CCurrentGuildBattleInfoManager26_user(_this, GUILD_BATTLE__CCurrentGuildBattleInfoManagerdtor_CCurrentGuildBattleInfoManager26_next);
            };
            
            ::std::array<hook_record, 12> CCurrentGuildBattleInfoManager_functions = 
            {
                _hook_record {
                    (LPVOID)0x1403cde40L,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerctor_CCurrentGuildBattleInfoManager2_user,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerctor_CCurrentGuildBattleInfoManager2_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CCurrentGuildBattleInfoManagerctor_CCurrentGuildBattleInfoManager2_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CCurrentGuildBattleInfoManager::*)())&GUILD_BATTLE::CCurrentGuildBattleInfoManager::ctor_CCurrentGuildBattleInfoManager)
                },
                _hook_record {
                    (LPVOID)0x1403ce510L,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerCleanUp4_user,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerCleanUp4_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CCurrentGuildBattleInfoManagerCleanUp4_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CCurrentGuildBattleInfoManager::*)())&GUILD_BATTLE::CCurrentGuildBattleInfoManager::CleanUp)
                },
                _hook_record {
                    (LPVOID)0x1403ce220L,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerClear6_user,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerClear6_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CCurrentGuildBattleInfoManagerClear6_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CCurrentGuildBattleInfoManager::*)(unsigned int))&GUILD_BATTLE::CCurrentGuildBattleInfoManager::Clear)
                },
                _hook_record {
                    (LPVOID)0x1403cdf80L,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerDestroy8_user,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerDestroy8_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CCurrentGuildBattleInfoManagerDestroy8_wrapper),
                    (LPVOID)cast_pointer_function((void(*)())&GUILD_BATTLE::CCurrentGuildBattleInfoManager::Destroy)
                },
                _hook_record {
                    (LPVOID)0x1403ce5c0L,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerGetLeftTime10_user,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerGetLeftTime10_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CCurrentGuildBattleInfoManagerGetLeftTime10_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CCurrentGuildBattleInfoManager::*)(unsigned int))&GUILD_BATTLE::CCurrentGuildBattleInfoManager::GetLeftTime)
                },
                _hook_record {
                    (LPVOID)0x1403ce000L,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerInit12_user,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerInit12_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CCurrentGuildBattleInfoManagerInit12_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CCurrentGuildBattleInfoManager::*)())&GUILD_BATTLE::CCurrentGuildBattleInfoManager::Init)
                },
                _hook_record {
                    (LPVOID)0x1403cdec0L,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerInstance14_user,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerInstance14_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CCurrentGuildBattleInfoManagerInstance14_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CCurrentGuildBattleInfoManager*(*)())&GUILD_BATTLE::CCurrentGuildBattleInfoManager::Instance)
                },
                _hook_record {
                    (LPVOID)0x1403ce3d0L,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerSend16_user,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerSend16_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CCurrentGuildBattleInfoManagerSend16_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CCurrentGuildBattleInfoManager::*)(int, unsigned int))&GUILD_BATTLE::CCurrentGuildBattleInfoManager::Send)
                },
                _hook_record {
                    (LPVOID)0x1403ce160L,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerSet18_user,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerSet18_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CCurrentGuildBattleInfoManagerSet18_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CCurrentGuildBattleInfoManager::*)(unsigned int, struct GUILD_BATTLE::CNormalGuildBattle*))&GUILD_BATTLE::CCurrentGuildBattleInfoManager::Set)
                },
                _hook_record {
                    (LPVOID)0x1403ce340L,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateGoalCnt20_user,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateGoalCnt20_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateGoalCnt20_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CCurrentGuildBattleInfoManager::*)(unsigned int, char, unsigned int))&GUILD_BATTLE::CCurrentGuildBattleInfoManager::UpdateGoalCnt)
                },
                _hook_record {
                    (LPVOID)0x1403ce2b0L,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateScore22_user,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateScore22_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CCurrentGuildBattleInfoManagerUpdateScore22_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CCurrentGuildBattleInfoManager::*)(unsigned int, char, unsigned int))&GUILD_BATTLE::CCurrentGuildBattleInfoManager::UpdateScore)
                },
                _hook_record {
                    (LPVOID)0x1403cde80L,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerdtor_CCurrentGuildBattleInfoManager26_user,
                    (LPVOID *)&GUILD_BATTLE__CCurrentGuildBattleInfoManagerdtor_CCurrentGuildBattleInfoManager26_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CCurrentGuildBattleInfoManagerdtor_CCurrentGuildBattleInfoManager26_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CCurrentGuildBattleInfoManager::*)())&GUILD_BATTLE::CCurrentGuildBattleInfoManager::dtor_CCurrentGuildBattleInfoManager)
                },
            };
        }; // end namespace Detail
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE
