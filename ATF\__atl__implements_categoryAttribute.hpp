// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace vc_attributes
    {
        namespace atl
        {
            template<>
            struct implements_categoryAttribute
            {
                const char *value;
            };
        }; // end namespace atl
    }; // end namespace vc_attributes
END_ATF_NAMESPACE
