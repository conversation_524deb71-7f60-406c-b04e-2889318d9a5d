// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_wireSAFEARRAY_UNION.hpp>
#include <tagSAFEARRAYBOUND.hpp>


START_ATF_NAMESPACE
    struct _wireSAFEARRAY
    {
        unsigned __int16 cDims;
        unsigned __int16 fFeatures;
        unsigned int cbElements;
        unsigned int cLocks;
        _wireSAFEARRAY_UNION uArrayStructs;
        tagSAFEARRAYBOUND rgsabound[1];
    };
END_ATF_NAMESPACE
