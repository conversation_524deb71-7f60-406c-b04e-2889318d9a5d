#pragma once

// NexusPro Game Enhancement Module
// Comprehensive game modification and enhancement capabilities
// Compatible with Visual Studio 2022

#include "../ATFMod.hpp"
#include "../../ATF/CPlayer.hpp"
#include "../../ATF/CGuild.hpp"
#include <vector>
#include <map>
#include <functional>
#include <chrono>

namespace NexusPro {

    // Enhancement categories
    enum class EnhancementType {
        PLAYER_STATS,
        COMBAT_SYSTEM,
        MOVEMENT,
        ECONOMY,
        ITEMS,
        GUILD_SYSTEM,
        SOCIAL_FEATURES,
        AUTOMATION,
        VISUAL_EFFECTS,
        AUDIO_EFFECTS
    };

    // Player Enhancement Module
    class PlayerEnhancementModule {
    private:
        bool godModeEnabled;
        bool unlimitedResourcesEnabled;
        float statMultipliers[10]; // Various stat multipliers
        int levelOverride;
        bool autoLevelingEnabled;

    public:
        PlayerEnhancementModule();
        ~PlayerEnhancementModule();

        // Core player modifications
        void SetGodMode(bool enable);
        void SetUnlimitedHealth(bool enable);
        void SetUnlimitedMana(bool enable);
        void SetUnlimitedStamina(bool enable);
        
        // Stat modifications
        void SetStatMultiplier(int statIndex, float multiplier);
        void SetAllStatsMultiplier(float multiplier);
        void SetLevel(int level);
        void AddExperience(long double amount);
        void SetAutoLeveling(bool enable, int targetLevel = 999);
        
        // Skill modifications
        void UnlockAllSkills();
        void MaxAllSkills();
        void SetSkillLevel(int skillId, int level);
        void AddSkillPoints(int amount);
        void ResetSkills();
        
        // Character appearance
        void ChangeCharacterModel(int modelId);
        void SetCharacterSize(float scale);
        void EnableInvisibility(bool enable);
        void SetCharacterGlow(bool enable, uint32_t color = 0xFFFFFF);
    };

    // Combat Enhancement Module
    class CombatEnhancementModule {
    private:
        float damageMultiplier;
        float attackSpeedMultiplier;
        float criticalChance;
        float criticalDamage;
        bool alwaysCritical;
        bool noSkillCooldown;
        float rangeMultiplier;

    public:
        CombatEnhancementModule();
        ~CombatEnhancementModule();

        // Damage modifications
        void SetDamageMultiplier(float multiplier);
        void SetCriticalChance(float chance); // 0.0 to 1.0
        void SetCriticalDamage(float multiplier);
        void SetAlwaysCritical(bool enable);
        
        // Attack modifications
        void SetAttackSpeed(float multiplier);
        void SetAttackRange(float multiplier);
        void EnableAutoAttack(bool enable);
        void SetAutoAttackTarget(void* target);
        
        // Skill modifications
        void RemoveSkillCooldowns(bool enable);
        void SetSkillCooldownMultiplier(float multiplier);
        void EnableUnlimitedMana(bool enable);
        void SetCastTimeMultiplier(float multiplier);
        
        // Defense modifications
        void SetDefenseMultiplier(float multiplier);
        void SetDodgeChance(float chance);
        void SetBlockChance(float chance);
        void EnableDamageReflection(bool enable, float percentage);
        
        // Special abilities
        void EnableLifeSteal(bool enable, float percentage);
        void EnableManaSteal(bool enable, float percentage);
        void SetElementalDamage(int element, float bonus);
        void EnableAreaOfEffectAttacks(bool enable, float radius);
    };

    // Movement Enhancement Module
    class MovementEnhancementModule {
    private:
        float speedMultiplier;
        bool noClipEnabled;
        bool flyModeEnabled;
        float jumpHeightMultiplier;
        bool teleportationEnabled;
        std::vector<std::pair<float, float>> savedLocations;

    public:
        MovementEnhancementModule();
        ~MovementEnhancementModule();

        // Speed modifications
        void SetMovementSpeed(float multiplier);
        void SetRunningSpeed(float multiplier);
        void SetSwimmingSpeed(float multiplier);
        void SetFlyingSpeed(float multiplier);
        
        // Movement modes
        void EnableNoClip(bool enable);
        void EnableFlyMode(bool enable);
        void EnableWallWalking(bool enable);
        void EnableWaterWalking(bool enable);
        
        // Jump modifications
        void SetJumpHeight(float multiplier);
        void EnableInfiniteJumps(bool enable);
        void EnableDoubleJump(bool enable);
        void SetGravity(float multiplier);
        
        // Teleportation
        void TeleportToCoordinates(float x, float y, float z);
        void TeleportToPlayer(const std::string& playerName);
        void SaveLocation(const std::string& name, float x, float y, float z);
        void TeleportToSavedLocation(const std::string& name);
        std::vector<std::string> GetSavedLocationNames();
        
        // Auto-movement
        void EnableAutoWalk(bool enable, float x, float y, float z);
        void EnableAutoFollow(bool enable, const std::string& targetPlayer);
        void SetMovementPath(const std::vector<std::pair<float, float>>& waypoints);
    };

    // Economic Enhancement Module
    class EconomicEnhancementModule {
    private:
        bool unlimitedMoneyEnabled;
        float moneyMultiplier;
        bool autoFarmingEnabled;
        std::map<int, float> dropRateMultipliers;

    public:
        EconomicEnhancementModule();
        ~EconomicEnhancementModule();

        // Currency modifications
        void SetUnlimitedMoney(bool enable);
        void SetMoneyMultiplier(float multiplier);
        void AddMoney(unsigned int dalant, unsigned int gold);
        void SetMoney(unsigned int dalant, unsigned int gold);
        
        // Trading enhancements
        void EnableAutoTrade(bool enable);
        void SetTradeBot(bool enable, const std::vector<int>& itemIds);
        void EnableMarketManipulation(bool enable);
        void SetOptimalPricing(bool enable);
        
        // Farming automation
        void EnableAutoFarming(bool enable);
        void SetFarmingLocation(float x, float y, float z, float radius);
        void SetFarmingTargets(const std::vector<int>& monsterIds);
        void EnableAutoLoot(bool enable);
        
        // Drop rate modifications
        void SetGlobalDropRate(float multiplier);
        void SetItemDropRate(int itemId, float multiplier);
        void EnableRareDropGuarantee(bool enable);
        void SetDropRateForRarity(int rarity, float multiplier);
        
        // Auction house automation
        void EnableAuctionBot(bool enable);
        void SetBuyoutThreshold(int itemId, unsigned int maxPrice);
        void SetSellStrategy(int itemId, unsigned int minPrice);
        void EnableSnipeMode(bool enable);
    };

    // Item Enhancement Module
    class ItemEnhancementModule {
    private:
        bool unlimitedDurabilityEnabled;
        bool autoRepairEnabled;
        std::map<int, int> itemQuantityOverrides;

    public:
        ItemEnhancementModule();
        ~ItemEnhancementModule();

        // Durability modifications
        void SetUnlimitedDurability(bool enable);
        void SetAutoRepair(bool enable);
        void RepairAllItems();
        void SetDurabilityMultiplier(float multiplier);
        
        // Item manipulation
        void DuplicateItem(int slotIndex, int quantity = 1);
        void CreateItem(int itemId, int quantity = 1);
        void DeleteItem(int slotIndex);
        void MoveItem(int fromSlot, int toSlot);
        
        // Item enhancement
        void UpgradeItem(int slotIndex, int targetLevel);
        void SetItemLevel(int slotIndex, int level);
        void AddItemOptions(int slotIndex, const std::vector<int>& optionIds);
        void SetItemRarity(int slotIndex, int rarity);
        
        // Inventory management
        void ExpandInventory(int additionalSlots);
        void EnableAutoSort(bool enable);
        void EnableAutoStack(bool enable);
        void SetAutoSell(bool enable, const std::vector<int>& itemIds);
        
        // Equipment enhancements
        void EnableAutoEquipBest(bool enable);
        void SetEquipmentSet(const std::vector<int>& itemIds);
        void EnableSetBonuses(bool enable);
        void MultiplySetBonuses(float multiplier);
        
        // Crafting automation
        void EnableAutoCrafting(bool enable);
        void SetCraftingQueue(const std::vector<int>& recipeIds);
        void EnableMaterialFarming(bool enable);
        void SetCraftingSuccess(float rate); // 0.0 to 1.0
    };

    // Guild Enhancement Module
    class GuildEnhancementModule {
    private:
        bool autoGuildBattleEnabled;
        bool guildResourceMultiplierEnabled;
        float guildExpMultiplier;

    public:
        GuildEnhancementModule();
        ~GuildEnhancementModule();

        // Guild battle automation
        void EnableAutoGuildBattle(bool enable);
        void SetGuildBattleStrategy(int strategy);
        void EnableAutoJoinBattle(bool enable);
        void SetBattleFormation(const std::vector<std::string>& playerNames);
        
        // Guild management
        void SetGuildTax(float rate);
        void EnableAutoAcceptMembers(bool enable);
        void SetMemberRequirements(int minLevel, int minGear);
        void EnableAutoKickInactive(bool enable, int dayThreshold);
        
        // Guild resources
        void SetGuildExpMultiplier(float multiplier);
        void AddGuildResources(unsigned int amount);
        void EnableResourceSharing(bool enable);
        void SetOptimalResourceAllocation(bool enable);
        
        // Guild facilities
        void UpgradeAllFacilities();
        void SetFacilityLevel(int facilityId, int level);
        void EnableAutoUpgrade(bool enable);
        void SetUpgradePriority(const std::vector<int>& facilityIds);
        
        // Alliance management
        void EnableAutoAlliance(bool enable);
        void SetAllianceStrategy(int strategy);
        void ManageAllianceWars(bool enable);
        void SetDiplomacySettings(const std::map<std::string, int>& relations);
    };

    // Automation Module
    class AutomationModule {
    private:
        std::map<std::string, std::function<void()>> automationTasks;
        std::map<std::string, std::chrono::milliseconds> taskIntervals;
        std::map<std::string, bool> taskStates;

    public:
        AutomationModule();
        ~AutomationModule();

        // Task management
        void RegisterTask(const std::string& name, std::function<void()> task, 
                         std::chrono::milliseconds interval);
        void StartTask(const std::string& name);
        void StopTask(const std::string& name);
        void StopAllTasks();
        
        // Pre-defined automations
        void EnableAutoLogin(bool enable, const std::string& username, 
                           const std::string& password);
        void EnableAutoReconnect(bool enable);
        void EnableAutoAcceptQuests(bool enable);
        void EnableAutoCompleteQuests(bool enable);
        
        // Event automation
        void EnableAutoEventParticipation(bool enable);
        void SetEventPriorities(const std::vector<int>& eventIds);
        void EnableAutoReward(bool enable);
        void SetAutoEventStrategy(int eventId, int strategy);
        
        // Social automation
        void EnableAutoFriend(bool enable);
        void EnableAutoParty(bool enable);
        void SetAutoResponse(const std::map<std::string, std::string>& responses);
        void EnableChatBot(bool enable);
        
        // Maintenance automation
        void EnableAutoMaintenance(bool enable);
        void SetMaintenanceSchedule(const std::vector<std::string>& tasks);
        void EnableAutoBackup(bool enable);
        void SetCleanupInterval(std::chrono::hours interval);
    };

    // Main Game Enhancement Manager
    class GameEnhancementManager : public IGameModification {
    private:
        std::unique_ptr<PlayerEnhancementModule> playerEnhancement;
        std::unique_ptr<CombatEnhancementModule> combatEnhancement;
        std::unique_ptr<MovementEnhancementModule> movementEnhancement;
        std::unique_ptr<EconomicEnhancementModule> economicEnhancement;
        std::unique_ptr<ItemEnhancementModule> itemEnhancement;
        std::unique_ptr<GuildEnhancementModule> guildEnhancement;
        std::unique_ptr<AutomationModule> automation;
        
        bool enabled;
        std::map<EnhancementType, bool> moduleStates;

    public:
        GameEnhancementManager();
        virtual ~GameEnhancementManager();

        // IGameModification interface
        bool Initialize() override;
        void Shutdown() override;
        const char* GetName() const override { return "GameEnhancementManager"; }
        Priority GetPriority() const override { return Priority::HIGH; }
        bool IsEnabled() const override { return enabled; }
        void SetEnabled(bool enable) override;

        // Module management
        void EnableModule(EnhancementType type, bool enable);
        bool IsModuleEnabled(EnhancementType type) const;
        
        // Module access
        PlayerEnhancementModule* GetPlayerEnhancement() { return playerEnhancement.get(); }
        CombatEnhancementModule* GetCombatEnhancement() { return combatEnhancement.get(); }
        MovementEnhancementModule* GetMovementEnhancement() { return movementEnhancement.get(); }
        EconomicEnhancementModule* GetEconomicEnhancement() { return economicEnhancement.get(); }
        ItemEnhancementModule* GetItemEnhancement() { return itemEnhancement.get(); }
        GuildEnhancementModule* GetGuildEnhancement() { return guildEnhancement.get(); }
        AutomationModule* GetAutomation() { return automation.get(); }
        
        // Configuration
        void LoadConfiguration();
        void SaveConfiguration();
        void ResetToDefaults();
        
        // Profiles
        void SaveProfile(const std::string& name);
        void LoadProfile(const std::string& name);
        std::vector<std::string> GetAvailableProfiles();
        void DeleteProfile(const std::string& name);
    };

} // namespace NexusPro
