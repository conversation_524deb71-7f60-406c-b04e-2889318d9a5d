// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <__change_monster.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using __change_monsterctor___change_monster2_ptr = void (WINAPIV*)(struct __change_monster*);
        using __change_monsterctor___change_monster2_clbk = void (WINAPIV*)(struct __change_monster*, __change_monsterctor___change_monster2_ptr);
        
        using __change_monsterdtor___change_monster6_ptr = void (WINAPIV*)(struct __change_monster*);
        using __change_monsterdtor___change_monster6_clbk = void (WINAPIV*)(struct __change_monster*, __change_monsterdtor___change_monster6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
