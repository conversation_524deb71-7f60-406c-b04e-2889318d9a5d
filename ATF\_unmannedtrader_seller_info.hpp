// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _unmannedtrader_seller_info
    {
        unsigned int dwPrice;
        unsigned int dwSeller;
        char byRaceSexCode;
        unsigned int dwDalant;
        unsigned int dwGuildSerial;
        unsigned int dwAccountSerial;
        char szAccountID[13];
        char wszName[17];
        unsigned int dwTax;
    };
END_ATF_NAMESPACE
