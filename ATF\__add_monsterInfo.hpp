// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <__add_monster.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using __add_monsterctor___add_monster2_ptr = void (WINAPIV*)(struct __add_monster*);
        using __add_monsterctor___add_monster2_clbk = void (WINAPIV*)(struct __add_monster*, __add_monsterctor___add_monster2_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
