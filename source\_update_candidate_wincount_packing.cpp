#include <_update_candidate_wincount_packing.hpp>


START_ATF_NAMESPACE
    _update_candidate_wincount_packing::_update_candidate_wincount_packing()
    {
        using org_ptr = void (WINAPIV*)(struct _update_candidate_wincount_packing*);
        (org_ptr(0x1402b65e0L))(this);
    };
    void _update_candidate_wincount_packing::ctor__update_candidate_wincount_packing()
    {
        using org_ptr = void (WINAPIV*)(struct _update_candidate_wincount_packing*);
        (org_ptr(0x1402b65e0L))(this);
    };
END_ATF_NAMESPACE
