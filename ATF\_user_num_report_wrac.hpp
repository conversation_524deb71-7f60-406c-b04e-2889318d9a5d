// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _user_num_report_wrac
    {
        unsigned int dwAveragePerHour;
        unsigned int dwMaxPerHour;
        unsigned int dwPlayerPerRace[3];
        char szLogDate[17];
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_user_num_report_wrac, 37>(), "_user_num_report_wrac");
END_ATF_NAMESPACE
