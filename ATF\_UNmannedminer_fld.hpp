// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _UNmannedminer_fld : _base_fld
    {
        int m_bExist;
        char m_strModel[64];
        char m_strCharMeshID[64];
        char m_strName[64];
        char m_strCivil[64];
        int m_nLevelLim;
        int m_nUpLevelLim;
        int m_nExpTime;
        int m_nIconIDX;
        int m_nLevel;
        int m_nHeight;
        int m_nWidth;
        int m_nDefSklUnit;
        int m_nDefFc;
        float m_fDefGap;
        float m_fDefFacing;
        int m_nFireTol;
        int m_nWaterTol;
        int m_nSoilTol;
        int m_nWindTol;
        int m_nMaxHP;
        int m_nMoney;
        int m_nStdPrice;
        int m_nStdPoint;
        int m_nGoldPoint;
        int m_nKillPoint;
        int m_nProcPoint;
        int m_nStoragePrice;
        int m_bSell;
        int m_bExchange;
        int m_bGround;
        int m_bStoragePossible;
        int m_bUseableNormalAcc;
        int m_bUpgrade;
        char m_strTooltipIndex[64];
        int m_nDefEffType;
        int m_bIsCash;
        int m_bIsTime;
    };
END_ATF_NAMESPACE
