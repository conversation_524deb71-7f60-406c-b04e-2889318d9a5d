// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_AFPROTOCOLS.hpp>
#include <_CSADDR_INFO.hpp>
#include <_GUID.hpp>
#include <_WSAVersion.hpp>
#include <tagBLOB.hpp>


START_ATF_NAMESPACE
    struct _WSAQuerySetA
    {
        unsigned int dwSize;
        char *lpszServiceInstanceName;
        _GUID *lpServiceClassId;
        _WSAVersion *lpVersion;
        char *lpszComment;
        unsigned int dwNameSpace;
        _GUID *lpNSProviderId;
        char *lpszContext;
        unsigned int dwNumberOfProtocols;
        _AFPROTOCOLS *lpafpProtocols;
        char *lpszQueryString;
        unsigned int dwNumberOfCsAddrs;
        _CSADDR_INFO *lpcsaBuffer;
        unsigned int dwOutputFlags;
        tagBLOB *lpBlob;
    };
END_ATF_NAMESPACE
