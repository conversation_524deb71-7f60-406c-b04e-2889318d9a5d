@echo off
REM Visual Studio 2022 Setup Script for NexusPro
REM This script configures the project specifically for Visual Studio 2022

echo ========================================
echo NexusPro Visual Studio 2022 Setup
echo ========================================

REM Check if Visual Studio 2022 is installed
echo Checking for Visual Studio 2022 installation...

set "VS2022_PATH="
if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\devenv.exe" (
    set "VS2022_PATH=C:\Program Files\Microsoft Visual Studio\2022\Enterprise"
    echo Found Visual Studio 2022 Enterprise
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\devenv.exe" (
    set "VS2022_PATH=C:\Program Files\Microsoft Visual Studio\2022\Professional"
    echo Found Visual Studio 2022 Professional
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\devenv.exe" (
    set "VS2022_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community"
    echo Found Visual Studio 2022 Community
) else (
    echo ERROR: Visual Studio 2022 not found!
    echo.
    echo Please install Visual Studio 2022 with the following workloads:
    echo - Desktop development with C++
    echo.
    echo Required individual components:
    echo - MSVC v143 - VS 2022 C++ x64/x86 build tools
    echo - Windows 10/11 SDK (latest version)
    echo - CMake tools for Visual Studio
    echo - Git for Windows
    echo.
    echo Download from: https://visualstudio.microsoft.com/vs/
    pause
    exit /b 1
)

echo Visual Studio 2022 found at: %VS2022_PATH%

REM Check for required components
echo.
echo Checking for required components...

REM Check for CMake
cmake --version >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: CMake not found in PATH
    echo CMake is required for building this project
    echo Please install CMake or ensure it's in your PATH
    echo.
)

REM Check for Git
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Git not found in PATH
    echo Git is recommended for version control
    echo.
)

REM Create build directory
echo.
echo Creating build directory...
if not exist "build" mkdir build
cd build

REM Configure project for Visual Studio 2022
echo.
echo Configuring project for Visual Studio 2022...
cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DBUILD_MONOLITHIC_DLL=ON ^
    -DBUILD_TOOLS=ON ^
    -DBUILD_DOCS=OFF ^
    -DUSE_MINHOOK=ON ^
    -DENABLE_LOGGING=ON

if %errorlevel% neq 0 (
    echo.
    echo ERROR: CMake configuration failed!
    echo Please check the error messages above.
    echo.
    echo Common solutions:
    echo 1. Ensure Visual Studio 2022 C++ workload is installed
    echo 2. Update CMake to the latest version
    echo 3. Check that Windows SDK is installed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Configuration completed successfully!
echo ========================================
echo.
echo Project configured for Visual Studio 2022 with:
echo - Platform: x64
echo - Monolithic DLL: Enabled
echo - Development Tools: Enabled
echo - MinHook Support: Enabled
echo - Logging: Enabled
echo.
echo Next steps:
echo 1. Open the solution: start NexusPro.sln
echo 2. Set build configuration to Release
echo 3. Build the solution (Ctrl+Shift+B)
echo 4. Use NexusProInjector.exe to inject NexusProMod.dll
echo.
echo Solution file: %CD%\NexusPro.sln
echo.

REM Ask if user wants to open Visual Studio
set /p "OPEN_VS=Open Visual Studio 2022 now? (y/n): "
if /i "%OPEN_VS%"=="y" (
    echo Opening Visual Studio 2022...
    start "" "%VS2022_PATH%\Common7\IDE\devenv.exe" "NexusPro.sln"
) else (
    echo.
    echo To open later, run:
    echo   start build\NexusPro.sln
)

echo.
echo Setup complete!
pause

cd ..
