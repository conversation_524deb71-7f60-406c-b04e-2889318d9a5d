#include <GUILD_BATTLE__CPossibleBattleGuildListManagerDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Detail
        {
            
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerctor_CPossibleBattleGuildListManager2_ptr GUILD_BATTLE__CPossibleBattleGuildListManagerctor_CPossibleBattleGuildListManager2_next(nullptr);
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerctor_CPossibleBattleGuildListManager2_clbk GUILD_BATTLE__CPossibleBattleGuildListManagerctor_CPossibleBattleGuildListManager2_user(nullptr);
            
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerClear4_ptr GUILD_BATTLE__CPossibleBattleGuildListManagerClear4_next(nullptr);
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerClear4_clbk GUILD_BATTLE__CPossibleBattleGuildListManagerClear4_user(nullptr);
            
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerDestroy6_ptr GUILD_BATTLE__CPossibleBattleGuildListManagerDestroy6_next(nullptr);
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerDestroy6_clbk GUILD_BATTLE__CPossibleBattleGuildListManagerDestroy6_user(nullptr);
            
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerDoDayChangedWork8_ptr GUILD_BATTLE__CPossibleBattleGuildListManagerDoDayChangedWork8_next(nullptr);
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerDoDayChangedWork8_clbk GUILD_BATTLE__CPossibleBattleGuildListManagerDoDayChangedWork8_user(nullptr);
            
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerInit10_ptr GUILD_BATTLE__CPossibleBattleGuildListManagerInit10_next(nullptr);
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerInit10_clbk GUILD_BATTLE__CPossibleBattleGuildListManagerInit10_user(nullptr);
            
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerInstance12_ptr GUILD_BATTLE__CPossibleBattleGuildListManagerInstance12_next(nullptr);
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerInstance12_clbk GUILD_BATTLE__CPossibleBattleGuildListManagerInstance12_user(nullptr);
            
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerLoad14_ptr GUILD_BATTLE__CPossibleBattleGuildListManagerLoad14_next(nullptr);
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerLoad14_clbk GUILD_BATTLE__CPossibleBattleGuildListManagerLoad14_user(nullptr);
            
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerMakePage16_ptr GUILD_BATTLE__CPossibleBattleGuildListManagerMakePage16_next(nullptr);
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerMakePage16_clbk GUILD_BATTLE__CPossibleBattleGuildListManagerMakePage16_user(nullptr);
            
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerSend18_ptr GUILD_BATTLE__CPossibleBattleGuildListManagerSend18_next(nullptr);
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerSend18_clbk GUILD_BATTLE__CPossibleBattleGuildListManagerSend18_user(nullptr);
            
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerSendErrorResult20_ptr GUILD_BATTLE__CPossibleBattleGuildListManagerSendErrorResult20_next(nullptr);
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerSendErrorResult20_clbk GUILD_BATTLE__CPossibleBattleGuildListManagerSendErrorResult20_user(nullptr);
            
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerSendFirst22_ptr GUILD_BATTLE__CPossibleBattleGuildListManagerSendFirst22_next(nullptr);
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerSendFirst22_clbk GUILD_BATTLE__CPossibleBattleGuildListManagerSendFirst22_user(nullptr);
            
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerSendInfo24_ptr GUILD_BATTLE__CPossibleBattleGuildListManagerSendInfo24_next(nullptr);
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerSendInfo24_clbk GUILD_BATTLE__CPossibleBattleGuildListManagerSendInfo24_user(nullptr);
            
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerUpdateGuildList26_ptr GUILD_BATTLE__CPossibleBattleGuildListManagerUpdateGuildList26_next(nullptr);
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerUpdateGuildList26_clbk GUILD_BATTLE__CPossibleBattleGuildListManagerUpdateGuildList26_user(nullptr);
            
            
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerdtor_CPossibleBattleGuildListManager30_ptr GUILD_BATTLE__CPossibleBattleGuildListManagerdtor_CPossibleBattleGuildListManager30_next(nullptr);
            Info::GUILD_BATTLE__CPossibleBattleGuildListManagerdtor_CPossibleBattleGuildListManager30_clbk GUILD_BATTLE__CPossibleBattleGuildListManagerdtor_CPossibleBattleGuildListManager30_user(nullptr);
            
            
            void GUILD_BATTLE__CPossibleBattleGuildListManagerctor_CPossibleBattleGuildListManager2_wrapper(struct GUILD_BATTLE::CPossibleBattleGuildListManager* _this)
            {
               GUILD_BATTLE__CPossibleBattleGuildListManagerctor_CPossibleBattleGuildListManager2_user(_this, GUILD_BATTLE__CPossibleBattleGuildListManagerctor_CPossibleBattleGuildListManager2_next);
            };
            void GUILD_BATTLE__CPossibleBattleGuildListManagerClear4_wrapper(struct GUILD_BATTLE::CPossibleBattleGuildListManager* _this)
            {
               GUILD_BATTLE__CPossibleBattleGuildListManagerClear4_user(_this, GUILD_BATTLE__CPossibleBattleGuildListManagerClear4_next);
            };
            void GUILD_BATTLE__CPossibleBattleGuildListManagerDestroy6_wrapper()
            {
               GUILD_BATTLE__CPossibleBattleGuildListManagerDestroy6_user(GUILD_BATTLE__CPossibleBattleGuildListManagerDestroy6_next);
            };
            void GUILD_BATTLE__CPossibleBattleGuildListManagerDoDayChangedWork8_wrapper(struct GUILD_BATTLE::CPossibleBattleGuildListManager* _this)
            {
               GUILD_BATTLE__CPossibleBattleGuildListManagerDoDayChangedWork8_user(_this, GUILD_BATTLE__CPossibleBattleGuildListManagerDoDayChangedWork8_next);
            };
            bool GUILD_BATTLE__CPossibleBattleGuildListManagerInit10_wrapper(struct GUILD_BATTLE::CPossibleBattleGuildListManager* _this)
            {
               return GUILD_BATTLE__CPossibleBattleGuildListManagerInit10_user(_this, GUILD_BATTLE__CPossibleBattleGuildListManagerInit10_next);
            };
            struct GUILD_BATTLE::CPossibleBattleGuildListManager* GUILD_BATTLE__CPossibleBattleGuildListManagerInstance12_wrapper()
            {
               return GUILD_BATTLE__CPossibleBattleGuildListManagerInstance12_user(GUILD_BATTLE__CPossibleBattleGuildListManagerInstance12_next);
            };
            bool GUILD_BATTLE__CPossibleBattleGuildListManagerLoad14_wrapper(struct GUILD_BATTLE::CPossibleBattleGuildListManager* _this)
            {
               return GUILD_BATTLE__CPossibleBattleGuildListManagerLoad14_user(_this, GUILD_BATTLE__CPossibleBattleGuildListManagerLoad14_next);
            };
            bool GUILD_BATTLE__CPossibleBattleGuildListManagerMakePage16_wrapper(struct GUILD_BATTLE::CPossibleBattleGuildListManager* _this, char byRace, char ucPage, uint16_t* wLastGuildInx)
            {
               return GUILD_BATTLE__CPossibleBattleGuildListManagerMakePage16_user(_this, byRace, ucPage, wLastGuildInx, GUILD_BATTLE__CPossibleBattleGuildListManagerMakePage16_next);
            };
            void GUILD_BATTLE__CPossibleBattleGuildListManagerSend18_wrapper(struct GUILD_BATTLE::CPossibleBattleGuildListManager* _this, int n, char byRace, char byPage, unsigned int dwVer)
            {
               GUILD_BATTLE__CPossibleBattleGuildListManagerSend18_user(_this, n, byRace, byPage, dwVer, GUILD_BATTLE__CPossibleBattleGuildListManagerSend18_next);
            };
            void GUILD_BATTLE__CPossibleBattleGuildListManagerSendErrorResult20_wrapper(struct GUILD_BATTLE::CPossibleBattleGuildListManager* _this, int n, char byRet)
            {
               GUILD_BATTLE__CPossibleBattleGuildListManagerSendErrorResult20_user(_this, n, byRet, GUILD_BATTLE__CPossibleBattleGuildListManagerSendErrorResult20_next);
            };
            void GUILD_BATTLE__CPossibleBattleGuildListManagerSendFirst22_wrapper(struct GUILD_BATTLE::CPossibleBattleGuildListManager* _this, int n, char byRace)
            {
               GUILD_BATTLE__CPossibleBattleGuildListManagerSendFirst22_user(_this, n, byRace, GUILD_BATTLE__CPossibleBattleGuildListManagerSendFirst22_next);
            };
            char GUILD_BATTLE__CPossibleBattleGuildListManagerSendInfo24_wrapper(struct GUILD_BATTLE::CPossibleBattleGuildListManager* _this, int n, char byRace, char byPage, unsigned int dwVer)
            {
               return GUILD_BATTLE__CPossibleBattleGuildListManagerSendInfo24_user(_this, n, byRace, byPage, dwVer, GUILD_BATTLE__CPossibleBattleGuildListManagerSendInfo24_next);
            };
            void GUILD_BATTLE__CPossibleBattleGuildListManagerUpdateGuildList26_wrapper(struct GUILD_BATTLE::CPossibleBattleGuildListManager* _this)
            {
               GUILD_BATTLE__CPossibleBattleGuildListManagerUpdateGuildList26_user(_this, GUILD_BATTLE__CPossibleBattleGuildListManagerUpdateGuildList26_next);
            };
            
            void GUILD_BATTLE__CPossibleBattleGuildListManagerdtor_CPossibleBattleGuildListManager30_wrapper(struct GUILD_BATTLE::CPossibleBattleGuildListManager* _this)
            {
               GUILD_BATTLE__CPossibleBattleGuildListManagerdtor_CPossibleBattleGuildListManager30_user(_this, GUILD_BATTLE__CPossibleBattleGuildListManagerdtor_CPossibleBattleGuildListManager30_next);
            };
            
            ::std::array<hook_record, 14> CPossibleBattleGuildListManager_functions = 
            {
                _hook_record {
                    (LPVOID)0x1403c9530L,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerctor_CPossibleBattleGuildListManager2_user,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerctor_CPossibleBattleGuildListManager2_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CPossibleBattleGuildListManagerctor_CPossibleBattleGuildListManager2_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CPossibleBattleGuildListManager::*)())&GUILD_BATTLE::CPossibleBattleGuildListManager::ctor_CPossibleBattleGuildListManager)
                },
                _hook_record {
                    (LPVOID)0x1403d98e0L,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerClear4_user,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerClear4_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CPossibleBattleGuildListManagerClear4_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CPossibleBattleGuildListManager::*)())&GUILD_BATTLE::CPossibleBattleGuildListManager::Clear)
                },
                _hook_record {
                    (LPVOID)0x1403c9710L,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerDestroy6_user,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerDestroy6_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CPossibleBattleGuildListManagerDestroy6_wrapper),
                    (LPVOID)cast_pointer_function((void(*)())&GUILD_BATTLE::CPossibleBattleGuildListManager::Destroy)
                },
                _hook_record {
                    (LPVOID)0x1403c9ae0L,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerDoDayChangedWork8_user,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerDoDayChangedWork8_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CPossibleBattleGuildListManagerDoDayChangedWork8_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CPossibleBattleGuildListManager::*)())&GUILD_BATTLE::CPossibleBattleGuildListManager::DoDayChangedWork)
                },
                _hook_record {
                    (LPVOID)0x1403c9790L,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerInit10_user,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerInit10_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CPossibleBattleGuildListManagerInit10_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CPossibleBattleGuildListManager::*)())&GUILD_BATTLE::CPossibleBattleGuildListManager::Init)
                },
                _hook_record {
                    (LPVOID)0x1403c9650L,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerInstance12_user,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerInstance12_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CPossibleBattleGuildListManagerInstance12_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CPossibleBattleGuildListManager*(*)())&GUILD_BATTLE::CPossibleBattleGuildListManager::Instance)
                },
                _hook_record {
                    (LPVOID)0x1403c99f0L,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerLoad14_user,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerLoad14_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CPossibleBattleGuildListManagerLoad14_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CPossibleBattleGuildListManager::*)())&GUILD_BATTLE::CPossibleBattleGuildListManager::Load)
                },
                _hook_record {
                    (LPVOID)0x1403c9d70L,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerMakePage16_user,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerMakePage16_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CPossibleBattleGuildListManagerMakePage16_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CPossibleBattleGuildListManager::*)(char, char, uint16_t*))&GUILD_BATTLE::CPossibleBattleGuildListManager::MakePage)
                },
                _hook_record {
                    (LPVOID)0x1403d9990L,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerSend18_user,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerSend18_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CPossibleBattleGuildListManagerSend18_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CPossibleBattleGuildListManager::*)(int, char, char, unsigned int))&GUILD_BATTLE::CPossibleBattleGuildListManager::Send)
                },
                _hook_record {
                    (LPVOID)0x1403ca260L,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerSendErrorResult20_user,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerSendErrorResult20_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CPossibleBattleGuildListManagerSendErrorResult20_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CPossibleBattleGuildListManager::*)(int, char))&GUILD_BATTLE::CPossibleBattleGuildListManager::SendErrorResult)
                },
                _hook_record {
                    (LPVOID)0x1403d9920L,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerSendFirst22_user,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerSendFirst22_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CPossibleBattleGuildListManagerSendFirst22_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CPossibleBattleGuildListManager::*)(int, char))&GUILD_BATTLE::CPossibleBattleGuildListManager::SendFirst)
                },
                _hook_record {
                    (LPVOID)0x1403ca110L,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerSendInfo24_user,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerSendInfo24_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CPossibleBattleGuildListManagerSendInfo24_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CPossibleBattleGuildListManager::*)(int, char, char, unsigned int))&GUILD_BATTLE::CPossibleBattleGuildListManager::SendInfo)
                },
                _hook_record {
                    (LPVOID)0x1403c9b90L,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerUpdateGuildList26_user,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerUpdateGuildList26_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CPossibleBattleGuildListManagerUpdateGuildList26_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CPossibleBattleGuildListManager::*)())&GUILD_BATTLE::CPossibleBattleGuildListManager::UpdateGuildList)
                },
                _hook_record {
                    (LPVOID)0x1403c9580L,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerdtor_CPossibleBattleGuildListManager30_user,
                    (LPVOID *)&GUILD_BATTLE__CPossibleBattleGuildListManagerdtor_CPossibleBattleGuildListManager30_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CPossibleBattleGuildListManagerdtor_CPossibleBattleGuildListManager30_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CPossibleBattleGuildListManager::*)())&GUILD_BATTLE::CPossibleBattleGuildListManager::dtor_CPossibleBattleGuildListManager)
                },
            };
        }; // end namespace Detail
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE
