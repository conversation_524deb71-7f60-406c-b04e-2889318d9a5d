cmake_minimum_required(VERSION 3.16)

# ATF Monolithic DLL Project
project(ATFMonolithicDLL
    VERSION 1.0.0
    DESCRIPTION "ATF Game Modification Monolithic DLL"
    LANGUAGES CXX
)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Platform-specific settings
if(WIN32)
    set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS OFF)  # We'll use explicit exports
    add_definitions(-DWIN32_LEAN_AND_MEAN)
    add_definitions(-DNOMINMAX)
    add_definitions(-DUNICODE -D_UNICODE)
endif()

# Compiler-specific settings
if(MSVC)
    # Enable parallel compilation
    add_compile_options(/MP)
    # Set warning level
    add_compile_options(/W3)
    # Disable specific warnings
    add_compile_options(/wd4996 /wd4267 /wd4244 /wd4018)
    # Enable function-level linking
    add_compile_options(/Gy)
    # Enable string pooling
    add_compile_options(/GF)
else()
    # GCC/Clang settings
    add_compile_options(-Wall -Wextra -Wno-unused-parameter)
    add_compile_options(-ffunction-sections -fdata-sections)
endif()

# Build configuration
option(BUILD_CONSOLE_TEST "Build console test application" OFF)
option(USE_MINHOOK "Use MinHook library for function hooking" ON)
option(ENABLE_LOGGING "Enable detailed logging" ON)

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/../ATF
    ${CMAKE_CURRENT_SOURCE_DIR}/../ATF/common
)

# Source files for the DLL
set(DLL_SOURCES
    DllMain.cpp
    ATFMod.cpp
    modifications/PlayerMod.cpp
    # Add other modification source files here
    # modifications/GuildMod.cpp
    # modifications/CombatMod.cpp
    # modifications/ItemMod.cpp
    # modifications/NetworkMod.cpp
    # modifications/UtilityMod.cpp
)

# Header files
set(DLL_HEADERS
    ATFMod.hpp
    modifications/PlayerMod.hpp
    # Add other modification header files here
)

# Create the monolithic DLL
add_library(ATFMod SHARED ${DLL_SOURCES} ${DLL_HEADERS})

# Set DLL properties
set_target_properties(ATFMod PROPERTIES
    OUTPUT_NAME "ATFMod"
    PREFIX ""
    SUFFIX ".dll"
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
)

# Link libraries
if(WIN32)
    target_link_libraries(ATFMod PRIVATE
        kernel32
        user32
        psapi
        # Add MinHook library if available
        # minhook
    )
endif()

# Preprocessor definitions
if(ENABLE_LOGGING)
    target_compile_definitions(ATFMod PRIVATE ENABLE_LOGGING=1)
endif()

# MinHook integration
if(USE_MINHOOK)
    # Try to find MinHook
    find_path(MINHOOK_INCLUDE_DIR MinHook.h
        PATHS
        ${CMAKE_CURRENT_SOURCE_DIR}/third_party/minhook/include
        ${CMAKE_CURRENT_SOURCE_DIR}/../third_party/minhook/include
        C:/MinHook/include
    )
    
    find_library(MINHOOK_LIBRARY
        NAMES MinHook libMinHook
        PATHS
        ${CMAKE_CURRENT_SOURCE_DIR}/third_party/minhook/lib
        ${CMAKE_CURRENT_SOURCE_DIR}/../third_party/minhook/lib
        C:/MinHook/lib
    )
    
    if(MINHOOK_INCLUDE_DIR AND MINHOOK_LIBRARY)
        target_include_directories(ATFMod PRIVATE ${MINHOOK_INCLUDE_DIR})
        target_link_libraries(ATFMod PRIVATE ${MINHOOK_LIBRARY})
        target_compile_definitions(ATFMod PRIVATE USE_MINHOOK=1)
        message(STATUS "MinHook found and enabled")
    else()
        message(WARNING "MinHook not found, function hooking will be limited")
        target_compile_definitions(ATFMod PRIVATE USE_MINHOOK=0)
    endif()
else()
    target_compile_definitions(ATFMod PRIVATE USE_MINHOOK=0)
endif()

# Console test application (optional)
if(BUILD_CONSOLE_TEST)
    add_executable(ATFModTest
        DllMain.cpp
        ATFMod.cpp
        modifications/PlayerMod.cpp
    )
    
    target_compile_definitions(ATFModTest PRIVATE _CONSOLE=1)
    target_include_directories(ATFModTest PRIVATE ${CMAKE_CURRENT_SOURCE_DIR})
    
    if(WIN32)
        target_link_libraries(ATFModTest PRIVATE
            kernel32
            user32
            psapi
        )
    endif()
    
    set_target_properties(ATFModTest PROPERTIES
        OUTPUT_NAME "ATFModTest"
        RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    )
endif()

# Installation
install(TARGETS ATFMod
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

# Install headers for development
install(FILES ATFMod.hpp
    DESTINATION include
)

install(DIRECTORY modifications/
    DESTINATION include/modifications
    FILES_MATCHING PATTERN "*.hpp"
)

# Create a simple injector tool
add_executable(ATFInjector
    injector/main.cpp
)

target_link_libraries(ATFInjector PRIVATE
    kernel32
    user32
    psapi
)

set_target_properties(ATFInjector PROPERTIES
    OUTPUT_NAME "ATFInjector"
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

# Configuration summary
message(STATUS "=== ATF Monolithic DLL Configuration ===")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "Platform: ${CMAKE_SYSTEM_NAME}")
message(STATUS "Console test: ${BUILD_CONSOLE_TEST}")
message(STATUS "MinHook: ${USE_MINHOOK}")
message(STATUS "Logging: ${ENABLE_LOGGING}")
message(STATUS "Output directory: ${CMAKE_BINARY_DIR}/bin")
message(STATUS "========================================")

# Custom targets for development
add_custom_target(clean_logs
    COMMAND ${CMAKE_COMMAND} -E remove -f "${CMAKE_BINARY_DIR}/bin/ATFMod.log"
    COMMENT "Cleaning log files"
)

add_custom_target(package_dll
    COMMAND ${CMAKE_COMMAND} -E copy "$<TARGET_FILE:ATFMod>" "${CMAKE_BINARY_DIR}/ATFMod_Release.dll"
    COMMAND ${CMAKE_COMMAND} -E copy "${CMAKE_CURRENT_SOURCE_DIR}/README_DLL.md" "${CMAKE_BINARY_DIR}/"
    DEPENDS ATFMod
    COMMENT "Packaging DLL for distribution"
)
