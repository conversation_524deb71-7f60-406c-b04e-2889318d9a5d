#include <CUserDBDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace Detail
    {
        Info::CUserDBAlive_Char_Complete2_ptr CUserDBAlive_Char_Complete2_next(nullptr);
        Info::CUserDBAlive_Char_Complete2_clbk CUserDBAlive_Char_Complete2_user(nullptr);
        
        Info::CUserDBAlive_Char_Request4_ptr CUserDBAlive_Char_Request4_next(nullptr);
        Info::CUserDBAlive_Char_Request4_clbk CUserDBAlive_Char_Request4_user(nullptr);
        
        
        Info::CUserDBctor_CUserDB6_ptr CUserDBctor_CUserDB6_next(nullptr);
        Info::CUserDBctor_CUserDB6_clbk CUserDBctor_CUserDB6_user(nullptr);
        
        Info::CUserDBCalcRadarDelay8_ptr CUserDBCalcRadarDelay8_next(nullptr);
        Info::CUserDBCalcRadarDelay8_clbk CUserDBCalcRadarDelay8_user(nullptr);
        
        Info::CUserDBCheckDQSLoadCharacterData10_ptr CUserDBCheckDQSLoadCharacterData10_next(nullptr);
        Info::CUserDBCheckDQSLoadCharacterData10_clbk CUserDBCheckDQSLoadCharacterData10_user(nullptr);
        
        Info::CUserDBClearBillingData12_ptr CUserDBClearBillingData12_next(nullptr);
        Info::CUserDBClearBillingData12_clbk CUserDBClearBillingData12_user(nullptr);
        
        Info::CUserDBCont_UserSave_Complete14_ptr CUserDBCont_UserSave_Complete14_next(nullptr);
        Info::CUserDBCont_UserSave_Complete14_clbk CUserDBCont_UserSave_Complete14_user(nullptr);
        
        Info::CUserDBDataValidCheckRevise16_ptr CUserDBDataValidCheckRevise16_next(nullptr);
        Info::CUserDBDataValidCheckRevise16_clbk CUserDBDataValidCheckRevise16_user(nullptr);
        
        Info::CUserDBDelPostData18_ptr CUserDBDelPostData18_next(nullptr);
        Info::CUserDBDelPostData18_clbk CUserDBDelPostData18_user(nullptr);
        
        Info::CUserDBDelete_Char_Complete20_ptr CUserDBDelete_Char_Complete20_next(nullptr);
        Info::CUserDBDelete_Char_Complete20_clbk CUserDBDelete_Char_Complete20_user(nullptr);
        
        Info::CUserDBDelete_Char_Request22_ptr CUserDBDelete_Char_Request22_next(nullptr);
        Info::CUserDBDelete_Char_Request22_clbk CUserDBDelete_Char_Request22_user(nullptr);
        
        Info::CUserDBDummyCreate24_ptr CUserDBDummyCreate24_next(nullptr);
        Info::CUserDBDummyCreate24_clbk CUserDBDummyCreate24_user(nullptr);
        
        Info::CUserDBEnter_Account26_ptr CUserDBEnter_Account26_next(nullptr);
        Info::CUserDBEnter_Account26_clbk CUserDBEnter_Account26_user(nullptr);
        
        Info::CUserDBExit_Account_Complete28_ptr CUserDBExit_Account_Complete28_next(nullptr);
        Info::CUserDBExit_Account_Complete28_clbk CUserDBExit_Account_Complete28_user(nullptr);
        
        Info::CUserDBExit_Account_Request30_ptr CUserDBExit_Account_Request30_next(nullptr);
        Info::CUserDBExit_Account_Request30_clbk CUserDBExit_Account_Request30_user(nullptr);
        
        Info::CUserDBFirstSettingData32_ptr CUserDBFirstSettingData32_next(nullptr);
        Info::CUserDBFirstSettingData32_clbk CUserDBFirstSettingData32_user(nullptr);
        
        Info::CUserDBForceCloseCommand34_ptr CUserDBForceCloseCommand34_next(nullptr);
        Info::CUserDBForceCloseCommand34_clbk CUserDBForceCloseCommand34_user(nullptr);
        
        Info::CUserDBGetActPoint36_ptr CUserDBGetActPoint36_next(nullptr);
        Info::CUserDBGetActPoint36_clbk CUserDBGetActPoint36_user(nullptr);
        
        Info::CUserDBGetBillingType38_ptr CUserDBGetBillingType38_next(nullptr);
        Info::CUserDBGetBillingType38_clbk CUserDBGetBillingType38_user(nullptr);
        
        Info::CUserDBGetPtrActPoint40_ptr CUserDBGetPtrActPoint40_next(nullptr);
        Info::CUserDBGetPtrActPoint40_clbk CUserDBGetPtrActPoint40_user(nullptr);
        
        Info::CUserDBInform_For_Exit_By_FireguardBlock42_ptr CUserDBInform_For_Exit_By_FireguardBlock42_next(nullptr);
        Info::CUserDBInform_For_Exit_By_FireguardBlock42_clbk CUserDBInform_For_Exit_By_FireguardBlock42_user(nullptr);
        
        Info::CUserDBInit44_ptr CUserDBInit44_next(nullptr);
        Info::CUserDBInit44_clbk CUserDBInit44_user(nullptr);
        
        Info::CUserDBInitClass46_ptr CUserDBInitClass46_next(nullptr);
        Info::CUserDBInitClass46_clbk CUserDBInitClass46_user(nullptr);
        
        Info::CUserDBInsert_Char_Complete48_ptr CUserDBInsert_Char_Complete48_next(nullptr);
        Info::CUserDBInsert_Char_Complete48_clbk CUserDBInsert_Char_Complete48_user(nullptr);
        
        Info::CUserDBInsert_Char_Request50_ptr CUserDBInsert_Char_Request50_next(nullptr);
        Info::CUserDBInsert_Char_Request50_clbk CUserDBInsert_Char_Request50_user(nullptr);
        
        Info::CUserDBIsContPushBefore52_ptr CUserDBIsContPushBefore52_next(nullptr);
        Info::CUserDBIsContPushBefore52_clbk CUserDBIsContPushBefore52_user(nullptr);
        
        Info::CUserDBIsExistRequestMoveCharacterList54_ptr CUserDBIsExistRequestMoveCharacterList54_next(nullptr);
        Info::CUserDBIsExistRequestMoveCharacterList54_clbk CUserDBIsExistRequestMoveCharacterList54_user(nullptr);
        
        Info::CUserDBIsReturnPostUpdate56_ptr CUserDBIsReturnPostUpdate56_next(nullptr);
        Info::CUserDBIsReturnPostUpdate56_clbk CUserDBIsReturnPostUpdate56_user(nullptr);
        
        Info::CUserDBLobby_Char_Complete58_ptr CUserDBLobby_Char_Complete58_next(nullptr);
        Info::CUserDBLobby_Char_Complete58_clbk CUserDBLobby_Char_Complete58_user(nullptr);
        
        Info::CUserDBLobby_Char_Request60_ptr CUserDBLobby_Char_Request60_next(nullptr);
        Info::CUserDBLobby_Char_Request60_clbk CUserDBLobby_Char_Request60_user(nullptr);
        
        Info::CUserDBOnLoop_Static62_ptr CUserDBOnLoop_Static62_next(nullptr);
        Info::CUserDBOnLoop_Static62_clbk CUserDBOnLoop_Static62_user(nullptr);
        
        Info::CUserDBParamInit64_ptr CUserDBParamInit64_next(nullptr);
        Info::CUserDBParamInit64_clbk CUserDBParamInit64_user(nullptr);
        
        Info::CUserDBReRangeClientIndex66_ptr CUserDBReRangeClientIndex66_next(nullptr);
        Info::CUserDBReRangeClientIndex66_clbk CUserDBReRangeClientIndex66_user(nullptr);
        
        Info::CUserDBReged_Char_Complete68_ptr CUserDBReged_Char_Complete68_next(nullptr);
        Info::CUserDBReged_Char_Complete68_clbk CUserDBReged_Char_Complete68_user(nullptr);
        
        Info::CUserDBReged_Char_Request70_ptr CUserDBReged_Char_Request70_next(nullptr);
        Info::CUserDBReged_Char_Request70_clbk CUserDBReged_Char_Request70_user(nullptr);
        
        Info::CUserDBSelect_Char_Complete72_ptr CUserDBSelect_Char_Complete72_next(nullptr);
        Info::CUserDBSelect_Char_Complete72_clbk CUserDBSelect_Char_Complete72_user(nullptr);
        
        Info::CUserDBSelect_Char_Request74_ptr CUserDBSelect_Char_Request74_next(nullptr);
        Info::CUserDBSelect_Char_Request74_clbk CUserDBSelect_Char_Request74_user(nullptr);
        
        Info::CUserDBSendMsgAccount_UILockRefresh_Update76_ptr CUserDBSendMsgAccount_UILockRefresh_Update76_next(nullptr);
        Info::CUserDBSendMsgAccount_UILockRefresh_Update76_clbk CUserDBSendMsgAccount_UILockRefresh_Update76_user(nullptr);
        
        Info::CUserDBSendMsg_BillingInfo78_ptr CUserDBSendMsg_BillingInfo78_next(nullptr);
        Info::CUserDBSendMsg_BillingInfo78_clbk CUserDBSendMsg_BillingInfo78_user(nullptr);
        
        Info::CUserDBSendMsg_Inform_UILock80_ptr CUserDBSendMsg_Inform_UILock80_next(nullptr);
        Info::CUserDBSendMsg_Inform_UILock80_clbk CUserDBSendMsg_Inform_UILock80_user(nullptr);
        
        Info::CUserDBSetActPoint82_ptr CUserDBSetActPoint82_next(nullptr);
        Info::CUserDBSetActPoint82_clbk CUserDBSetActPoint82_user(nullptr);
        
        Info::CUserDBSetBillingData84_ptr CUserDBSetBillingData84_next(nullptr);
        Info::CUserDBSetBillingData84_clbk CUserDBSetBillingData84_user(nullptr);
        
        Info::CUserDBSetBillingData86_ptr CUserDBSetBillingData86_next(nullptr);
        Info::CUserDBSetBillingData86_clbk CUserDBSetBillingData86_user(nullptr);
        
        Info::CUserDBSetBillingNoLogout88_ptr CUserDBSetBillingNoLogout88_next(nullptr);
        Info::CUserDBSetBillingNoLogout88_clbk CUserDBSetBillingNoLogout88_user(nullptr);
        
        Info::CUserDBSetChatLock90_ptr CUserDBSetChatLock90_next(nullptr);
        Info::CUserDBSetChatLock90_clbk CUserDBSetChatLock90_user(nullptr);
        
        Info::CUserDBSetDBPostData92_ptr CUserDBSetDBPostData92_next(nullptr);
        Info::CUserDBSetDBPostData92_clbk CUserDBSetDBPostData92_user(nullptr);
        
        Info::CUserDBSetNewDBPostData94_ptr CUserDBSetNewDBPostData94_next(nullptr);
        Info::CUserDBSetNewDBPostData94_clbk CUserDBSetNewDBPostData94_user(nullptr);
        
        Info::CUserDBSetRadarDelay96_ptr CUserDBSetRadarDelay96_next(nullptr);
        Info::CUserDBSetRadarDelay96_clbk CUserDBSetRadarDelay96_user(nullptr);
        
        Info::CUserDBSetRemainTime98_ptr CUserDBSetRemainTime98_next(nullptr);
        Info::CUserDBSetRemainTime98_clbk CUserDBSetRemainTime98_user(nullptr);
        
        Info::CUserDBSetWorldCLID100_ptr CUserDBSetWorldCLID100_next(nullptr);
        Info::CUserDBSetWorldCLID100_clbk CUserDBSetWorldCLID100_user(nullptr);
        
        Info::CUserDBSetting_Class102_ptr CUserDBSetting_Class102_next(nullptr);
        Info::CUserDBSetting_Class102_clbk CUserDBSetting_Class102_user(nullptr);
        
        Info::CUserDBStartFieldMode104_ptr CUserDBStartFieldMode104_next(nullptr);
        Info::CUserDBStartFieldMode104_clbk CUserDBStartFieldMode104_user(nullptr);
        
        Info::CUserDBTotalPlayMinCheck106_ptr CUserDBTotalPlayMinCheck106_next(nullptr);
        Info::CUserDBTotalPlayMinCheck106_clbk CUserDBTotalPlayMinCheck106_user(nullptr);
        
        Info::CUserDBUILockInfo_Init108_ptr CUserDBUILockInfo_Init108_next(nullptr);
        Info::CUserDBUILockInfo_Init108_clbk CUserDBUILockInfo_Init108_user(nullptr);
        
        Info::CUserDBUILockInfo_Update110_ptr CUserDBUILockInfo_Update110_next(nullptr);
        Info::CUserDBUILockInfo_Update110_clbk CUserDBUILockInfo_Update110_user(nullptr);
        
        Info::CUserDBUpdateContUserSave112_ptr CUserDBUpdateContUserSave112_next(nullptr);
        Info::CUserDBUpdateContUserSave112_clbk CUserDBUpdateContUserSave112_user(nullptr);
        
        Info::CUserDBUpdate_AddBuddy114_ptr CUserDBUpdate_AddBuddy114_next(nullptr);
        Info::CUserDBUpdate_AddBuddy114_clbk CUserDBUpdate_AddBuddy114_user(nullptr);
        
        Info::CUserDBUpdate_AlterPvPCashBag116_ptr CUserDBUpdate_AlterPvPCashBag116_next(nullptr);
        Info::CUserDBUpdate_AlterPvPCashBag116_clbk CUserDBUpdate_AlterPvPCashBag116_user(nullptr);
        
        Info::CUserDBUpdate_AlterPvPPoint118_ptr CUserDBUpdate_AlterPvPPoint118_next(nullptr);
        Info::CUserDBUpdate_AlterPvPPoint118_clbk CUserDBUpdate_AlterPvPPoint118_user(nullptr);
        
        Info::CUserDBUpdate_AutoTradeAllClear120_ptr CUserDBUpdate_AutoTradeAllClear120_next(nullptr);
        Info::CUserDBUpdate_AutoTradeAllClear120_clbk CUserDBUpdate_AutoTradeAllClear120_user(nullptr);
        
        Info::CUserDBUpdate_BagNum122_ptr CUserDBUpdate_BagNum122_next(nullptr);
        Info::CUserDBUpdate_BagNum122_clbk CUserDBUpdate_BagNum122_user(nullptr);
        
        Info::CUserDBUpdate_Bind124_ptr CUserDBUpdate_Bind124_next(nullptr);
        Info::CUserDBUpdate_Bind124_clbk CUserDBUpdate_Bind124_user(nullptr);
        
        Info::CUserDBUpdate_BossCryMsg126_ptr CUserDBUpdate_BossCryMsg126_next(nullptr);
        Info::CUserDBUpdate_BossCryMsg126_clbk CUserDBUpdate_BossCryMsg126_user(nullptr);
        
        Info::CUserDBUpdate_Class128_ptr CUserDBUpdate_Class128_next(nullptr);
        Info::CUserDBUpdate_Class128_clbk CUserDBUpdate_Class128_user(nullptr);
        
        Info::CUserDBUpdate_CombineExResult_Pop130_ptr CUserDBUpdate_CombineExResult_Pop130_next(nullptr);
        Info::CUserDBUpdate_CombineExResult_Pop130_clbk CUserDBUpdate_CombineExResult_Pop130_user(nullptr);
        
        Info::CUserDBUpdate_CombineExResult_Push132_ptr CUserDBUpdate_CombineExResult_Push132_next(nullptr);
        Info::CUserDBUpdate_CombineExResult_Push132_clbk CUserDBUpdate_CombineExResult_Push132_user(nullptr);
        
        Info::CUserDBUpdate_CopyAll134_ptr CUserDBUpdate_CopyAll134_next(nullptr);
        Info::CUserDBUpdate_CopyAll134_clbk CUserDBUpdate_CopyAll134_user(nullptr);
        
        Info::CUserDBUpdate_CuttingEmpty136_ptr CUserDBUpdate_CuttingEmpty136_next(nullptr);
        Info::CUserDBUpdate_CuttingEmpty136_clbk CUserDBUpdate_CuttingEmpty136_user(nullptr);
        
        Info::CUserDBUpdate_CuttingPush138_ptr CUserDBUpdate_CuttingPush138_next(nullptr);
        Info::CUserDBUpdate_CuttingPush138_clbk CUserDBUpdate_CuttingPush138_user(nullptr);
        
        Info::CUserDBUpdate_CuttingTrans140_ptr CUserDBUpdate_CuttingTrans140_next(nullptr);
        Info::CUserDBUpdate_CuttingTrans140_clbk CUserDBUpdate_CuttingTrans140_user(nullptr);
        
        Info::CUserDBUpdate_DelBuddy142_ptr CUserDBUpdate_DelBuddy142_next(nullptr);
        Info::CUserDBUpdate_DelBuddy142_clbk CUserDBUpdate_DelBuddy142_user(nullptr);
        
        Info::CUserDBUpdate_DelPost144_ptr CUserDBUpdate_DelPost144_next(nullptr);
        Info::CUserDBUpdate_DelPost144_clbk CUserDBUpdate_DelPost144_user(nullptr);
        
        Info::CUserDBUpdate_Exp146_ptr CUserDBUpdate_Exp146_next(nullptr);
        Info::CUserDBUpdate_Exp146_clbk CUserDBUpdate_Exp146_user(nullptr);
        
        Info::CUserDBUpdate_ExtTrunkSlotNum148_ptr CUserDBUpdate_ExtTrunkSlotNum148_next(nullptr);
        Info::CUserDBUpdate_ExtTrunkSlotNum148_clbk CUserDBUpdate_ExtTrunkSlotNum148_user(nullptr);
        
        Info::CUserDBUpdate_ItemAdd150_ptr CUserDBUpdate_ItemAdd150_next(nullptr);
        Info::CUserDBUpdate_ItemAdd150_clbk CUserDBUpdate_ItemAdd150_user(nullptr);
        
        Info::CUserDBUpdate_ItemDelete152_ptr CUserDBUpdate_ItemDelete152_next(nullptr);
        Info::CUserDBUpdate_ItemDelete152_clbk CUserDBUpdate_ItemDelete152_user(nullptr);
        
        Info::CUserDBUpdate_ItemDur154_ptr CUserDBUpdate_ItemDur154_next(nullptr);
        Info::CUserDBUpdate_ItemDur154_clbk CUserDBUpdate_ItemDur154_user(nullptr);
        
        Info::CUserDBUpdate_ItemSlot156_ptr CUserDBUpdate_ItemSlot156_next(nullptr);
        Info::CUserDBUpdate_ItemSlot156_clbk CUserDBUpdate_ItemSlot156_user(nullptr);
        
        Info::CUserDBUpdate_ItemUpgrade158_ptr CUserDBUpdate_ItemUpgrade158_next(nullptr);
        Info::CUserDBUpdate_ItemUpgrade158_clbk CUserDBUpdate_ItemUpgrade158_user(nullptr);
        
        Info::CUserDBUpdate_LastAttBuff160_ptr CUserDBUpdate_LastAttBuff160_next(nullptr);
        Info::CUserDBUpdate_LastAttBuff160_clbk CUserDBUpdate_LastAttBuff160_user(nullptr);
        
        Info::CUserDBUpdate_Level162_ptr CUserDBUpdate_Level162_next(nullptr);
        Info::CUserDBUpdate_Level162_clbk CUserDBUpdate_Level162_user(nullptr);
        
        Info::CUserDBUpdate_LinkBoardLock164_ptr CUserDBUpdate_LinkBoardLock164_next(nullptr);
        Info::CUserDBUpdate_LinkBoardLock164_clbk CUserDBUpdate_LinkBoardLock164_user(nullptr);
        
        Info::CUserDBUpdate_LinkBoardSlot166_ptr CUserDBUpdate_LinkBoardSlot166_next(nullptr);
        Info::CUserDBUpdate_LinkBoardSlot166_clbk CUserDBUpdate_LinkBoardSlot166_user(nullptr);
        
        Info::CUserDBUpdate_LossExp168_ptr CUserDBUpdate_LossExp168_next(nullptr);
        Info::CUserDBUpdate_LossExp168_clbk CUserDBUpdate_LossExp168_user(nullptr);
        
        Info::CUserDBUpdate_Macro170_ptr CUserDBUpdate_Macro170_next(nullptr);
        Info::CUserDBUpdate_Macro170_clbk CUserDBUpdate_Macro170_user(nullptr);
        
        Info::CUserDBUpdate_Map172_ptr CUserDBUpdate_Map172_next(nullptr);
        Info::CUserDBUpdate_Map172_clbk CUserDBUpdate_Map172_user(nullptr);
        
        Info::CUserDBUpdate_MaxLevel174_ptr CUserDBUpdate_MaxLevel174_next(nullptr);
        Info::CUserDBUpdate_MaxLevel174_clbk CUserDBUpdate_MaxLevel174_user(nullptr);
        
        Info::CUserDBUpdate_Money176_ptr CUserDBUpdate_Money176_next(nullptr);
        Info::CUserDBUpdate_Money176_clbk CUserDBUpdate_Money176_user(nullptr);
        
        Info::CUserDBUpdate_NPCQuestHistory178_ptr CUserDBUpdate_NPCQuestHistory178_next(nullptr);
        Info::CUserDBUpdate_NPCQuestHistory178_clbk CUserDBUpdate_NPCQuestHistory178_user(nullptr);
        
        Info::CUserDBUpdate_Param180_ptr CUserDBUpdate_Param180_next(nullptr);
        Info::CUserDBUpdate_Param180_clbk CUserDBUpdate_Param180_user(nullptr);
        
        Info::CUserDBUpdate_PlayTime182_ptr CUserDBUpdate_PlayTime182_next(nullptr);
        Info::CUserDBUpdate_PlayTime182_clbk CUserDBUpdate_PlayTime182_user(nullptr);
        
        Info::CUserDBUpdate_Post184_ptr CUserDBUpdate_Post184_next(nullptr);
        Info::CUserDBUpdate_Post184_clbk CUserDBUpdate_Post184_user(nullptr);
        
        Info::CUserDBUpdate_PotionNextUseTime186_ptr CUserDBUpdate_PotionNextUseTime186_next(nullptr);
        Info::CUserDBUpdate_PotionNextUseTime186_clbk CUserDBUpdate_PotionNextUseTime186_user(nullptr);
        
        Info::CUserDBUpdate_PvpPointLeak188_ptr CUserDBUpdate_PvpPointLeak188_next(nullptr);
        Info::CUserDBUpdate_PvpPointLeak188_clbk CUserDBUpdate_PvpPointLeak188_user(nullptr);
        
        Info::CUserDBUpdate_QuestDelete190_ptr CUserDBUpdate_QuestDelete190_next(nullptr);
        Info::CUserDBUpdate_QuestDelete190_clbk CUserDBUpdate_QuestDelete190_user(nullptr);
        
        Info::CUserDBUpdate_QuestInsert192_ptr CUserDBUpdate_QuestInsert192_next(nullptr);
        Info::CUserDBUpdate_QuestInsert192_clbk CUserDBUpdate_QuestInsert192_user(nullptr);
        
        Info::CUserDBUpdate_QuestUpdate194_ptr CUserDBUpdate_QuestUpdate194_next(nullptr);
        Info::CUserDBUpdate_QuestUpdate194_clbk CUserDBUpdate_QuestUpdate194_user(nullptr);
        
        Info::CUserDBUpdate_RaceVoteInfoInit196_ptr CUserDBUpdate_RaceVoteInfoInit196_next(nullptr);
        Info::CUserDBUpdate_RaceVoteInfoInit196_clbk CUserDBUpdate_RaceVoteInfoInit196_user(nullptr);
        
        Info::CUserDBUpdate_ReturnPost198_ptr CUserDBUpdate_ReturnPost198_next(nullptr);
        Info::CUserDBUpdate_ReturnPost198_clbk CUserDBUpdate_ReturnPost198_user(nullptr);
        
        Info::CUserDBUpdate_SFContDelete200_ptr CUserDBUpdate_SFContDelete200_next(nullptr);
        Info::CUserDBUpdate_SFContDelete200_clbk CUserDBUpdate_SFContDelete200_user(nullptr);
        
        Info::CUserDBUpdate_SFContInsert202_ptr CUserDBUpdate_SFContInsert202_next(nullptr);
        Info::CUserDBUpdate_SFContInsert202_clbk CUserDBUpdate_SFContInsert202_user(nullptr);
        
        Info::CUserDBUpdate_SFContUpdate204_ptr CUserDBUpdate_SFContUpdate204_next(nullptr);
        Info::CUserDBUpdate_SFContUpdate204_clbk CUserDBUpdate_SFContUpdate204_user(nullptr);
        
        Info::CUserDBUpdate_StartNPCQuestHistory206_ptr CUserDBUpdate_StartNPCQuestHistory206_next(nullptr);
        Info::CUserDBUpdate_StartNPCQuestHistory206_clbk CUserDBUpdate_StartNPCQuestHistory206_user(nullptr);
        
        Info::CUserDBUpdate_Stat208_ptr CUserDBUpdate_Stat208_next(nullptr);
        Info::CUserDBUpdate_Stat208_clbk CUserDBUpdate_Stat208_user(nullptr);
        
        Info::CUserDBUpdate_TakeLastCriTicket210_ptr CUserDBUpdate_TakeLastCriTicket210_next(nullptr);
        Info::CUserDBUpdate_TakeLastCriTicket210_clbk CUserDBUpdate_TakeLastCriTicket210_user(nullptr);
        
        Info::CUserDBUpdate_TakeLastMentalTicket212_ptr CUserDBUpdate_TakeLastMentalTicket212_next(nullptr);
        Info::CUserDBUpdate_TakeLastMentalTicket212_clbk CUserDBUpdate_TakeLastMentalTicket212_user(nullptr);
        
        Info::CUserDBUpdate_TrunkHint214_ptr CUserDBUpdate_TrunkHint214_next(nullptr);
        Info::CUserDBUpdate_TrunkHint214_clbk CUserDBUpdate_TrunkHint214_user(nullptr);
        
        Info::CUserDBUpdate_TrunkMoney216_ptr CUserDBUpdate_TrunkMoney216_next(nullptr);
        Info::CUserDBUpdate_TrunkMoney216_clbk CUserDBUpdate_TrunkMoney216_user(nullptr);
        
        Info::CUserDBUpdate_TrunkPassword218_ptr CUserDBUpdate_TrunkPassword218_next(nullptr);
        Info::CUserDBUpdate_TrunkPassword218_clbk CUserDBUpdate_TrunkPassword218_user(nullptr);
        
        Info::CUserDBUpdate_TrunkSlotNum220_ptr CUserDBUpdate_TrunkSlotNum220_next(nullptr);
        Info::CUserDBUpdate_TrunkSlotNum220_clbk CUserDBUpdate_TrunkSlotNum220_user(nullptr);
        
        Info::CUserDBUpdate_UnitData222_ptr CUserDBUpdate_UnitData222_next(nullptr);
        Info::CUserDBUpdate_UnitData222_clbk CUserDBUpdate_UnitData222_user(nullptr);
        
        Info::CUserDBUpdate_UnitDelete224_ptr CUserDBUpdate_UnitDelete224_next(nullptr);
        Info::CUserDBUpdate_UnitDelete224_clbk CUserDBUpdate_UnitDelete224_user(nullptr);
        
        Info::CUserDBUpdate_UnitInsert226_ptr CUserDBUpdate_UnitInsert226_next(nullptr);
        Info::CUserDBUpdate_UnitInsert226_clbk CUserDBUpdate_UnitInsert226_user(nullptr);
        
        Info::CUserDBUpdate_UserFatigue228_ptr CUserDBUpdate_UserFatigue228_next(nullptr);
        Info::CUserDBUpdate_UserFatigue228_clbk CUserDBUpdate_UserFatigue228_user(nullptr);
        
        Info::CUserDBUpdate_UserGetScaner230_ptr CUserDBUpdate_UserGetScaner230_next(nullptr);
        Info::CUserDBUpdate_UserGetScaner230_clbk CUserDBUpdate_UserGetScaner230_user(nullptr);
        
        Info::CUserDBUpdate_UserPlayTime232_ptr CUserDBUpdate_UserPlayTime232_next(nullptr);
        Info::CUserDBUpdate_UserPlayTime232_clbk CUserDBUpdate_UserPlayTime232_user(nullptr);
        
        Info::CUserDBUpdate_UserTLStatus234_ptr CUserDBUpdate_UserTLStatus234_next(nullptr);
        Info::CUserDBUpdate_UserTLStatus234_clbk CUserDBUpdate_UserTLStatus234_user(nullptr);
        
        Info::CUserDBUpdate_UserVoteData236_ptr CUserDBUpdate_UserVoteData236_next(nullptr);
        Info::CUserDBUpdate_UserVoteData236_clbk CUserDBUpdate_UserVoteData236_user(nullptr);
        
        Info::CUserDBUpdate_User_Action_Point238_ptr CUserDBUpdate_User_Action_Point238_next(nullptr);
        Info::CUserDBUpdate_User_Action_Point238_clbk CUserDBUpdate_User_Action_Point238_user(nullptr);
        
        Info::CUserDBUpdate_WindowInfo240_ptr CUserDBUpdate_WindowInfo240_next(nullptr);
        Info::CUserDBUpdate_WindowInfo240_clbk CUserDBUpdate_WindowInfo240_user(nullptr);
        
        Info::CUserDBWriteLog_ChangeClassAfterInitClass242_ptr CUserDBWriteLog_ChangeClassAfterInitClass242_next(nullptr);
        Info::CUserDBWriteLog_ChangeClassAfterInitClass242_clbk CUserDBWriteLog_ChangeClassAfterInitClass242_user(nullptr);
        
        Info::CUserDBWriteLog_CharSelect244_ptr CUserDBWriteLog_CharSelect244_next(nullptr);
        Info::CUserDBWriteLog_CharSelect244_clbk CUserDBWriteLog_CharSelect244_user(nullptr);
        
        Info::CUserDBWriteLog_Level246_ptr CUserDBWriteLog_Level246_next(nullptr);
        Info::CUserDBWriteLog_Level246_clbk CUserDBWriteLog_Level246_user(nullptr);
        
        
        Info::CUserDBdtor_CUserDB252_ptr CUserDBdtor_CUserDB252_next(nullptr);
        Info::CUserDBdtor_CUserDB252_clbk CUserDBdtor_CUserDB252_user(nullptr);
        
        void CUserDBAlive_Char_Complete2_wrapper(struct CUserDB* _this, char byRetCode, char byCase, unsigned int dwSerial, struct _REGED* pAliveAvator)
        {
           CUserDBAlive_Char_Complete2_user(_this, byRetCode, byCase, dwSerial, pAliveAvator, CUserDBAlive_Char_Complete2_next);
        };
        bool CUserDBAlive_Char_Request4_wrapper(struct CUserDB* _this, char byCase, unsigned int dwSerial, char* pwszName, char bySlotIndex)
        {
           return CUserDBAlive_Char_Request4_user(_this, byCase, dwSerial, pwszName, bySlotIndex, CUserDBAlive_Char_Request4_next);
        };
        
        void CUserDBctor_CUserDB6_wrapper(struct CUserDB* _this)
        {
           CUserDBctor_CUserDB6_user(_this, CUserDBctor_CUserDB6_next);
        };
        void CUserDBCalcRadarDelay8_wrapper(struct CUserDB* _this)
        {
           CUserDBCalcRadarDelay8_user(_this, CUserDBCalcRadarDelay8_next);
        };
        bool CUserDBCheckDQSLoadCharacterData10_wrapper(struct _AVATOR_DATA* pData)
        {
           return CUserDBCheckDQSLoadCharacterData10_user(pData, CUserDBCheckDQSLoadCharacterData10_next);
        };
        void CUserDBClearBillingData12_wrapper(struct CUserDB* _this)
        {
           CUserDBClearBillingData12_user(_this, CUserDBClearBillingData12_next);
        };
        void CUserDBCont_UserSave_Complete14_wrapper(struct CUserDB* _this, char byResult, struct _AVATOR_DATA* pAvatorData)
        {
           CUserDBCont_UserSave_Complete14_user(_this, byResult, pAvatorData, CUserDBCont_UserSave_Complete14_next);
        };
        bool CUserDBDataValidCheckRevise16_wrapper(struct _AVATOR_DATA* pData, bool* pDataUpdated)
        {
           return CUserDBDataValidCheckRevise16_user(pData, pDataUpdated, CUserDBDataValidCheckRevise16_next);
        };
        void CUserDBDelPostData18_wrapper(struct CUserDB* _this, unsigned int dwIndex)
        {
           CUserDBDelPostData18_user(_this, dwIndex, CUserDBDelPostData18_next);
        };
        void CUserDBDelete_Char_Complete20_wrapper(struct CUserDB* _this, char byRetCode, char bySlotIndex)
        {
           CUserDBDelete_Char_Complete20_user(_this, byRetCode, bySlotIndex, CUserDBDelete_Char_Complete20_next);
        };
        bool CUserDBDelete_Char_Request22_wrapper(struct CUserDB* _this, char bySlotIndex)
        {
           return CUserDBDelete_Char_Request22_user(_this, bySlotIndex, CUserDBDelete_Char_Request22_next);
        };
        void CUserDBDummyCreate24_wrapper(struct CUserDB* _this, unsigned int dwSerial)
        {
           CUserDBDummyCreate24_user(_this, dwSerial, CUserDBDummyCreate24_next);
        };
        bool CUserDBEnter_Account26_wrapper(struct CUserDB* _this, unsigned int dwAccountSerial, unsigned int dwIP, unsigned int dwProtocolVer, unsigned int* pdwMasterKey)
        {
           return CUserDBEnter_Account26_user(_this, dwAccountSerial, dwIP, dwProtocolVer, pdwMasterKey, CUserDBEnter_Account26_next);
        };
        void CUserDBExit_Account_Complete28_wrapper(struct CUserDB* _this, char byRetCode)
        {
           CUserDBExit_Account_Complete28_user(_this, byRetCode, CUserDBExit_Account_Complete28_next);
        };
        void CUserDBExit_Account_Request30_wrapper(struct CUserDB* _this)
        {
           CUserDBExit_Account_Request30_user(_this, CUserDBExit_Account_Request30_next);
        };
        bool CUserDBFirstSettingData32_wrapper(struct CUserDB* _this)
        {
           return CUserDBFirstSettingData32_user(_this, CUserDBFirstSettingData32_next);
        };
        void CUserDBForceCloseCommand34_wrapper(struct CUserDB* _this, char byKickType, unsigned int dwPushIP, bool bSlow, char* pszCause)
        {
           CUserDBForceCloseCommand34_user(_this, byKickType, dwPushIP, bSlow, pszCause, CUserDBForceCloseCommand34_next);
        };
        unsigned int CUserDBGetActPoint36_wrapper(struct CUserDB* _this, char byCode)
        {
           return CUserDBGetActPoint36_user(_this, byCode, CUserDBGetActPoint36_next);
        };
        int CUserDBGetBillingType38_wrapper(struct CUserDB* _this)
        {
           return CUserDBGetBillingType38_user(_this, CUserDBGetBillingType38_next);
        };
        unsigned int* CUserDBGetPtrActPoint40_wrapper(struct CUserDB* _this)
        {
           return CUserDBGetPtrActPoint40_user(_this, CUserDBGetPtrActPoint40_next);
        };
        void CUserDBInform_For_Exit_By_FireguardBlock42_wrapper(struct CUserDB* _this)
        {
           CUserDBInform_For_Exit_By_FireguardBlock42_user(_this, CUserDBInform_For_Exit_By_FireguardBlock42_next);
        };
        void CUserDBInit44_wrapper(struct CUserDB* _this, unsigned int dwIndex)
        {
           CUserDBInit44_user(_this, dwIndex, CUserDBInit44_next);
        };
        bool CUserDBInitClass46_wrapper(struct CUserDB* _this, char* pszClassCode)
        {
           return CUserDBInitClass46_user(_this, pszClassCode, CUserDBInitClass46_next);
        };
        void CUserDBInsert_Char_Complete48_wrapper(struct CUserDB* _this, char byRetCode, struct _REGED_AVATOR_DB* pInsertData)
        {
           CUserDBInsert_Char_Complete48_user(_this, byRetCode, pInsertData, CUserDBInsert_Char_Complete48_next);
        };
        bool CUserDBInsert_Char_Request50_wrapper(struct CUserDB* _this, char* pwszCharName, char bySlotIndex, char byRaceSexCode, char* pszClassCode, unsigned int dwBaseShape)
        {
           return CUserDBInsert_Char_Request50_user(_this, pwszCharName, bySlotIndex, byRaceSexCode, pszClassCode, dwBaseShape, CUserDBInsert_Char_Request50_next);
        };
        struct _AVATOR_DATA* CUserDBIsContPushBefore52_wrapper(struct CUserDB* _this)
        {
           return CUserDBIsContPushBefore52_user(_this, CUserDBIsContPushBefore52_next);
        };
        char CUserDBIsExistRequestMoveCharacterList54_wrapper(struct CUserDB* _this, unsigned int dwCharSerial)
        {
           return CUserDBIsExistRequestMoveCharacterList54_user(_this, dwCharSerial, CUserDBIsExistRequestMoveCharacterList54_next);
        };
        bool CUserDBIsReturnPostUpdate56_wrapper(struct CUserDB* _this)
        {
           return CUserDBIsReturnPostUpdate56_user(_this, CUserDBIsReturnPostUpdate56_next);
        };
        void CUserDBLobby_Char_Complete58_wrapper(struct CUserDB* _this, char byRetCode)
        {
           CUserDBLobby_Char_Complete58_user(_this, byRetCode, CUserDBLobby_Char_Complete58_next);
        };
        bool CUserDBLobby_Char_Request60_wrapper(struct CUserDB* _this)
        {
           return CUserDBLobby_Char_Request60_user(_this, CUserDBLobby_Char_Request60_next);
        };
        void CUserDBOnLoop_Static62_wrapper()
        {
           CUserDBOnLoop_Static62_user(CUserDBOnLoop_Static62_next);
        };
        void CUserDBParamInit64_wrapper(struct CUserDB* _this)
        {
           CUserDBParamInit64_user(_this, CUserDBParamInit64_next);
        };
        void CUserDBReRangeClientIndex66_wrapper(struct _AVATOR_DATA* pData)
        {
           CUserDBReRangeClientIndex66_user(pData, CUserDBReRangeClientIndex66_next);
        };
        void CUserDBReged_Char_Complete68_wrapper(struct CUserDB* _this, char byRetCode, struct _REGED* pRegedList, struct _NOT_ARRANGED_AVATOR_DB* pArrangedList)
        {
           CUserDBReged_Char_Complete68_user(_this, byRetCode, pRegedList, pArrangedList, CUserDBReged_Char_Complete68_next);
        };
        bool CUserDBReged_Char_Request70_wrapper(struct CUserDB* _this)
        {
           return CUserDBReged_Char_Request70_user(_this, CUserDBReged_Char_Request70_next);
        };
        void CUserDBSelect_Char_Complete72_wrapper(struct CUserDB* _this, char byRetCode, struct _AVATOR_DATA* pLoadData, bool* pbAddItem, unsigned int dwAddDalant, unsigned int dwAddGold, unsigned int dwCheckSum, bool* pbTrunkAddItem, char byTrunkOldSlot, long double dTrunkOldDalant, long double dTrunkOldGold, bool bCreateTrunkFree, bool* pbExtTrunkAddItem, char byExtTrunkOldSlot)
        {
           CUserDBSelect_Char_Complete72_user(_this, byRetCode, pLoadData, pbAddItem, dwAddDalant, dwAddGold, dwCheckSum, pbTrunkAddItem, byTrunkOldSlot, dTrunkOldDalant, dTrunkOldGold, bCreateTrunkFree, pbExtTrunkAddItem, byExtTrunkOldSlot, CUserDBSelect_Char_Complete72_next);
        };
        bool CUserDBSelect_Char_Request74_wrapper(struct CUserDB* _this, char bySlotIndex)
        {
           return CUserDBSelect_Char_Request74_user(_this, bySlotIndex, CUserDBSelect_Char_Request74_next);
        };
        void CUserDBSendMsgAccount_UILockRefresh_Update76_wrapper(struct CUserDB* _this)
        {
           CUserDBSendMsgAccount_UILockRefresh_Update76_user(_this, CUserDBSendMsgAccount_UILockRefresh_Update76_next);
        };
        void CUserDBSendMsg_BillingInfo78_wrapper(struct CUserDB* _this)
        {
           CUserDBSendMsg_BillingInfo78_user(_this, CUserDBSendMsg_BillingInfo78_next);
        };
        void CUserDBSendMsg_Inform_UILock80_wrapper(struct CUserDB* _this)
        {
           CUserDBSendMsg_Inform_UILock80_user(_this, CUserDBSendMsg_Inform_UILock80_next);
        };
        void CUserDBSetActPoint82_wrapper(struct CUserDB* _this, char byCode, unsigned int dwLeftPoint)
        {
           CUserDBSetActPoint82_user(_this, byCode, dwLeftPoint, CUserDBSetActPoint82_next);
        };
        void CUserDBSetBillingData84_wrapper(struct CUserDB* _this, struct _BILLING_INFO* pBillingInfo)
        {
           CUserDBSetBillingData84_user(_this, pBillingInfo, CUserDBSetBillingData84_next);
        };
        void CUserDBSetBillingData86_wrapper(struct CUserDB* _this, char* szCMSCode, int16_t iType, int lRemainTime, struct _SYSTEMTIME* pstEndDate)
        {
           CUserDBSetBillingData86_user(_this, szCMSCode, iType, lRemainTime, pstEndDate, CUserDBSetBillingData86_next);
        };
        void CUserDBSetBillingNoLogout88_wrapper(struct CUserDB* _this, bool bNoLogout)
        {
           CUserDBSetBillingNoLogout88_user(_this, bNoLogout, CUserDBSetBillingNoLogout88_next);
        };
        void CUserDBSetChatLock90_wrapper(struct CUserDB* _this, bool bLock)
        {
           CUserDBSetChatLock90_user(_this, bLock, CUserDBSetChatLock90_next);
        };
        void CUserDBSetDBPostData92_wrapper(struct CUserDB* _this, int n, unsigned int dwSerial, int nNumber, char byState, int nKey, uint64_t dwDur, unsigned int dwUpt, unsigned int dwGold, bool bUpdateIndex, uint64_t lnUID)
        {
           CUserDBSetDBPostData92_user(_this, n, dwSerial, nNumber, byState, nKey, dwDur, dwUpt, dwGold, bUpdateIndex, lnUID, CUserDBSetDBPostData92_next);
        };
        void CUserDBSetNewDBPostData94_wrapper(struct CUserDB* _this, int n, unsigned int dwSerial, int nNumber, char byState, char* wszSendName, char* wszRecvName, char* wszTitle, char* wszContent, int nKey, uint64_t dwDur, unsigned int dwUpt, unsigned int dwGold, uint64_t lnUID)
        {
           CUserDBSetNewDBPostData94_user(_this, n, dwSerial, nNumber, byState, wszSendName, wszRecvName, wszTitle, wszContent, nKey, dwDur, dwUpt, dwGold, lnUID, CUserDBSetNewDBPostData94_next);
        };
        void CUserDBSetRadarDelay96_wrapper(struct CUserDB* _this, unsigned int dwDelay)
        {
           CUserDBSetRadarDelay96_user(_this, dwDelay, CUserDBSetRadarDelay96_next);
        };
        void CUserDBSetRemainTime98_wrapper(struct CUserDB* _this, int lRemainTime)
        {
           CUserDBSetRemainTime98_user(_this, lRemainTime, CUserDBSetRemainTime98_next);
        };
        void CUserDBSetWorldCLID100_wrapper(struct CUserDB* _this, unsigned int dwSerial, unsigned int* pipAddr)
        {
           CUserDBSetWorldCLID100_user(_this, dwSerial, pipAddr, CUserDBSetWorldCLID100_next);
        };
        bool CUserDBSetting_Class102_wrapper(struct CUserDB* _this, char* pszClassCode)
        {
           return CUserDBSetting_Class102_user(_this, pszClassCode, CUserDBSetting_Class102_next);
        };
        void CUserDBStartFieldMode104_wrapper(struct CUserDB* _this)
        {
           CUserDBStartFieldMode104_user(_this, CUserDBStartFieldMode104_next);
        };
        void CUserDBTotalPlayMinCheck106_wrapper(struct CUserDB* _this)
        {
           CUserDBTotalPlayMinCheck106_user(_this, CUserDBTotalPlayMinCheck106_next);
        };
        void CUserDBUILockInfo_Init108_wrapper(struct CUserDB* _this, char* pMsg)
        {
           CUserDBUILockInfo_Init108_user(_this, pMsg, CUserDBUILockInfo_Init108_next);
        };
        void CUserDBUILockInfo_Update110_wrapper(struct CUserDB* _this, char* pMsg)
        {
           CUserDBUILockInfo_Update110_user(_this, pMsg, CUserDBUILockInfo_Update110_next);
        };
        bool CUserDBUpdateContUserSave112_wrapper(struct CUserDB* _this, bool bDirect)
        {
           return CUserDBUpdateContUserSave112_user(_this, bDirect, CUserDBUpdateContUserSave112_next);
        };
        bool CUserDBUpdate_AddBuddy114_wrapper(struct CUserDB* _this, char bySlotIndex, unsigned int dwAdderSerial, char* pwszAdderName)
        {
           return CUserDBUpdate_AddBuddy114_user(_this, bySlotIndex, dwAdderSerial, pwszAdderName, CUserDBUpdate_AddBuddy114_next);
        };
        bool CUserDBUpdate_AlterPvPCashBag116_wrapper(struct CUserDB* _this, long double dNewPoint)
        {
           return CUserDBUpdate_AlterPvPCashBag116_user(_this, dNewPoint, CUserDBUpdate_AlterPvPCashBag116_next);
        };
        bool CUserDBUpdate_AlterPvPPoint118_wrapper(struct CUserDB* _this, long double dNewPoint)
        {
           return CUserDBUpdate_AlterPvPPoint118_user(_this, dNewPoint, CUserDBUpdate_AlterPvPPoint118_next);
        };
        bool CUserDBUpdate_AutoTradeAllClear120_wrapper(struct CUserDB* _this)
        {
           return CUserDBUpdate_AutoTradeAllClear120_user(_this, CUserDBUpdate_AutoTradeAllClear120_next);
        };
        bool CUserDBUpdate_BagNum122_wrapper(struct CUserDB* _this, char bagnum)
        {
           return CUserDBUpdate_BagNum122_user(_this, bagnum, CUserDBUpdate_BagNum122_next);
        };
        bool CUserDBUpdate_Bind124_wrapper(struct CUserDB* _this, char* pszMapCode, char* pDummyCode, bool bUpdate)
        {
           return CUserDBUpdate_Bind124_user(_this, pszMapCode, pDummyCode, bUpdate, CUserDBUpdate_Bind124_next);
        };
        void CUserDBUpdate_BossCryMsg126_wrapper(struct CUserDB* _this, char bySlot, char* pwszCryMsg)
        {
           CUserDBUpdate_BossCryMsg126_user(_this, bySlot, pwszCryMsg, CUserDBUpdate_BossCryMsg126_next);
        };
        bool CUserDBUpdate_Class128_wrapper(struct CUserDB* _this, char* pszClassCode, char byHistoryRecordNum, uint16_t wHistoryClassIndex)
        {
           return CUserDBUpdate_Class128_user(_this, pszClassCode, byHistoryRecordNum, wHistoryClassIndex, CUserDBUpdate_Class128_next);
        };
        bool CUserDBUpdate_CombineExResult_Pop130_wrapper(struct CUserDB* _this)
        {
           return CUserDBUpdate_CombineExResult_Pop130_user(_this, CUserDBUpdate_CombineExResult_Pop130_next);
        };
        bool CUserDBUpdate_CombineExResult_Push132_wrapper(struct CUserDB* _this, struct _ITEMCOMBINE_DB_BASE* pItemCombineDB_IN)
        {
           return CUserDBUpdate_CombineExResult_Push132_user(_this, pItemCombineDB_IN, CUserDBUpdate_CombineExResult_Push132_next);
        };
        bool CUserDBUpdate_CopyAll134_wrapper(struct CUserDB* _this, struct _AVATOR_DATA* pSrc)
        {
           return CUserDBUpdate_CopyAll134_user(_this, pSrc, CUserDBUpdate_CopyAll134_next);
        };
        bool CUserDBUpdate_CuttingEmpty136_wrapper(struct CUserDB* _this)
        {
           return CUserDBUpdate_CuttingEmpty136_user(_this, CUserDBUpdate_CuttingEmpty136_next);
        };
        bool CUserDBUpdate_CuttingPush138_wrapper(struct CUserDB* _this, char resnum, struct _CUTTING_DB_BASE::_LIST* plist)
        {
           return CUserDBUpdate_CuttingPush138_user(_this, resnum, plist, CUserDBUpdate_CuttingPush138_next);
        };
        bool CUserDBUpdate_CuttingTrans140_wrapper(struct CUserDB* _this, uint16_t wResItemIndex, uint16_t wLeftAmt)
        {
           return CUserDBUpdate_CuttingTrans140_user(_this, wResItemIndex, wLeftAmt, CUserDBUpdate_CuttingTrans140_next);
        };
        bool CUserDBUpdate_DelBuddy142_wrapper(struct CUserDB* _this, char bySlotIndex)
        {
           return CUserDBUpdate_DelBuddy142_user(_this, bySlotIndex, CUserDBUpdate_DelBuddy142_next);
        };
        bool CUserDBUpdate_DelPost144_wrapper(struct CUserDB* _this, unsigned int dwSerial, int nIndex)
        {
           return CUserDBUpdate_DelPost144_user(_this, dwSerial, nIndex, CUserDBUpdate_DelPost144_next);
        };
        bool CUserDBUpdate_Exp146_wrapper(struct CUserDB* _this, long double exp)
        {
           return CUserDBUpdate_Exp146_user(_this, exp, CUserDBUpdate_Exp146_next);
        };
        bool CUserDBUpdate_ExtTrunkSlotNum148_wrapper(struct CUserDB* _this, char byExtSlotNum)
        {
           return CUserDBUpdate_ExtTrunkSlotNum148_user(_this, byExtSlotNum, CUserDBUpdate_ExtTrunkSlotNum148_next);
        };
        bool CUserDBUpdate_ItemAdd150_wrapper(struct CUserDB* _this, char storage, char slot, struct _STORAGE_LIST::_db_con* pItem, bool bUpdate)
        {
           return CUserDBUpdate_ItemAdd150_user(_this, storage, slot, pItem, bUpdate, CUserDBUpdate_ItemAdd150_next);
        };
        bool CUserDBUpdate_ItemDelete152_wrapper(struct CUserDB* _this, char storage, char slot, bool bUpdate)
        {
           return CUserDBUpdate_ItemDelete152_user(_this, storage, slot, bUpdate, CUserDBUpdate_ItemDelete152_next);
        };
        bool CUserDBUpdate_ItemDur154_wrapper(struct CUserDB* _this, char storage, char slot, uint64_t amount, bool bUpdate)
        {
           return CUserDBUpdate_ItemDur154_user(_this, storage, slot, amount, bUpdate, CUserDBUpdate_ItemDur154_next);
        };
        bool CUserDBUpdate_ItemSlot156_wrapper(struct CUserDB* _this, char storage, char slot, char clientpos)
        {
           return CUserDBUpdate_ItemSlot156_user(_this, storage, slot, clientpos, CUserDBUpdate_ItemSlot156_next);
        };
        bool CUserDBUpdate_ItemUpgrade158_wrapper(struct CUserDB* _this, char storage, char slot, unsigned int upg, bool bUpdate)
        {
           return CUserDBUpdate_ItemUpgrade158_user(_this, storage, slot, upg, bUpdate, CUserDBUpdate_ItemUpgrade158_next);
        };
        void CUserDBUpdate_LastAttBuff160_wrapper(struct CUserDB* _this, bool bSet)
        {
           CUserDBUpdate_LastAttBuff160_user(_this, bSet, CUserDBUpdate_LastAttBuff160_next);
        };
        bool CUserDBUpdate_Level162_wrapper(struct CUserDB* _this, char lv, long double exp)
        {
           return CUserDBUpdate_Level162_user(_this, lv, exp, CUserDBUpdate_Level162_next);
        };
        bool CUserDBUpdate_LinkBoardLock164_wrapper(struct CUserDB* _this, char byLBLock)
        {
           return CUserDBUpdate_LinkBoardLock164_user(_this, byLBLock, CUserDBUpdate_LinkBoardLock164_next);
        };
        bool CUserDBUpdate_LinkBoardSlot166_wrapper(struct CUserDB* _this, char bySlot, char byLinkCode, uint16_t wIndex)
        {
           return CUserDBUpdate_LinkBoardSlot166_user(_this, bySlot, byLinkCode, wIndex, CUserDBUpdate_LinkBoardSlot166_next);
        };
        bool CUserDBUpdate_LossExp168_wrapper(struct CUserDB* _this, long double dLossExp)
        {
           return CUserDBUpdate_LossExp168_user(_this, dLossExp, CUserDBUpdate_LossExp168_next);
        };
        bool CUserDBUpdate_Macro170_wrapper(struct CUserDB* _this, char* pBuf)
        {
           return CUserDBUpdate_Macro170_user(_this, pBuf, CUserDBUpdate_Macro170_next);
        };
        bool CUserDBUpdate_Map172_wrapper(struct CUserDB* _this, char map, float* pos)
        {
           return CUserDBUpdate_Map172_user(_this, map, pos, CUserDBUpdate_Map172_next);
        };
        void CUserDBUpdate_MaxLevel174_wrapper(struct CUserDB* _this, char byMaxLevel)
        {
           CUserDBUpdate_MaxLevel174_user(_this, byMaxLevel, CUserDBUpdate_MaxLevel174_next);
        };
        bool CUserDBUpdate_Money176_wrapper(struct CUserDB* _this, unsigned int dalant, unsigned int gold)
        {
           return CUserDBUpdate_Money176_user(_this, dalant, gold, CUserDBUpdate_Money176_next);
        };
        bool CUserDBUpdate_NPCQuestHistory178_wrapper(struct CUserDB* _this, char byIndex, struct _QUEST_DB_BASE::_NPC_QUEST_HISTORY* pHisData)
        {
           return CUserDBUpdate_NPCQuestHistory178_user(_this, byIndex, pHisData, CUserDBUpdate_NPCQuestHistory178_next);
        };
        bool CUserDBUpdate_Param180_wrapper(struct CUserDB* _this, struct _EXIT_ALTER_PARAM* pCon)
        {
           return CUserDBUpdate_Param180_user(_this, pCon, CUserDBUpdate_Param180_next);
        };
        bool CUserDBUpdate_PlayTime182_wrapper(struct CUserDB* _this, unsigned int dwTotalTimeMin)
        {
           return CUserDBUpdate_PlayTime182_user(_this, dwTotalTimeMin, CUserDBUpdate_PlayTime182_next);
        };
        void CUserDBUpdate_Post184_wrapper(struct CUserDB* _this, int n, unsigned int dwSerial, int nNumber, char byState, int nKey, uint64_t dwDur, unsigned int dwUpt, unsigned int dwGold, uint64_t lnUID)
        {
           CUserDBUpdate_Post184_user(_this, n, dwSerial, nNumber, byState, nKey, dwDur, dwUpt, dwGold, lnUID, CUserDBUpdate_Post184_next);
        };
        void CUserDBUpdate_PotionNextUseTime186_wrapper(struct CUserDB* _this, char byPotionClass, unsigned int dwNextUseTime)
        {
           CUserDBUpdate_PotionNextUseTime186_user(_this, byPotionClass, dwNextUseTime, CUserDBUpdate_PotionNextUseTime186_next);
        };
        void CUserDBUpdate_PvpPointLeak188_wrapper(struct CUserDB* _this, long double dValue)
        {
           CUserDBUpdate_PvpPointLeak188_user(_this, dValue, CUserDBUpdate_PvpPointLeak188_next);
        };
        bool CUserDBUpdate_QuestDelete190_wrapper(struct CUserDB* _this, char bySlotIndex)
        {
           return CUserDBUpdate_QuestDelete190_user(_this, bySlotIndex, CUserDBUpdate_QuestDelete190_next);
        };
        bool CUserDBUpdate_QuestInsert192_wrapper(struct CUserDB* _this, char bySlotIndex, struct _QUEST_DB_BASE::_LIST* pSlotData)
        {
           return CUserDBUpdate_QuestInsert192_user(_this, bySlotIndex, pSlotData, CUserDBUpdate_QuestInsert192_next);
        };
        bool CUserDBUpdate_QuestUpdate194_wrapper(struct CUserDB* _this, char bySlotIndex, struct _QUEST_DB_BASE::_LIST* pSlotData, bool bUpdate)
        {
           return CUserDBUpdate_QuestUpdate194_user(_this, bySlotIndex, pSlotData, bUpdate, CUserDBUpdate_QuestUpdate194_next);
        };
        bool CUserDBUpdate_RaceVoteInfoInit196_wrapper(struct CUserDB* _this)
        {
           return CUserDBUpdate_RaceVoteInfoInit196_user(_this, CUserDBUpdate_RaceVoteInfoInit196_next);
        };
        void CUserDBUpdate_ReturnPost198_wrapper(struct CUserDB* _this, unsigned int dwSerial)
        {
           CUserDBUpdate_ReturnPost198_user(_this, dwSerial, CUserDBUpdate_ReturnPost198_next);
        };
        bool CUserDBUpdate_SFContDelete200_wrapper(struct CUserDB* _this, char byContCode, char bySlotIndex)
        {
           return CUserDBUpdate_SFContDelete200_user(_this, byContCode, bySlotIndex, CUserDBUpdate_SFContDelete200_next);
        };
        bool CUserDBUpdate_SFContInsert202_wrapper(struct CUserDB* _this, char byContCode, char bySlotIndex, char byEffectCode, uint16_t wEffectIndex, char byLv, uint16_t wDurSec)
        {
           return CUserDBUpdate_SFContInsert202_user(_this, byContCode, bySlotIndex, byEffectCode, wEffectIndex, byLv, wDurSec, CUserDBUpdate_SFContInsert202_next);
        };
        bool CUserDBUpdate_SFContUpdate204_wrapper(struct CUserDB* _this, char byContCode, char bySlotIndex, uint16_t wTime, bool bUpdate)
        {
           return CUserDBUpdate_SFContUpdate204_user(_this, byContCode, bySlotIndex, wTime, bUpdate, CUserDBUpdate_SFContUpdate204_next);
        };
        bool CUserDBUpdate_StartNPCQuestHistory206_wrapper(struct CUserDB* _this, char byIndex, struct _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY* pHisData)
        {
           return CUserDBUpdate_StartNPCQuestHistory206_user(_this, byIndex, pHisData, CUserDBUpdate_StartNPCQuestHistory206_next);
        };
        bool CUserDBUpdate_Stat208_wrapper(struct CUserDB* _this, char byStatIndex, unsigned int dwNewCum, bool bUpdate)
        {
           return CUserDBUpdate_Stat208_user(_this, byStatIndex, dwNewCum, bUpdate, CUserDBUpdate_Stat208_next);
        };
        bool CUserDBUpdate_TakeLastCriTicket210_wrapper(struct CUserDB* _this, unsigned int dwCriTicket)
        {
           return CUserDBUpdate_TakeLastCriTicket210_user(_this, dwCriTicket, CUserDBUpdate_TakeLastCriTicket210_next);
        };
        bool CUserDBUpdate_TakeLastMentalTicket212_wrapper(struct CUserDB* _this, unsigned int dwMentalTicket)
        {
           return CUserDBUpdate_TakeLastMentalTicket212_user(_this, dwMentalTicket, CUserDBUpdate_TakeLastMentalTicket212_next);
        };
        bool CUserDBUpdate_TrunkHint214_wrapper(struct CUserDB* _this, char byHintIndex, char* pwszHintAnswer)
        {
           return CUserDBUpdate_TrunkHint214_user(_this, byHintIndex, pwszHintAnswer, CUserDBUpdate_TrunkHint214_next);
        };
        bool CUserDBUpdate_TrunkMoney216_wrapper(struct CUserDB* _this, long double dGold, long double dDalant)
        {
           return CUserDBUpdate_TrunkMoney216_user(_this, dGold, dDalant, CUserDBUpdate_TrunkMoney216_next);
        };
        bool CUserDBUpdate_TrunkPassword218_wrapper(struct CUserDB* _this, char* pwszPassword)
        {
           return CUserDBUpdate_TrunkPassword218_user(_this, pwszPassword, CUserDBUpdate_TrunkPassword218_next);
        };
        bool CUserDBUpdate_TrunkSlotNum220_wrapper(struct CUserDB* _this, char bySlotNum)
        {
           return CUserDBUpdate_TrunkSlotNum220_user(_this, bySlotNum, CUserDBUpdate_TrunkSlotNum220_next);
        };
        bool CUserDBUpdate_UnitData222_wrapper(struct CUserDB* _this, char bySlotIndex, struct _UNIT_DB_BASE::_LIST* pData)
        {
           return CUserDBUpdate_UnitData222_user(_this, bySlotIndex, pData, CUserDBUpdate_UnitData222_next);
        };
        bool CUserDBUpdate_UnitDelete224_wrapper(struct CUserDB* _this, char bySlotIndex)
        {
           return CUserDBUpdate_UnitDelete224_user(_this, bySlotIndex, CUserDBUpdate_UnitDelete224_next);
        };
        bool CUserDBUpdate_UnitInsert226_wrapper(struct CUserDB* _this, char bySlotIndex, struct _UNIT_DB_BASE::_LIST* pSlotData)
        {
           return CUserDBUpdate_UnitInsert226_user(_this, bySlotIndex, pSlotData, CUserDBUpdate_UnitInsert226_next);
        };
        bool CUserDBUpdate_UserFatigue228_wrapper(struct CUserDB* _this, unsigned int dwFatigue)
        {
           return CUserDBUpdate_UserFatigue228_user(_this, dwFatigue, CUserDBUpdate_UserFatigue228_next);
        };
        bool CUserDBUpdate_UserGetScaner230_wrapper(struct CUserDB* _this, uint16_t wScanerCnt, uint16_t wBattleTime)
        {
           return CUserDBUpdate_UserGetScaner230_user(_this, wScanerCnt, wBattleTime, CUserDBUpdate_UserGetScaner230_next);
        };
        bool CUserDBUpdate_UserPlayTime232_wrapper(struct CUserDB* _this, unsigned int dwAccPlayTime)
        {
           return CUserDBUpdate_UserPlayTime232_user(_this, dwAccPlayTime, CUserDBUpdate_UserPlayTime232_next);
        };
        bool CUserDBUpdate_UserTLStatus234_wrapper(struct CUserDB* _this, char byStatus)
        {
           return CUserDBUpdate_UserTLStatus234_user(_this, byStatus, CUserDBUpdate_UserTLStatus234_next);
        };
        bool CUserDBUpdate_UserVoteData236_wrapper(struct CUserDB* _this)
        {
           return CUserDBUpdate_UserVoteData236_user(_this, CUserDBUpdate_UserVoteData236_next);
        };
        bool CUserDBUpdate_User_Action_Point238_wrapper(struct CUserDB* _this, char byActionCode, unsigned int dwPoint)
        {
           return CUserDBUpdate_User_Action_Point238_user(_this, byActionCode, dwPoint, CUserDBUpdate_User_Action_Point238_next);
        };
        bool CUserDBUpdate_WindowInfo240_wrapper(struct CUserDB* _this, unsigned int* pdwSkill, unsigned int* pdwForce, unsigned int* pdwChar, unsigned int* pdwAnimus, unsigned int dwInven, unsigned int* pdwInvenBag)
        {
           return CUserDBUpdate_WindowInfo240_user(_this, pdwSkill, pdwForce, pdwChar, pdwAnimus, dwInven, pdwInvenBag, CUserDBUpdate_WindowInfo240_next);
        };
        void CUserDBWriteLog_ChangeClassAfterInitClass242_wrapper(struct CUserDB* _this, char byType, char* szPrevClass)
        {
           CUserDBWriteLog_ChangeClassAfterInitClass242_user(_this, byType, szPrevClass, CUserDBWriteLog_ChangeClassAfterInitClass242_next);
        };
        void CUserDBWriteLog_CharSelect244_wrapper(struct CUserDB* _this)
        {
           CUserDBWriteLog_CharSelect244_user(_this, CUserDBWriteLog_CharSelect244_next);
        };
        void CUserDBWriteLog_Level246_wrapper(struct CUserDB* _this, char byLv)
        {
           CUserDBWriteLog_Level246_user(_this, byLv, CUserDBWriteLog_Level246_next);
        };
        
        void CUserDBdtor_CUserDB252_wrapper(struct CUserDB* _this)
        {
           CUserDBdtor_CUserDB252_user(_this, CUserDBdtor_CUserDB252_next);
        };
        
        ::std::array<hook_record, 124> CUserDB_functions = 
        {
            _hook_record {
                (LPVOID)0x14011afc0L,
                (LPVOID *)&CUserDBAlive_Char_Complete2_user,
                (LPVOID *)&CUserDBAlive_Char_Complete2_next,
                (LPVOID)cast_pointer_function(CUserDBAlive_Char_Complete2_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(char, char, unsigned int, struct _REGED*))&CUserDB::Alive_Char_Complete)
            },
            _hook_record {
                (LPVOID)0x14011abf0L,
                (LPVOID *)&CUserDBAlive_Char_Request4_user,
                (LPVOID *)&CUserDBAlive_Char_Request4_next,
                (LPVOID)cast_pointer_function(CUserDBAlive_Char_Request4_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, unsigned int, char*, char))&CUserDB::Alive_Char_Request)
            },
            _hook_record {
                (LPVOID)0x14010fb90L,
                (LPVOID *)&CUserDBctor_CUserDB6_user,
                (LPVOID *)&CUserDBctor_CUserDB6_next,
                (LPVOID)cast_pointer_function(CUserDBctor_CUserDB6_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)())&CUserDB::ctor_CUserDB)
            },
            _hook_record {
                (LPVOID)0x14011b730L,
                (LPVOID *)&CUserDBCalcRadarDelay8_user,
                (LPVOID *)&CUserDBCalcRadarDelay8_next,
                (LPVOID)cast_pointer_function(CUserDBCalcRadarDelay8_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)())&CUserDB::CalcRadarDelay)
            },
            _hook_record {
                (LPVOID)0x140118860L,
                (LPVOID *)&CUserDBCheckDQSLoadCharacterData10_user,
                (LPVOID *)&CUserDBCheckDQSLoadCharacterData10_next,
                (LPVOID)cast_pointer_function(CUserDBCheckDQSLoadCharacterData10_wrapper),
                (LPVOID)cast_pointer_function((bool(*)(struct _AVATOR_DATA*))&CUserDB::CheckDQSLoadCharacterData)
            },
            _hook_record {
                (LPVOID)0x140118090L,
                (LPVOID *)&CUserDBClearBillingData12_user,
                (LPVOID *)&CUserDBClearBillingData12_next,
                (LPVOID)cast_pointer_function(CUserDBClearBillingData12_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)())&CUserDB::ClearBillingData)
            },
            _hook_record {
                (LPVOID)0x1401141c0L,
                (LPVOID *)&CUserDBCont_UserSave_Complete14_user,
                (LPVOID *)&CUserDBCont_UserSave_Complete14_next,
                (LPVOID)cast_pointer_function(CUserDBCont_UserSave_Complete14_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(char, struct _AVATOR_DATA*))&CUserDB::Cont_UserSave_Complete)
            },
            _hook_record {
                (LPVOID)0x140118940L,
                (LPVOID *)&CUserDBDataValidCheckRevise16_user,
                (LPVOID *)&CUserDBDataValidCheckRevise16_next,
                (LPVOID)cast_pointer_function(CUserDBDataValidCheckRevise16_wrapper),
                (LPVOID)cast_pointer_function((bool(*)(struct _AVATOR_DATA*, bool*))&CUserDB::DataValidCheckRevise)
            },
            _hook_record {
                (LPVOID)0x140117be0L,
                (LPVOID *)&CUserDBDelPostData18_user,
                (LPVOID *)&CUserDBDelPostData18_next,
                (LPVOID)cast_pointer_function(CUserDBDelPostData18_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(unsigned int))&CUserDB::DelPostData)
            },
            _hook_record {
                (LPVOID)0x1401124d0L,
                (LPVOID *)&CUserDBDelete_Char_Complete20_user,
                (LPVOID *)&CUserDBDelete_Char_Complete20_next,
                (LPVOID)cast_pointer_function(CUserDBDelete_Char_Complete20_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(char, char))&CUserDB::Delete_Char_Complete)
            },
            _hook_record {
                (LPVOID)0x140112230L,
                (LPVOID *)&CUserDBDelete_Char_Request22_user,
                (LPVOID *)&CUserDBDelete_Char_Request22_next,
                (LPVOID)cast_pointer_function(CUserDBDelete_Char_Request22_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char))&CUserDB::Delete_Char_Request)
            },
            _hook_record {
                (LPVOID)0x140110550L,
                (LPVOID *)&CUserDBDummyCreate24_user,
                (LPVOID *)&CUserDBDummyCreate24_next,
                (LPVOID)cast_pointer_function(CUserDBDummyCreate24_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(unsigned int))&CUserDB::DummyCreate)
            },
            _hook_record {
                (LPVOID)0x1401105b0L,
                (LPVOID *)&CUserDBEnter_Account26_user,
                (LPVOID *)&CUserDBEnter_Account26_next,
                (LPVOID)cast_pointer_function(CUserDBEnter_Account26_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(unsigned int, unsigned int, unsigned int, unsigned int*))&CUserDB::Enter_Account)
            },
            _hook_record {
                (LPVOID)0x1401113e0L,
                (LPVOID *)&CUserDBExit_Account_Complete28_user,
                (LPVOID *)&CUserDBExit_Account_Complete28_next,
                (LPVOID)cast_pointer_function(CUserDBExit_Account_Complete28_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(char))&CUserDB::Exit_Account_Complete)
            },
            _hook_record {
                (LPVOID)0x140111020L,
                (LPVOID *)&CUserDBExit_Account_Request30_user,
                (LPVOID *)&CUserDBExit_Account_Request30_next,
                (LPVOID)cast_pointer_function(CUserDBExit_Account_Request30_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)())&CUserDB::Exit_Account_Request)
            },
            _hook_record {
                (LPVOID)0x14011a600L,
                (LPVOID *)&CUserDBFirstSettingData32_user,
                (LPVOID *)&CUserDBFirstSettingData32_next,
                (LPVOID)cast_pointer_function(CUserDBFirstSettingData32_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)())&CUserDB::FirstSettingData)
            },
            _hook_record {
                (LPVOID)0x140110350L,
                (LPVOID *)&CUserDBForceCloseCommand34_user,
                (LPVOID *)&CUserDBForceCloseCommand34_next,
                (LPVOID)cast_pointer_function(CUserDBForceCloseCommand34_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(char, unsigned int, bool, char*))&CUserDB::ForceCloseCommand)
            },
            _hook_record {
                (LPVOID)0x14007b850L,
                (LPVOID *)&CUserDBGetActPoint36_user,
                (LPVOID *)&CUserDBGetActPoint36_next,
                (LPVOID)cast_pointer_function(CUserDBGetActPoint36_wrapper),
                (LPVOID)cast_pointer_function((unsigned int(CUserDB::*)(char))&CUserDB::GetActPoint)
            },
            _hook_record {
                (LPVOID)0x14007da20L,
                (LPVOID *)&CUserDBGetBillingType38_user,
                (LPVOID *)&CUserDBGetBillingType38_next,
                (LPVOID)cast_pointer_function(CUserDBGetBillingType38_wrapper),
                (LPVOID)cast_pointer_function((int(CUserDB::*)())&CUserDB::GetBillingType)
            },
            _hook_record {
                (LPVOID)0x1400f7800L,
                (LPVOID *)&CUserDBGetPtrActPoint40_user,
                (LPVOID *)&CUserDBGetPtrActPoint40_next,
                (LPVOID)cast_pointer_function(CUserDBGetPtrActPoint40_wrapper),
                (LPVOID)cast_pointer_function((unsigned int*(CUserDB::*)())&CUserDB::GetPtrActPoint)
            },
            _hook_record {
                (LPVOID)0x1401114a0L,
                (LPVOID *)&CUserDBInform_For_Exit_By_FireguardBlock42_user,
                (LPVOID *)&CUserDBInform_For_Exit_By_FireguardBlock42_next,
                (LPVOID)cast_pointer_function(CUserDBInform_For_Exit_By_FireguardBlock42_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)())&CUserDB::Inform_For_Exit_By_FireguardBlock)
            },
            _hook_record {
                (LPVOID)0x140110030L,
                (LPVOID *)&CUserDBInit44_user,
                (LPVOID *)&CUserDBInit44_next,
                (LPVOID)cast_pointer_function(CUserDBInit44_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(unsigned int))&CUserDB::Init)
            },
            _hook_record {
                (LPVOID)0x1401160e0L,
                (LPVOID *)&CUserDBInitClass46_user,
                (LPVOID *)&CUserDBInitClass46_next,
                (LPVOID)cast_pointer_function(CUserDBInitClass46_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char*))&CUserDB::InitClass)
            },
            _hook_record {
                (LPVOID)0x1401120e0L,
                (LPVOID *)&CUserDBInsert_Char_Complete48_user,
                (LPVOID *)&CUserDBInsert_Char_Complete48_next,
                (LPVOID)cast_pointer_function(CUserDBInsert_Char_Complete48_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(char, struct _REGED_AVATOR_DB*))&CUserDB::Insert_Char_Complete)
            },
            _hook_record {
                (LPVOID)0x140111c90L,
                (LPVOID *)&CUserDBInsert_Char_Request50_user,
                (LPVOID *)&CUserDBInsert_Char_Request50_next,
                (LPVOID)cast_pointer_function(CUserDBInsert_Char_Request50_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char*, char, char, char*, unsigned int))&CUserDB::Insert_Char_Request)
            },
            _hook_record {
                (LPVOID)0x140110ec0L,
                (LPVOID *)&CUserDBIsContPushBefore52_user,
                (LPVOID *)&CUserDBIsContPushBefore52_next,
                (LPVOID)cast_pointer_function(CUserDBIsContPushBefore52_wrapper),
                (LPVOID)cast_pointer_function((struct _AVATOR_DATA*(CUserDB::*)())&CUserDB::IsContPushBefore)
            },
            _hook_record {
                (LPVOID)0x14011bae0L,
                (LPVOID *)&CUserDBIsExistRequestMoveCharacterList54_user,
                (LPVOID *)&CUserDBIsExistRequestMoveCharacterList54_next,
                (LPVOID)cast_pointer_function(CUserDBIsExistRequestMoveCharacterList54_wrapper),
                (LPVOID)cast_pointer_function((char(CUserDB::*)(unsigned int))&CUserDB::IsExistRequestMoveCharacterList)
            },
            _hook_record {
                (LPVOID)0x140117a50L,
                (LPVOID *)&CUserDBIsReturnPostUpdate56_user,
                (LPVOID *)&CUserDBIsReturnPostUpdate56_next,
                (LPVOID)cast_pointer_function(CUserDBIsReturnPostUpdate56_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)())&CUserDB::IsReturnPostUpdate)
            },
            _hook_record {
                (LPVOID)0x140113850L,
                (LPVOID *)&CUserDBLobby_Char_Complete58_user,
                (LPVOID *)&CUserDBLobby_Char_Complete58_next,
                (LPVOID)cast_pointer_function(CUserDBLobby_Char_Complete58_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(char))&CUserDB::Lobby_Char_Complete)
            },
            _hook_record {
                (LPVOID)0x140113500L,
                (LPVOID *)&CUserDBLobby_Char_Request60_user,
                (LPVOID *)&CUserDBLobby_Char_Request60_next,
                (LPVOID)cast_pointer_function(CUserDBLobby_Char_Request60_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)())&CUserDB::Lobby_Char_Request)
            },
            _hook_record {
                (LPVOID)0x14010fff0L,
                (LPVOID *)&CUserDBOnLoop_Static62_user,
                (LPVOID *)&CUserDBOnLoop_Static62_next,
                (LPVOID)cast_pointer_function(CUserDBOnLoop_Static62_wrapper),
                (LPVOID)cast_pointer_function((void(*)())&CUserDB::OnLoop_Static)
            },
            _hook_record {
                (LPVOID)0x1401100a0L,
                (LPVOID *)&CUserDBParamInit64_user,
                (LPVOID *)&CUserDBParamInit64_next,
                (LPVOID)cast_pointer_function(CUserDBParamInit64_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)())&CUserDB::ParamInit)
            },
            _hook_record {
                (LPVOID)0x140119ed0L,
                (LPVOID *)&CUserDBReRangeClientIndex66_user,
                (LPVOID *)&CUserDBReRangeClientIndex66_next,
                (LPVOID)cast_pointer_function(CUserDBReRangeClientIndex66_wrapper),
                (LPVOID)cast_pointer_function((void(*)(struct _AVATOR_DATA*))&CUserDB::ReRangeClientIndex)
            },
            _hook_record {
                (LPVOID)0x140111760L,
                (LPVOID *)&CUserDBReged_Char_Complete68_user,
                (LPVOID *)&CUserDBReged_Char_Complete68_next,
                (LPVOID)cast_pointer_function(CUserDBReged_Char_Complete68_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(char, struct _REGED*, struct _NOT_ARRANGED_AVATOR_DB*))&CUserDB::Reged_Char_Complete)
            },
            _hook_record {
                (LPVOID)0x140111580L,
                (LPVOID *)&CUserDBReged_Char_Request70_user,
                (LPVOID *)&CUserDBReged_Char_Request70_next,
                (LPVOID)cast_pointer_function(CUserDBReged_Char_Request70_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)())&CUserDB::Reged_Char_Request)
            },
            _hook_record {
                (LPVOID)0x1401129c0L,
                (LPVOID *)&CUserDBSelect_Char_Complete72_user,
                (LPVOID *)&CUserDBSelect_Char_Complete72_next,
                (LPVOID)cast_pointer_function(CUserDBSelect_Char_Complete72_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(char, struct _AVATOR_DATA*, bool*, unsigned int, unsigned int, unsigned int, bool*, char, long double, long double, bool, bool*, char))&CUserDB::Select_Char_Complete)
            },
            _hook_record {
                (LPVOID)0x1401125e0L,
                (LPVOID *)&CUserDBSelect_Char_Request74_user,
                (LPVOID *)&CUserDBSelect_Char_Request74_next,
                (LPVOID)cast_pointer_function(CUserDBSelect_Char_Request74_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char))&CUserDB::Select_Char_Request)
            },
            _hook_record {
                (LPVOID)0x140118720L,
                (LPVOID *)&CUserDBSendMsgAccount_UILockRefresh_Update76_user,
                (LPVOID *)&CUserDBSendMsgAccount_UILockRefresh_Update76_next,
                (LPVOID)cast_pointer_function(CUserDBSendMsgAccount_UILockRefresh_Update76_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)())&CUserDB::SendMsgAccount_UILockRefresh_Update)
            },
            _hook_record {
                (LPVOID)0x140118270L,
                (LPVOID *)&CUserDBSendMsg_BillingInfo78_user,
                (LPVOID *)&CUserDBSendMsg_BillingInfo78_next,
                (LPVOID)cast_pointer_function(CUserDBSendMsg_BillingInfo78_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)())&CUserDB::SendMsg_BillingInfo)
            },
            _hook_record {
                (LPVOID)0x140118650L,
                (LPVOID *)&CUserDBSendMsg_Inform_UILock80_user,
                (LPVOID *)&CUserDBSendMsg_Inform_UILock80_next,
                (LPVOID)cast_pointer_function(CUserDBSendMsg_Inform_UILock80_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)())&CUserDB::SendMsg_Inform_UILock)
            },
            _hook_record {
                (LPVOID)0x14011be90L,
                (LPVOID *)&CUserDBSetActPoint82_user,
                (LPVOID *)&CUserDBSetActPoint82_next,
                (LPVOID)cast_pointer_function(CUserDBSetActPoint82_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(char, unsigned int))&CUserDB::SetActPoint)
            },
            _hook_record {
                (LPVOID)0x1401180e0L,
                (LPVOID *)&CUserDBSetBillingData84_user,
                (LPVOID *)&CUserDBSetBillingData84_next,
                (LPVOID)cast_pointer_function(CUserDBSetBillingData84_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(struct _BILLING_INFO*))&CUserDB::SetBillingData)
            },
            _hook_record {
                (LPVOID)0x140118150L,
                (LPVOID *)&CUserDBSetBillingData86_user,
                (LPVOID *)&CUserDBSetBillingData86_next,
                (LPVOID)cast_pointer_function(CUserDBSetBillingData86_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(char*, int16_t, int, struct _SYSTEMTIME*))&CUserDB::SetBillingData)
            },
            _hook_record {
                (LPVOID)0x14007bf80L,
                (LPVOID *)&CUserDBSetBillingNoLogout88_user,
                (LPVOID *)&CUserDBSetBillingNoLogout88_next,
                (LPVOID)cast_pointer_function(CUserDBSetBillingNoLogout88_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(bool))&CUserDB::SetBillingNoLogout)
            },
            _hook_record {
                (LPVOID)0x1401104a0L,
                (LPVOID *)&CUserDBSetChatLock90_user,
                (LPVOID *)&CUserDBSetChatLock90_next,
                (LPVOID)cast_pointer_function(CUserDBSetChatLock90_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(bool))&CUserDB::SetChatLock)
            },
            _hook_record {
                (LPVOID)0x1401174d0L,
                (LPVOID *)&CUserDBSetDBPostData92_user,
                (LPVOID *)&CUserDBSetDBPostData92_next,
                (LPVOID)cast_pointer_function(CUserDBSetDBPostData92_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(int, unsigned int, int, char, int, uint64_t, unsigned int, unsigned int, bool, uint64_t))&CUserDB::SetDBPostData)
            },
            _hook_record {
                (LPVOID)0x1401176c0L,
                (LPVOID *)&CUserDBSetNewDBPostData94_user,
                (LPVOID *)&CUserDBSetNewDBPostData94_next,
                (LPVOID)cast_pointer_function(CUserDBSetNewDBPostData94_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(int, unsigned int, int, char, char*, char*, char*, char*, int, uint64_t, unsigned int, unsigned int, uint64_t))&CUserDB::SetNewDBPostData)
            },
            _hook_record {
                (LPVOID)0x14011b700L,
                (LPVOID *)&CUserDBSetRadarDelay96_user,
                (LPVOID *)&CUserDBSetRadarDelay96_next,
                (LPVOID)cast_pointer_function(CUserDBSetRadarDelay96_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(unsigned int))&CUserDB::SetRadarDelay)
            },
            _hook_record {
                (LPVOID)0x14028db40L,
                (LPVOID *)&CUserDBSetRemainTime98_user,
                (LPVOID *)&CUserDBSetRemainTime98_next,
                (LPVOID)cast_pointer_function(CUserDBSetRemainTime98_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(int))&CUserDB::SetRemainTime)
            },
            _hook_record {
                (LPVOID)0x1401102a0L,
                (LPVOID *)&CUserDBSetWorldCLID100_user,
                (LPVOID *)&CUserDBSetWorldCLID100_next,
                (LPVOID)cast_pointer_function(CUserDBSetWorldCLID100_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(unsigned int, unsigned int*))&CUserDB::SetWorldCLID)
            },
            _hook_record {
                (LPVOID)0x140118010L,
                (LPVOID *)&CUserDBSetting_Class102_user,
                (LPVOID *)&CUserDBSetting_Class102_next,
                (LPVOID)cast_pointer_function(CUserDBSetting_Class102_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char*))&CUserDB::Setting_Class)
            },
            _hook_record {
                (LPVOID)0x140110330L,
                (LPVOID *)&CUserDBStartFieldMode104_user,
                (LPVOID *)&CUserDBStartFieldMode104_next,
                (LPVOID)cast_pointer_function(CUserDBStartFieldMode104_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)())&CUserDB::StartFieldMode)
            },
            _hook_record {
                (LPVOID)0x140113ac0L,
                (LPVOID *)&CUserDBTotalPlayMinCheck106_user,
                (LPVOID *)&CUserDBTotalPlayMinCheck106_next,
                (LPVOID)cast_pointer_function(CUserDBTotalPlayMinCheck106_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)())&CUserDB::TotalPlayMinCheck)
            },
            _hook_record {
                (LPVOID)0x140118340L,
                (LPVOID *)&CUserDBUILockInfo_Init108_user,
                (LPVOID *)&CUserDBUILockInfo_Init108_next,
                (LPVOID)cast_pointer_function(CUserDBUILockInfo_Init108_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(char*))&CUserDB::UILockInfo_Init)
            },
            _hook_record {
                (LPVOID)0x1401184d0L,
                (LPVOID *)&CUserDBUILockInfo_Update110_user,
                (LPVOID *)&CUserDBUILockInfo_Update110_next,
                (LPVOID)cast_pointer_function(CUserDBUILockInfo_Update110_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(char*))&CUserDB::UILockInfo_Update)
            },
            _hook_record {
                (LPVOID)0x140113df0L,
                (LPVOID *)&CUserDBUpdateContUserSave112_user,
                (LPVOID *)&CUserDBUpdateContUserSave112_next,
                (LPVOID)cast_pointer_function(CUserDBUpdateContUserSave112_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(bool))&CUserDB::UpdateContUserSave)
            },
            _hook_record {
                (LPVOID)0x140116390L,
                (LPVOID *)&CUserDBUpdate_AddBuddy114_user,
                (LPVOID *)&CUserDBUpdate_AddBuddy114_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_AddBuddy114_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, unsigned int, char*))&CUserDB::Update_AddBuddy)
            },
            _hook_record {
                (LPVOID)0x140115fc0L,
                (LPVOID *)&CUserDBUpdate_AlterPvPCashBag116_user,
                (LPVOID *)&CUserDBUpdate_AlterPvPCashBag116_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_AlterPvPCashBag116_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(long double))&CUserDB::Update_AlterPvPCashBag)
            },
            _hook_record {
                (LPVOID)0x140115f80L,
                (LPVOID *)&CUserDBUpdate_AlterPvPPoint118_user,
                (LPVOID *)&CUserDBUpdate_AlterPvPPoint118_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_AlterPvPPoint118_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(long double))&CUserDB::Update_AlterPvPPoint)
            },
            _hook_record {
                (LPVOID)0x140116ec0L,
                (LPVOID *)&CUserDBUpdate_AutoTradeAllClear120_user,
                (LPVOID *)&CUserDBUpdate_AutoTradeAllClear120_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_AutoTradeAllClear120_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)())&CUserDB::Update_AutoTradeAllClear)
            },
            _hook_record {
                (LPVOID)0x140116480L,
                (LPVOID *)&CUserDBUpdate_BagNum122_user,
                (LPVOID *)&CUserDBUpdate_BagNum122_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_BagNum122_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char))&CUserDB::Update_BagNum)
            },
            _hook_record {
                (LPVOID)0x1401162f0L,
                (LPVOID *)&CUserDBUpdate_Bind124_user,
                (LPVOID *)&CUserDBUpdate_Bind124_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_Bind124_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char*, char*, bool))&CUserDB::Update_Bind)
            },
            _hook_record {
                (LPVOID)0x14011b7f0L,
                (LPVOID *)&CUserDBUpdate_BossCryMsg126_user,
                (LPVOID *)&CUserDBUpdate_BossCryMsg126_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_BossCryMsg126_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(char, char*))&CUserDB::Update_BossCryMsg)
            },
            _hook_record {
                (LPVOID)0x140116000L,
                (LPVOID *)&CUserDBUpdate_Class128_user,
                (LPVOID *)&CUserDBUpdate_Class128_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_Class128_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char*, char, uint16_t))&CUserDB::Update_Class)
            },
            _hook_record {
                (LPVOID)0x1401171b0L,
                (LPVOID *)&CUserDBUpdate_CombineExResult_Pop130_user,
                (LPVOID *)&CUserDBUpdate_CombineExResult_Pop130_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_CombineExResult_Pop130_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)())&CUserDB::Update_CombineExResult_Pop)
            },
            _hook_record {
                (LPVOID)0x140117140L,
                (LPVOID *)&CUserDBUpdate_CombineExResult_Push132_user,
                (LPVOID *)&CUserDBUpdate_CombineExResult_Push132_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_CombineExResult_Push132_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(struct _ITEMCOMBINE_DB_BASE*))&CUserDB::Update_CombineExResult_Push)
            },
            _hook_record {
                (LPVOID)0x140117e30L,
                (LPVOID *)&CUserDBUpdate_CopyAll134_user,
                (LPVOID *)&CUserDBUpdate_CopyAll134_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_CopyAll134_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(struct _AVATOR_DATA*))&CUserDB::Update_CopyAll)
            },
            _hook_record {
                (LPVOID)0x140116800L,
                (LPVOID *)&CUserDBUpdate_CuttingEmpty136_user,
                (LPVOID *)&CUserDBUpdate_CuttingEmpty136_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_CuttingEmpty136_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)())&CUserDB::Update_CuttingEmpty)
            },
            _hook_record {
                (LPVOID)0x140116560L,
                (LPVOID *)&CUserDBUpdate_CuttingPush138_user,
                (LPVOID *)&CUserDBUpdate_CuttingPush138_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_CuttingPush138_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, struct _CUTTING_DB_BASE::_LIST*))&CUserDB::Update_CuttingPush)
            },
            _hook_record {
                (LPVOID)0x1401166a0L,
                (LPVOID *)&CUserDBUpdate_CuttingTrans140_user,
                (LPVOID *)&CUserDBUpdate_CuttingTrans140_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_CuttingTrans140_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(uint16_t, uint16_t))&CUserDB::Update_CuttingTrans)
            },
            _hook_record {
                (LPVOID)0x140116420L,
                (LPVOID *)&CUserDBUpdate_DelBuddy142_user,
                (LPVOID *)&CUserDBUpdate_DelBuddy142_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_DelBuddy142_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char))&CUserDB::Update_DelBuddy)
            },
            _hook_record {
                (LPVOID)0x140117b00L,
                (LPVOID *)&CUserDBUpdate_DelPost144_user,
                (LPVOID *)&CUserDBUpdate_DelPost144_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_DelPost144_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(unsigned int, int))&CUserDB::Update_DelPost)
            },
            _hook_record {
                (LPVOID)0x140115f00L,
                (LPVOID *)&CUserDBUpdate_Exp146_user,
                (LPVOID *)&CUserDBUpdate_Exp146_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_Exp146_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(long double))&CUserDB::Update_Exp)
            },
            _hook_record {
                (LPVOID)0x140116f70L,
                (LPVOID *)&CUserDBUpdate_ExtTrunkSlotNum148_user,
                (LPVOID *)&CUserDBUpdate_ExtTrunkSlotNum148_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_ExtTrunkSlotNum148_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char))&CUserDB::Update_ExtTrunkSlotNum)
            },
            _hook_record {
                (LPVOID)0x140114350L,
                (LPVOID *)&CUserDBUpdate_ItemAdd150_user,
                (LPVOID *)&CUserDBUpdate_ItemAdd150_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_ItemAdd150_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, char, struct _STORAGE_LIST::_db_con*, bool))&CUserDB::Update_ItemAdd)
            },
            _hook_record {
                (LPVOID)0x140114850L,
                (LPVOID *)&CUserDBUpdate_ItemDelete152_user,
                (LPVOID *)&CUserDBUpdate_ItemDelete152_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_ItemDelete152_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, char, bool))&CUserDB::Update_ItemDelete)
            },
            _hook_record {
                (LPVOID)0x140114aa0L,
                (LPVOID *)&CUserDBUpdate_ItemDur154_user,
                (LPVOID *)&CUserDBUpdate_ItemDur154_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_ItemDur154_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, char, uint64_t, bool))&CUserDB::Update_ItemDur)
            },
            _hook_record {
                (LPVOID)0x140115800L,
                (LPVOID *)&CUserDBUpdate_ItemSlot156_user,
                (LPVOID *)&CUserDBUpdate_ItemSlot156_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_ItemSlot156_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, char, char))&CUserDB::Update_ItemSlot)
            },
            _hook_record {
                (LPVOID)0x140114f10L,
                (LPVOID *)&CUserDBUpdate_ItemUpgrade158_user,
                (LPVOID *)&CUserDBUpdate_ItemUpgrade158_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_ItemUpgrade158_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, char, unsigned int, bool))&CUserDB::Update_ItemUpgrade)
            },
            _hook_record {
                (LPVOID)0x14007e100L,
                (LPVOID *)&CUserDBUpdate_LastAttBuff160_user,
                (LPVOID *)&CUserDBUpdate_LastAttBuff160_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_LastAttBuff160_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(bool))&CUserDB::Update_LastAttBuff)
            },
            _hook_record {
                (LPVOID)0x140115d60L,
                (LPVOID *)&CUserDBUpdate_Level162_user,
                (LPVOID *)&CUserDBUpdate_Level162_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_Level162_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, long double))&CUserDB::Update_Level)
            },
            _hook_record {
                (LPVOID)0x140115c40L,
                (LPVOID *)&CUserDBUpdate_LinkBoardLock164_user,
                (LPVOID *)&CUserDBUpdate_LinkBoardLock164_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_LinkBoardLock164_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char))&CUserDB::Update_LinkBoardLock)
            },
            _hook_record {
                (LPVOID)0x140115b90L,
                (LPVOID *)&CUserDBUpdate_LinkBoardSlot166_user,
                (LPVOID *)&CUserDBUpdate_LinkBoardSlot166_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_LinkBoardSlot166_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, char, uint16_t))&CUserDB::Update_LinkBoardSlot)
            },
            _hook_record {
                (LPVOID)0x140115f40L,
                (LPVOID *)&CUserDBUpdate_LossExp168_user,
                (LPVOID *)&CUserDBUpdate_LossExp168_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_LossExp168_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(long double))&CUserDB::Update_LossExp)
            },
            _hook_record {
                (LPVOID)0x140117200L,
                (LPVOID *)&CUserDBUpdate_Macro170_user,
                (LPVOID *)&CUserDBUpdate_Macro170_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_Macro170_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char*))&CUserDB::Update_Macro)
            },
            _hook_record {
                (LPVOID)0x140116240L,
                (LPVOID *)&CUserDBUpdate_Map172_user,
                (LPVOID *)&CUserDBUpdate_Map172_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_Map172_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, float*))&CUserDB::Update_Map)
            },
            _hook_record {
                (LPVOID)0x14011b850L,
                (LPVOID *)&CUserDBUpdate_MaxLevel174_user,
                (LPVOID *)&CUserDBUpdate_MaxLevel174_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_MaxLevel174_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(char))&CUserDB::Update_MaxLevel)
            },
            _hook_record {
                (LPVOID)0x1401161f0L,
                (LPVOID *)&CUserDBUpdate_Money176_user,
                (LPVOID *)&CUserDBUpdate_Money176_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_Money176_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(unsigned int, unsigned int))&CUserDB::Update_Money)
            },
            _hook_record {
                (LPVOID)0x14011b880L,
                (LPVOID *)&CUserDBUpdate_NPCQuestHistory178_user,
                (LPVOID *)&CUserDBUpdate_NPCQuestHistory178_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_NPCQuestHistory178_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, struct _QUEST_DB_BASE::_NPC_QUEST_HISTORY*))&CUserDB::Update_NPCQuestHistory)
            },
            _hook_record {
                (LPVOID)0x140116990L,
                (LPVOID *)&CUserDBUpdate_Param180_user,
                (LPVOID *)&CUserDBUpdate_Param180_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_Param180_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(struct _EXIT_ALTER_PARAM*))&CUserDB::Update_Param)
            },
            _hook_record {
                (LPVOID)0x140116960L,
                (LPVOID *)&CUserDBUpdate_PlayTime182_user,
                (LPVOID *)&CUserDBUpdate_PlayTime182_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_PlayTime182_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(unsigned int))&CUserDB::Update_PlayTime)
            },
            _hook_record {
                (LPVOID)0x140117900L,
                (LPVOID *)&CUserDBUpdate_Post184_user,
                (LPVOID *)&CUserDBUpdate_Post184_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_Post184_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(int, unsigned int, int, char, int, uint64_t, unsigned int, unsigned int, uint64_t))&CUserDB::Update_Post)
            },
            _hook_record {
                (LPVOID)0x14011ba90L,
                (LPVOID *)&CUserDBUpdate_PotionNextUseTime186_user,
                (LPVOID *)&CUserDBUpdate_PotionNextUseTime186_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_PotionNextUseTime186_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(char, unsigned int))&CUserDB::Update_PotionNextUseTime)
            },
            _hook_record {
                (LPVOID)0x14011ba60L,
                (LPVOID *)&CUserDBUpdate_PvpPointLeak188_user,
                (LPVOID *)&CUserDBUpdate_PvpPointLeak188_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_PvpPointLeak188_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(long double))&CUserDB::Update_PvpPointLeak)
            },
            _hook_record {
                (LPVOID)0x140115620L,
                (LPVOID *)&CUserDBUpdate_QuestDelete190_user,
                (LPVOID *)&CUserDBUpdate_QuestDelete190_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_QuestDelete190_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char))&CUserDB::Update_QuestDelete)
            },
            _hook_record {
                (LPVOID)0x140115530L,
                (LPVOID *)&CUserDBUpdate_QuestInsert192_user,
                (LPVOID *)&CUserDBUpdate_QuestInsert192_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_QuestInsert192_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, struct _QUEST_DB_BASE::_LIST*))&CUserDB::Update_QuestInsert)
            },
            _hook_record {
                (LPVOID)0x140115700L,
                (LPVOID *)&CUserDBUpdate_QuestUpdate194_user,
                (LPVOID *)&CUserDBUpdate_QuestUpdate194_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_QuestUpdate194_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, struct _QUEST_DB_BASE::_LIST*, bool))&CUserDB::Update_QuestUpdate)
            },
            _hook_record {
                (LPVOID)0x14011bb90L,
                (LPVOID *)&CUserDBUpdate_RaceVoteInfoInit196_user,
                (LPVOID *)&CUserDBUpdate_RaceVoteInfoInit196_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_RaceVoteInfoInit196_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)())&CUserDB::Update_RaceVoteInfoInit)
            },
            _hook_record {
                (LPVOID)0x140117a80L,
                (LPVOID *)&CUserDBUpdate_ReturnPost198_user,
                (LPVOID *)&CUserDBUpdate_ReturnPost198_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_ReturnPost198_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(unsigned int))&CUserDB::Update_ReturnPost)
            },
            _hook_record {
                (LPVOID)0x140116c30L,
                (LPVOID *)&CUserDBUpdate_SFContDelete200_user,
                (LPVOID *)&CUserDBUpdate_SFContDelete200_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_SFContDelete200_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, char))&CUserDB::Update_SFContDelete)
            },
            _hook_record {
                (LPVOID)0x140116ad0L,
                (LPVOID *)&CUserDBUpdate_SFContInsert202_user,
                (LPVOID *)&CUserDBUpdate_SFContInsert202_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_SFContInsert202_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, char, char, uint16_t, char, uint16_t))&CUserDB::Update_SFContInsert)
            },
            _hook_record {
                (LPVOID)0x140116d80L,
                (LPVOID *)&CUserDBUpdate_SFContUpdate204_user,
                (LPVOID *)&CUserDBUpdate_SFContUpdate204_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_SFContUpdate204_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, char, uint16_t, bool))&CUserDB::Update_SFContUpdate)
            },
            _hook_record {
                (LPVOID)0x14011b920L,
                (LPVOID *)&CUserDBUpdate_StartNPCQuestHistory206_user,
                (LPVOID *)&CUserDBUpdate_StartNPCQuestHistory206_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_StartNPCQuestHistory206_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, struct _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY*))&CUserDB::Update_StartNPCQuestHistory)
            },
            _hook_record {
                (LPVOID)0x140116850L,
                (LPVOID *)&CUserDBUpdate_Stat208_user,
                (LPVOID *)&CUserDBUpdate_Stat208_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_Stat208_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, unsigned int, bool))&CUserDB::Update_Stat)
            },
            _hook_record {
                (LPVOID)0x140117e00L,
                (LPVOID *)&CUserDBUpdate_TakeLastCriTicket210_user,
                (LPVOID *)&CUserDBUpdate_TakeLastCriTicket210_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_TakeLastCriTicket210_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(unsigned int))&CUserDB::Update_TakeLastCriTicket)
            },
            _hook_record {
                (LPVOID)0x140117dd0L,
                (LPVOID *)&CUserDBUpdate_TakeLastMentalTicket212_user,
                (LPVOID *)&CUserDBUpdate_TakeLastMentalTicket212_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_TakeLastMentalTicket212_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(unsigned int))&CUserDB::Update_TakeLastMentalTicket)
            },
            _hook_record {
                (LPVOID)0x1401170c0L,
                (LPVOID *)&CUserDBUpdate_TrunkHint214_user,
                (LPVOID *)&CUserDBUpdate_TrunkHint214_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_TrunkHint214_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, char*))&CUserDB::Update_TrunkHint)
            },
            _hook_record {
                (LPVOID)0x140117040L,
                (LPVOID *)&CUserDBUpdate_TrunkMoney216_user,
                (LPVOID *)&CUserDBUpdate_TrunkMoney216_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_TrunkMoney216_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(long double, long double))&CUserDB::Update_TrunkMoney)
            },
            _hook_record {
                (LPVOID)0x140116fd0L,
                (LPVOID *)&CUserDBUpdate_TrunkPassword218_user,
                (LPVOID *)&CUserDBUpdate_TrunkPassword218_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_TrunkPassword218_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char*))&CUserDB::Update_TrunkPassword)
            },
            _hook_record {
                (LPVOID)0x140116f10L,
                (LPVOID *)&CUserDBUpdate_TrunkSlotNum220_user,
                (LPVOID *)&CUserDBUpdate_TrunkSlotNum220_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_TrunkSlotNum220_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char))&CUserDB::Update_TrunkSlotNum)
            },
            _hook_record {
                (LPVOID)0x140115440L,
                (LPVOID *)&CUserDBUpdate_UnitData222_user,
                (LPVOID *)&CUserDBUpdate_UnitData222_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_UnitData222_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, struct _UNIT_DB_BASE::_LIST*))&CUserDB::Update_UnitData)
            },
            _hook_record {
                (LPVOID)0x140115360L,
                (LPVOID *)&CUserDBUpdate_UnitDelete224_user,
                (LPVOID *)&CUserDBUpdate_UnitDelete224_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_UnitDelete224_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char))&CUserDB::Update_UnitDelete)
            },
            _hook_record {
                (LPVOID)0x140115270L,
                (LPVOID *)&CUserDBUpdate_UnitInsert226_user,
                (LPVOID *)&CUserDBUpdate_UnitInsert226_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_UnitInsert226_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, struct _UNIT_DB_BASE::_LIST*))&CUserDB::Update_UnitInsert)
            },
            _hook_record {
                (LPVOID)0x14011bdc0L,
                (LPVOID *)&CUserDBUpdate_UserFatigue228_user,
                (LPVOID *)&CUserDBUpdate_UserFatigue228_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_UserFatigue228_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(unsigned int))&CUserDB::Update_UserFatigue)
            },
            _hook_record {
                (LPVOID)0x14011bc50L,
                (LPVOID *)&CUserDBUpdate_UserGetScaner230_user,
                (LPVOID *)&CUserDBUpdate_UserGetScaner230_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_UserGetScaner230_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(uint16_t, uint16_t))&CUserDB::Update_UserGetScaner)
            },
            _hook_record {
                (LPVOID)0x14011bc10L,
                (LPVOID *)&CUserDBUpdate_UserPlayTime232_user,
                (LPVOID *)&CUserDBUpdate_UserPlayTime232_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_UserPlayTime232_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(unsigned int))&CUserDB::Update_UserPlayTime)
            },
            _hook_record {
                (LPVOID)0x14011bdf0L,
                (LPVOID *)&CUserDBUpdate_UserTLStatus234_user,
                (LPVOID *)&CUserDBUpdate_UserTLStatus234_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_UserTLStatus234_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char))&CUserDB::Update_UserTLStatus)
            },
            _hook_record {
                (LPVOID)0x14011bd80L,
                (LPVOID *)&CUserDBUpdate_UserVoteData236_user,
                (LPVOID *)&CUserDBUpdate_UserVoteData236_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_UserVoteData236_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)())&CUserDB::Update_UserVoteData)
            },
            _hook_record {
                (LPVOID)0x14011be20L,
                (LPVOID *)&CUserDBUpdate_User_Action_Point238_user,
                (LPVOID *)&CUserDBUpdate_User_Action_Point238_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_User_Action_Point238_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(char, unsigned int))&CUserDB::Update_User_Action_Point)
            },
            _hook_record {
                (LPVOID)0x140115c70L,
                (LPVOID *)&CUserDBUpdate_WindowInfo240_user,
                (LPVOID *)&CUserDBUpdate_WindowInfo240_next,
                (LPVOID)cast_pointer_function(CUserDBUpdate_WindowInfo240_wrapper),
                (LPVOID)cast_pointer_function((bool(CUserDB::*)(unsigned int*, unsigned int*, unsigned int*, unsigned int*, unsigned int, unsigned int*))&CUserDB::Update_WindowInfo)
            },
            _hook_record {
                (LPVOID)0x14011b280L,
                (LPVOID *)&CUserDBWriteLog_ChangeClassAfterInitClass242_user,
                (LPVOID *)&CUserDBWriteLog_ChangeClassAfterInitClass242_next,
                (LPVOID)cast_pointer_function(CUserDBWriteLog_ChangeClassAfterInitClass242_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(char, char*))&CUserDB::WriteLog_ChangeClassAfterInitClass)
            },
            _hook_record {
                (LPVOID)0x140113c00L,
                (LPVOID *)&CUserDBWriteLog_CharSelect244_user,
                (LPVOID *)&CUserDBWriteLog_CharSelect244_next,
                (LPVOID)cast_pointer_function(CUserDBWriteLog_CharSelect244_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)())&CUserDB::WriteLog_CharSelect)
            },
            _hook_record {
                (LPVOID)0x140113b40L,
                (LPVOID *)&CUserDBWriteLog_Level246_user,
                (LPVOID *)&CUserDBWriteLog_Level246_next,
                (LPVOID)cast_pointer_function(CUserDBWriteLog_Level246_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)(char))&CUserDB::WriteLog_Level)
            },
            _hook_record {
                (LPVOID)0x14010fed0L,
                (LPVOID *)&CUserDBdtor_CUserDB252_user,
                (LPVOID *)&CUserDBdtor_CUserDB252_next,
                (LPVOID)cast_pointer_function(CUserDBdtor_CUserDB252_wrapper),
                (LPVOID)cast_pointer_function((void(CUserDB::*)())&CUserDB::dtor_CUserDB)
            },
        };
    }; // end namespace Detail
END_ATF_NAMESPACE
