// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_LARGE_INTEGER.hpp>



START_ATF_NAMESPACE
    #pragma pack(push, 4)
    struct _WIN32_STREAM_ID
    {
        unsigned int dwStreamId;
        unsigned int dwStreamAttributes;
        _LARGE_INTEGER Size;
        unsigned int dwStreamNameSize;
        wchar_t cStreamName[1];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
