#include <_personal_automine_delbattery_zocl.hpp>


START_ATF_NAMESPACE
    _personal_automine_delbattery_zocl::_personal_automine_delbattery_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_delbattery_zocl*);
        (org_ptr(0x1402de130L))(this);
    };
    void _personal_automine_delbattery_zocl::ctor__personal_automine_delbattery_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_delbattery_zocl*);
        (org_ptr(0x1402de130L))(this);
    };
    int _personal_automine_delbattery_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _personal_automine_delbattery_zocl*);
        return (org_ptr(0x1402de160L))(this);
    };
END_ATF_NAMESPACE
