#include <GUILD_BATTLE__CGuildBattleRankManagerDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Detail
        {
            
            Info::GUILD_BATTLE__CGuildBattleRankManagerctor_CGuildBattleRankManager2_ptr GUILD_BATTLE__CGuildBattleRankManagerctor_CGuildBattleRankManager2_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleRankManagerctor_CGuildBattleRankManager2_clbk GUILD_BATTLE__CGuildBattleRankManagerctor_CGuildBattleRankManager2_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleRankManagerCheckRecord4_ptr GUILD_BATTLE__CGuildBattleRankManagerCheckRecord4_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleRankManagerCheckRecord4_clbk GUILD_BATTLE__CGuildBattleRankManagerCheckRecord4_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleRankManagerCleanUp6_ptr GUILD_BATTLE__CGuildBattleRankManagerCleanUp6_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleRankManagerCleanUp6_clbk GUILD_BATTLE__CGuildBattleRankManagerCleanUp6_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleRankManagerClear8_ptr GUILD_BATTLE__CGuildBattleRankManagerClear8_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleRankManagerClear8_clbk GUILD_BATTLE__CGuildBattleRankManagerClear8_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleRankManagerClear10_ptr GUILD_BATTLE__CGuildBattleRankManagerClear10_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleRankManagerClear10_clbk GUILD_BATTLE__CGuildBattleRankManagerClear10_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleRankManagerDestroy12_ptr GUILD_BATTLE__CGuildBattleRankManagerDestroy12_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleRankManagerDestroy12_clbk GUILD_BATTLE__CGuildBattleRankManagerDestroy12_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleRankManagerFind14_ptr GUILD_BATTLE__CGuildBattleRankManagerFind14_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleRankManagerFind14_clbk GUILD_BATTLE__CGuildBattleRankManagerFind14_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleRankManagerInit16_ptr GUILD_BATTLE__CGuildBattleRankManagerInit16_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleRankManagerInit16_clbk GUILD_BATTLE__CGuildBattleRankManagerInit16_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleRankManagerInstance18_ptr GUILD_BATTLE__CGuildBattleRankManagerInstance18_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleRankManagerInstance18_clbk GUILD_BATTLE__CGuildBattleRankManagerInstance18_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleRankManagerLoad20_ptr GUILD_BATTLE__CGuildBattleRankManagerLoad20_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleRankManagerLoad20_clbk GUILD_BATTLE__CGuildBattleRankManagerLoad20_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleRankManagerLoad22_ptr GUILD_BATTLE__CGuildBattleRankManagerLoad22_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleRankManagerLoad22_clbk GUILD_BATTLE__CGuildBattleRankManagerLoad22_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleRankManagerPushClearGuildBattleRank24_ptr GUILD_BATTLE__CGuildBattleRankManagerPushClearGuildBattleRank24_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleRankManagerPushClearGuildBattleRank24_clbk GUILD_BATTLE__CGuildBattleRankManagerPushClearGuildBattleRank24_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleRankManagerPushCreateGuildBattleRankTable26_ptr GUILD_BATTLE__CGuildBattleRankManagerPushCreateGuildBattleRankTable26_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleRankManagerPushCreateGuildBattleRankTable26_clbk GUILD_BATTLE__CGuildBattleRankManagerPushCreateGuildBattleRankTable26_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleRankManagerSelectGuildBattleRankList28_ptr GUILD_BATTLE__CGuildBattleRankManagerSelectGuildBattleRankList28_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleRankManagerSelectGuildBattleRankList28_clbk GUILD_BATTLE__CGuildBattleRankManagerSelectGuildBattleRankList28_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleRankManagerSend30_ptr GUILD_BATTLE__CGuildBattleRankManagerSend30_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleRankManagerSend30_clbk GUILD_BATTLE__CGuildBattleRankManagerSend30_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleRankManagerUpdate32_ptr GUILD_BATTLE__CGuildBattleRankManagerUpdate32_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleRankManagerUpdate32_clbk GUILD_BATTLE__CGuildBattleRankManagerUpdate32_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleRankManagerUpdateDraw34_ptr GUILD_BATTLE__CGuildBattleRankManagerUpdateDraw34_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleRankManagerUpdateDraw34_clbk GUILD_BATTLE__CGuildBattleRankManagerUpdateDraw34_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleRankManagerUpdateWinLose36_ptr GUILD_BATTLE__CGuildBattleRankManagerUpdateWinLose36_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleRankManagerUpdateWinLose36_clbk GUILD_BATTLE__CGuildBattleRankManagerUpdateWinLose36_user(nullptr);
            
            
            Info::GUILD_BATTLE__CGuildBattleRankManagerdtor_CGuildBattleRankManager40_ptr GUILD_BATTLE__CGuildBattleRankManagerdtor_CGuildBattleRankManager40_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleRankManagerdtor_CGuildBattleRankManager40_clbk GUILD_BATTLE__CGuildBattleRankManagerdtor_CGuildBattleRankManager40_user(nullptr);
            
            
            void GUILD_BATTLE__CGuildBattleRankManagerctor_CGuildBattleRankManager2_wrapper(struct GUILD_BATTLE::CGuildBattleRankManager* _this)
            {
               GUILD_BATTLE__CGuildBattleRankManagerctor_CGuildBattleRankManager2_user(_this, GUILD_BATTLE__CGuildBattleRankManagerctor_CGuildBattleRankManager2_next);
            };
            bool GUILD_BATTLE__CGuildBattleRankManagerCheckRecord4_wrapper(struct GUILD_BATTLE::CGuildBattleRankManager* _this, unsigned int dwGuildSerial)
            {
               return GUILD_BATTLE__CGuildBattleRankManagerCheckRecord4_user(_this, dwGuildSerial, GUILD_BATTLE__CGuildBattleRankManagerCheckRecord4_next);
            };
            void GUILD_BATTLE__CGuildBattleRankManagerCleanUp6_wrapper(struct GUILD_BATTLE::CGuildBattleRankManager* _this)
            {
               GUILD_BATTLE__CGuildBattleRankManagerCleanUp6_user(_this, GUILD_BATTLE__CGuildBattleRankManagerCleanUp6_next);
            };
            void GUILD_BATTLE__CGuildBattleRankManagerClear8_wrapper(struct GUILD_BATTLE::CGuildBattleRankManager* _this, char byRace)
            {
               GUILD_BATTLE__CGuildBattleRankManagerClear8_user(_this, byRace, GUILD_BATTLE__CGuildBattleRankManagerClear8_next);
            };
            void GUILD_BATTLE__CGuildBattleRankManagerClear10_wrapper(struct GUILD_BATTLE::CGuildBattleRankManager* _this)
            {
               GUILD_BATTLE__CGuildBattleRankManagerClear10_user(_this, GUILD_BATTLE__CGuildBattleRankManagerClear10_next);
            };
            void GUILD_BATTLE__CGuildBattleRankManagerDestroy12_wrapper()
            {
               GUILD_BATTLE__CGuildBattleRankManagerDestroy12_user(GUILD_BATTLE__CGuildBattleRankManagerDestroy12_next);
            };
            bool GUILD_BATTLE__CGuildBattleRankManagerFind14_wrapper(struct GUILD_BATTLE::CGuildBattleRankManager* _this, char byRace, unsigned int dwGuildSerial, int* iFindInx, char* byFindPage)
            {
               return GUILD_BATTLE__CGuildBattleRankManagerFind14_user(_this, byRace, dwGuildSerial, iFindInx, byFindPage, GUILD_BATTLE__CGuildBattleRankManagerFind14_next);
            };
            bool GUILD_BATTLE__CGuildBattleRankManagerInit16_wrapper(struct GUILD_BATTLE::CGuildBattleRankManager* _this)
            {
               return GUILD_BATTLE__CGuildBattleRankManagerInit16_user(_this, GUILD_BATTLE__CGuildBattleRankManagerInit16_next);
            };
            struct GUILD_BATTLE::CGuildBattleRankManager* GUILD_BATTLE__CGuildBattleRankManagerInstance18_wrapper()
            {
               return GUILD_BATTLE__CGuildBattleRankManagerInstance18_user(GUILD_BATTLE__CGuildBattleRankManagerInstance18_next);
            };
            bool GUILD_BATTLE__CGuildBattleRankManagerLoad20_wrapper(struct GUILD_BATTLE::CGuildBattleRankManager* _this, char byRace)
            {
               return GUILD_BATTLE__CGuildBattleRankManagerLoad20_user(_this, byRace, GUILD_BATTLE__CGuildBattleRankManagerLoad20_next);
            };
            bool GUILD_BATTLE__CGuildBattleRankManagerLoad22_wrapper(struct GUILD_BATTLE::CGuildBattleRankManager* _this)
            {
               return GUILD_BATTLE__CGuildBattleRankManagerLoad22_user(_this, GUILD_BATTLE__CGuildBattleRankManagerLoad22_next);
            };
            void GUILD_BATTLE__CGuildBattleRankManagerPushClearGuildBattleRank24_wrapper(struct GUILD_BATTLE::CGuildBattleRankManager* _this)
            {
               GUILD_BATTLE__CGuildBattleRankManagerPushClearGuildBattleRank24_user(_this, GUILD_BATTLE__CGuildBattleRankManagerPushClearGuildBattleRank24_next);
            };
            void GUILD_BATTLE__CGuildBattleRankManagerPushCreateGuildBattleRankTable26_wrapper(struct GUILD_BATTLE::CGuildBattleRankManager* _this)
            {
               GUILD_BATTLE__CGuildBattleRankManagerPushCreateGuildBattleRankTable26_user(_this, GUILD_BATTLE__CGuildBattleRankManagerPushCreateGuildBattleRankTable26_next);
            };
            bool GUILD_BATTLE__CGuildBattleRankManagerSelectGuildBattleRankList28_wrapper(struct GUILD_BATTLE::CGuildBattleRankManager* _this, char byRace, char* pOutData)
            {
               return GUILD_BATTLE__CGuildBattleRankManagerSelectGuildBattleRankList28_user(_this, byRace, pOutData, GUILD_BATTLE__CGuildBattleRankManagerSelectGuildBattleRankList28_next);
            };
            void GUILD_BATTLE__CGuildBattleRankManagerSend30_wrapper(struct GUILD_BATTLE::CGuildBattleRankManager* _this, int n, char bySelfRace, unsigned int dwCurVer, char byTabRace, char byPage, unsigned int dwGuildSerial)
            {
               GUILD_BATTLE__CGuildBattleRankManagerSend30_user(_this, n, bySelfRace, dwCurVer, byTabRace, byPage, dwGuildSerial, GUILD_BATTLE__CGuildBattleRankManagerSend30_next);
            };
            bool GUILD_BATTLE__CGuildBattleRankManagerUpdate32_wrapper(struct GUILD_BATTLE::CGuildBattleRankManager* _this, char byRace, char* pLoadData)
            {
               return GUILD_BATTLE__CGuildBattleRankManagerUpdate32_user(_this, byRace, pLoadData, GUILD_BATTLE__CGuildBattleRankManagerUpdate32_next);
            };
            bool GUILD_BATTLE__CGuildBattleRankManagerUpdateDraw34_wrapper(struct GUILD_BATTLE::CGuildBattleRankManager* _this, char by1PRace, unsigned int dw1PGuildSerial, char by2PRace, unsigned int dw2PGuildSerial)
            {
               return GUILD_BATTLE__CGuildBattleRankManagerUpdateDraw34_user(_this, by1PRace, dw1PGuildSerial, by2PRace, dw2PGuildSerial, GUILD_BATTLE__CGuildBattleRankManagerUpdateDraw34_next);
            };
            bool GUILD_BATTLE__CGuildBattleRankManagerUpdateWinLose36_wrapper(struct GUILD_BATTLE::CGuildBattleRankManager* _this, char byWinRace, unsigned int dwWinGuildSerial, char byLoseRace, unsigned int dwLoseGuildSerial)
            {
               return GUILD_BATTLE__CGuildBattleRankManagerUpdateWinLose36_user(_this, byWinRace, dwWinGuildSerial, byLoseRace, dwLoseGuildSerial, GUILD_BATTLE__CGuildBattleRankManagerUpdateWinLose36_next);
            };
            
            void GUILD_BATTLE__CGuildBattleRankManagerdtor_CGuildBattleRankManager40_wrapper(struct GUILD_BATTLE::CGuildBattleRankManager* _this)
            {
               GUILD_BATTLE__CGuildBattleRankManagerdtor_CGuildBattleRankManager40_user(_this, GUILD_BATTLE__CGuildBattleRankManagerdtor_CGuildBattleRankManager40_next);
            };
            
            ::std::array<hook_record, 19> CGuildBattleRankManager_functions = 
            {
                _hook_record {
                    (LPVOID)0x1403ca2f0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerctor_CGuildBattleRankManager2_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerctor_CGuildBattleRankManager2_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleRankManagerctor_CGuildBattleRankManager2_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleRankManager::*)())&GUILD_BATTLE::CGuildBattleRankManager::ctor_CGuildBattleRankManager)
                },
                _hook_record {
                    (LPVOID)0x1403cb7c0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerCheckRecord4_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerCheckRecord4_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleRankManagerCheckRecord4_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleRankManager::*)(unsigned int))&GUILD_BATTLE::CGuildBattleRankManager::CheckRecord)
                },
                _hook_record {
                    (LPVOID)0x1403cb3f0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerCleanUp6_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerCleanUp6_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleRankManagerCleanUp6_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleRankManager::*)())&GUILD_BATTLE::CGuildBattleRankManager::CleanUp)
                },
                _hook_record {
                    (LPVOID)0x1403cb530L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerClear8_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerClear8_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleRankManagerClear8_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleRankManager::*)(char))&GUILD_BATTLE::CGuildBattleRankManager::Clear)
                },
                _hook_record {
                    (LPVOID)0x1403cb4b0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerClear10_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerClear10_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleRankManagerClear10_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleRankManager::*)())&GUILD_BATTLE::CGuildBattleRankManager::Clear)
                },
                _hook_record {
                    (LPVOID)0x1403ca430L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerDestroy12_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerDestroy12_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleRankManagerDestroy12_wrapper),
                    (LPVOID)cast_pointer_function((void(*)())&GUILD_BATTLE::CGuildBattleRankManager::Destroy)
                },
                _hook_record {
                    (LPVOID)0x1403cb6f0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerFind14_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerFind14_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleRankManagerFind14_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleRankManager::*)(char, unsigned int, int*, char*))&GUILD_BATTLE::CGuildBattleRankManager::Find)
                },
                _hook_record {
                    (LPVOID)0x1403ca4b0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerInit16_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerInit16_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleRankManagerInit16_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleRankManager::*)())&GUILD_BATTLE::CGuildBattleRankManager::Init)
                },
                _hook_record {
                    (LPVOID)0x1403ca370L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerInstance18_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerInstance18_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleRankManagerInstance18_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CGuildBattleRankManager*(*)())&GUILD_BATTLE::CGuildBattleRankManager::Instance)
                },
                _hook_record {
                    (LPVOID)0x1403cb820L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerLoad20_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerLoad20_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleRankManagerLoad20_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleRankManager::*)(char))&GUILD_BATTLE::CGuildBattleRankManager::Load)
                },
                _hook_record {
                    (LPVOID)0x1403ca610L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerLoad22_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerLoad22_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleRankManagerLoad22_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleRankManager::*)())&GUILD_BATTLE::CGuildBattleRankManager::Load)
                },
                _hook_record {
                    (LPVOID)0x1403cb380L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerPushClearGuildBattleRank24_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerPushClearGuildBattleRank24_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleRankManagerPushClearGuildBattleRank24_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleRankManager::*)())&GUILD_BATTLE::CGuildBattleRankManager::PushClearGuildBattleRank)
                },
                _hook_record {
                    (LPVOID)0x1403cb250L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerPushCreateGuildBattleRankTable26_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerPushCreateGuildBattleRankTable26_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleRankManagerPushCreateGuildBattleRankTable26_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleRankManager::*)())&GUILD_BATTLE::CGuildBattleRankManager::PushCreateGuildBattleRankTable)
                },
                _hook_record {
                    (LPVOID)0x1403caf70L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerSelectGuildBattleRankList28_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerSelectGuildBattleRankList28_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleRankManagerSelectGuildBattleRankList28_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleRankManager::*)(char, char*))&GUILD_BATTLE::CGuildBattleRankManager::SelectGuildBattleRankList)
                },
                _hook_record {
                    (LPVOID)0x1403ca9c0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerSend30_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerSend30_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleRankManagerSend30_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleRankManager::*)(int, char, unsigned int, char, char, unsigned int))&GUILD_BATTLE::CGuildBattleRankManager::Send)
                },
                _hook_record {
                    (LPVOID)0x1403ca680L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerUpdate32_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerUpdate32_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleRankManagerUpdate32_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleRankManager::*)(char, char*))&GUILD_BATTLE::CGuildBattleRankManager::Update)
                },
                _hook_record {
                    (LPVOID)0x1403cb120L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerUpdateDraw34_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerUpdateDraw34_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleRankManagerUpdateDraw34_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleRankManager::*)(char, unsigned int, char, unsigned int))&GUILD_BATTLE::CGuildBattleRankManager::UpdateDraw)
                },
                _hook_record {
                    (LPVOID)0x1403caff0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerUpdateWinLose36_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerUpdateWinLose36_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleRankManagerUpdateWinLose36_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleRankManager::*)(char, unsigned int, char, unsigned int))&GUILD_BATTLE::CGuildBattleRankManager::UpdateWinLose)
                },
                _hook_record {
                    (LPVOID)0x1403ca330L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerdtor_CGuildBattleRankManager40_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleRankManagerdtor_CGuildBattleRankManager40_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleRankManagerdtor_CGuildBattleRankManager40_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleRankManager::*)())&GUILD_BATTLE::CGuildBattleRankManager::dtor_CGuildBattleRankManager)
                },
            };
        }; // end namespace Detail
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE
