// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <__inner_check.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using __inner_checkctor___inner_check2_ptr = void (WINAPIV*)(struct __inner_check*);
        using __inner_checkctor___inner_check2_clbk = void (WINAPIV*)(struct __inner_check*, __inner_checkctor___inner_check2_ptr);
        
        using __inner_checkdtor___inner_check6_ptr = void (WINAPIV*)(struct __inner_check*);
        using __inner_checkdtor___inner_check6_clbk = void (WINAPIV*)(struct __inner_check*, __inner_checkdtor___inner_check6_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
