#include <_QUEST_DB_BASE.hpp>


START_ATF_NAMESPACE
    void _QUEST_DB_BASE::Init()
    {
        using org_ptr = void (WINAPIV*)(struct _QUEST_DB_BASE*);
        (org_ptr(0x140076a30L))(this);
    };
    _QUEST_DB_BASE::_QUEST_DB_BASE()
    {
        using org_ptr = void (WINAPIV*)(struct _QUEST_DB_BASE*);
        (org_ptr(0x140076800L))(this);
    };
    void _QUEST_DB_BASE::ctor__QUEST_DB_BASE()
    {
        using org_ptr = void (WINAPIV*)(struct _QUEST_DB_BASE*);
        (org_ptr(0x140076800L))(this);
    };
    void _QUEST_DB_BASE::_LIST::Init()
    {
        using org_ptr = void (WINAPIV*)(struct _QUEST_DB_BASE::_LIST*);
        (org_ptr(0x1400768f0L))(this);
    };
    _QUEST_DB_BASE::_LIST::_LIST()
    {
        using org_ptr = void (WINAPIV*)(struct _QUEST_DB_BASE::_LIST*);
        (org_ptr(0x1400768a0L))(this);
    };
    void _QUEST_DB_BASE::_LIST::ctor__LIST()
    {
        using org_ptr = void (WINAPIV*)(struct _QUEST_DB_BASE::_LIST*);
        (org_ptr(0x1400768a0L))(this);
    };
    void _QUEST_DB_BASE::_NPC_QUEST_HISTORY::Init()
    {
        using org_ptr = void (WINAPIV*)(struct _QUEST_DB_BASE::_NPC_QUEST_HISTORY*);
        (org_ptr(0x1400769d0L))(this);
    };
    _QUEST_DB_BASE::_NPC_QUEST_HISTORY::_NPC_QUEST_HISTORY()
    {
        using org_ptr = void (WINAPIV*)(struct _QUEST_DB_BASE::_NPC_QUEST_HISTORY*);
        (org_ptr(0x140076980L))(this);
    };
    void _QUEST_DB_BASE::_NPC_QUEST_HISTORY::ctor__NPC_QUEST_HISTORY()
    {
        using org_ptr = void (WINAPIV*)(struct _QUEST_DB_BASE::_NPC_QUEST_HISTORY*);
        (org_ptr(0x140076980L))(this);
    };
    void _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY::Init()
    {
        using org_ptr = void (WINAPIV*)(struct _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY*);
        (org_ptr(0x1400cfcf0L))(this);
    };
    _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY::_START_NPC_QUEST_HISTORY()
    {
        using org_ptr = void (WINAPIV*)(struct _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY*);
        (org_ptr(0x1400cfca0L))(this);
    };
    void _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY::ctor__START_NPC_QUEST_HISTORY()
    {
        using org_ptr = void (WINAPIV*)(struct _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY*);
        (org_ptr(0x1400cfca0L))(this);
    };
END_ATF_NAMESPACE
