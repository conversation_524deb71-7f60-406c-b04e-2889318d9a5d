#include <CUnmannedTraderTaxRateManagerDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace Detail
    {
        
        Info::CUnmannedTraderTaxRateManagerctor_CUnmannedTraderTaxRateManager2_ptr CUnmannedTraderTaxRateManagerctor_CUnmannedTraderTaxRateManager2_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerctor_CUnmannedTraderTaxRateManager2_clbk CUnmannedTraderTaxRateManagerctor_CUnmannedTraderTaxRateManager2_user(nullptr);
        
        Info::CUnmannedTraderTaxRateManagerChangeOwner4_ptr CUnmannedTraderTaxRateManagerChangeOwner4_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerChangeOwner4_clbk CUnmannedTraderTaxRateManagerChangeOwner4_user(nullptr);
        
        Info::CUnmannedTraderTaxRateManagerCheatChangeTaxRate6_ptr CUnmannedTraderTaxRateManagerCheatChangeTaxRate6_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerCheatChangeTaxRate6_clbk CUnmannedTraderTaxRateManagerCheatChangeTaxRate6_user(nullptr);
        
        Info::CUnmannedTraderTaxRateManagerCleanUp8_ptr CUnmannedTraderTaxRateManagerCleanUp8_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerCleanUp8_clbk CUnmannedTraderTaxRateManagerCleanUp8_user(nullptr);
        
        Info::CUnmannedTraderTaxRateManagerCompleteCreate10_ptr CUnmannedTraderTaxRateManagerCompleteCreate10_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerCompleteCreate10_clbk CUnmannedTraderTaxRateManagerCompleteCreate10_user(nullptr);
        
        Info::CUnmannedTraderTaxRateManagerDQSCompleteInAtradTaxMoney12_ptr CUnmannedTraderTaxRateManagerDQSCompleteInAtradTaxMoney12_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerDQSCompleteInAtradTaxMoney12_clbk CUnmannedTraderTaxRateManagerDQSCompleteInAtradTaxMoney12_user(nullptr);
        
        Info::CUnmannedTraderTaxRateManagerDestroy14_ptr CUnmannedTraderTaxRateManagerDestroy14_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerDestroy14_clbk CUnmannedTraderTaxRateManagerDestroy14_user(nullptr);
        
        Info::CUnmannedTraderTaxRateManagerGetSuggestedTime16_ptr CUnmannedTraderTaxRateManagerGetSuggestedTime16_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerGetSuggestedTime16_clbk CUnmannedTraderTaxRateManagerGetSuggestedTime16_user(nullptr);
        
        Info::CUnmannedTraderTaxRateManagerGetTax18_ptr CUnmannedTraderTaxRateManagerGetTax18_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerGetTax18_clbk CUnmannedTraderTaxRateManagerGetTax18_user(nullptr);
        
        Info::CUnmannedTraderTaxRateManagerGetTaxRate20_ptr CUnmannedTraderTaxRateManagerGetTaxRate20_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerGetTaxRate20_clbk CUnmannedTraderTaxRateManagerGetTaxRate20_user(nullptr);
        
        Info::CUnmannedTraderTaxRateManagerInit22_ptr CUnmannedTraderTaxRateManagerInit22_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerInit22_clbk CUnmannedTraderTaxRateManagerInit22_user(nullptr);
        
        Info::CUnmannedTraderTaxRateManagerInstance24_ptr CUnmannedTraderTaxRateManagerInstance24_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerInstance24_clbk CUnmannedTraderTaxRateManagerInstance24_user(nullptr);
        
        Info::CUnmannedTraderTaxRateManagerIsOwnerGuild26_ptr CUnmannedTraderTaxRateManagerIsOwnerGuild26_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerIsOwnerGuild26_clbk CUnmannedTraderTaxRateManagerIsOwnerGuild26_user(nullptr);
        
        Info::CUnmannedTraderTaxRateManagerLoad28_ptr CUnmannedTraderTaxRateManagerLoad28_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerLoad28_clbk CUnmannedTraderTaxRateManagerLoad28_user(nullptr);
        
        Info::CUnmannedTraderTaxRateManagerLoop30_ptr CUnmannedTraderTaxRateManagerLoop30_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerLoop30_clbk CUnmannedTraderTaxRateManagerLoop30_user(nullptr);
        
        Info::CUnmannedTraderTaxRateManagerSendTaxRate32_ptr CUnmannedTraderTaxRateManagerSendTaxRate32_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerSendTaxRate32_clbk CUnmannedTraderTaxRateManagerSendTaxRate32_user(nullptr);
        
        Info::CUnmannedTraderTaxRateManagerSendTaxRatePatriarch34_ptr CUnmannedTraderTaxRateManagerSendTaxRatePatriarch34_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerSendTaxRatePatriarch34_clbk CUnmannedTraderTaxRateManagerSendTaxRatePatriarch34_user(nullptr);
        
        Info::CUnmannedTraderTaxRateManagerSetGuildMaintainMoney36_ptr CUnmannedTraderTaxRateManagerSetGuildMaintainMoney36_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerSetGuildMaintainMoney36_clbk CUnmannedTraderTaxRateManagerSetGuildMaintainMoney36_user(nullptr);
        
        Info::CUnmannedTraderTaxRateManagerSetPatriarchTaxMoney38_ptr CUnmannedTraderTaxRateManagerSetPatriarchTaxMoney38_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerSetPatriarchTaxMoney38_clbk CUnmannedTraderTaxRateManagerSetPatriarchTaxMoney38_user(nullptr);
        
        Info::CUnmannedTraderTaxRateManagerSetSuggested40_ptr CUnmannedTraderTaxRateManagerSetSuggested40_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerSetSuggested40_clbk CUnmannedTraderTaxRateManagerSetSuggested40_user(nullptr);
        
        
        Info::CUnmannedTraderTaxRateManagerdtor_CUnmannedTraderTaxRateManager44_ptr CUnmannedTraderTaxRateManagerdtor_CUnmannedTraderTaxRateManager44_next(nullptr);
        Info::CUnmannedTraderTaxRateManagerdtor_CUnmannedTraderTaxRateManager44_clbk CUnmannedTraderTaxRateManagerdtor_CUnmannedTraderTaxRateManager44_user(nullptr);
        
        
        void CUnmannedTraderTaxRateManagerctor_CUnmannedTraderTaxRateManager2_wrapper(struct CUnmannedTraderTaxRateManager* _this)
        {
           CUnmannedTraderTaxRateManagerctor_CUnmannedTraderTaxRateManager2_user(_this, CUnmannedTraderTaxRateManagerctor_CUnmannedTraderTaxRateManager2_next);
        };
        int CUnmannedTraderTaxRateManagerChangeOwner4_wrapper(struct CUnmannedTraderTaxRateManager* _this, char byRace, struct CGuild* pGuild)
        {
           return CUnmannedTraderTaxRateManagerChangeOwner4_user(_this, byRace, pGuild, CUnmannedTraderTaxRateManagerChangeOwner4_next);
        };
        bool CUnmannedTraderTaxRateManagerCheatChangeTaxRate6_wrapper(struct CUnmannedTraderTaxRateManager* _this, char byRace, unsigned int dwNewTaxRate, char* pCheaterName)
        {
           return CUnmannedTraderTaxRateManagerCheatChangeTaxRate6_user(_this, byRace, dwNewTaxRate, pCheaterName, CUnmannedTraderTaxRateManagerCheatChangeTaxRate6_next);
        };
        void CUnmannedTraderTaxRateManagerCleanUp8_wrapper(struct CUnmannedTraderTaxRateManager* _this)
        {
           CUnmannedTraderTaxRateManagerCleanUp8_user(_this, CUnmannedTraderTaxRateManagerCleanUp8_next);
        };
        void CUnmannedTraderTaxRateManagerCompleteCreate10_wrapper(struct CUnmannedTraderTaxRateManager* _this, uint16_t wInx)
        {
           CUnmannedTraderTaxRateManagerCompleteCreate10_user(_this, wInx, CUnmannedTraderTaxRateManagerCompleteCreate10_next);
        };
        void CUnmannedTraderTaxRateManagerDQSCompleteInAtradTaxMoney12_wrapper(struct CUnmannedTraderTaxRateManager* _this, char byRace, char* pdata)
        {
           CUnmannedTraderTaxRateManagerDQSCompleteInAtradTaxMoney12_user(_this, byRace, pdata, CUnmannedTraderTaxRateManagerDQSCompleteInAtradTaxMoney12_next);
        };
        void CUnmannedTraderTaxRateManagerDestroy14_wrapper()
        {
           CUnmannedTraderTaxRateManagerDestroy14_user(CUnmannedTraderTaxRateManagerDestroy14_next);
        };
        unsigned int CUnmannedTraderTaxRateManagerGetSuggestedTime16_wrapper(struct CUnmannedTraderTaxRateManager* _this, char byRace)
        {
           return CUnmannedTraderTaxRateManagerGetSuggestedTime16_user(_this, byRace, CUnmannedTraderTaxRateManagerGetSuggestedTime16_next);
        };
        unsigned int CUnmannedTraderTaxRateManagerGetTax18_wrapper(struct CUnmannedTraderTaxRateManager* _this, char byRace, unsigned int dwGuildSerial, unsigned int dwPrice)
        {
           return CUnmannedTraderTaxRateManagerGetTax18_user(_this, byRace, dwGuildSerial, dwPrice, CUnmannedTraderTaxRateManagerGetTax18_next);
        };
        float CUnmannedTraderTaxRateManagerGetTaxRate20_wrapper(struct CUnmannedTraderTaxRateManager* _this, char byRace)
        {
           return CUnmannedTraderTaxRateManagerGetTaxRate20_user(_this, byRace, CUnmannedTraderTaxRateManagerGetTaxRate20_next);
        };
        bool CUnmannedTraderTaxRateManagerInit22_wrapper(struct CUnmannedTraderTaxRateManager* _this, struct CLogFile* pkLogger)
        {
           return CUnmannedTraderTaxRateManagerInit22_user(_this, pkLogger, CUnmannedTraderTaxRateManagerInit22_next);
        };
        struct CUnmannedTraderTaxRateManager* CUnmannedTraderTaxRateManagerInstance24_wrapper()
        {
           return CUnmannedTraderTaxRateManagerInstance24_user(CUnmannedTraderTaxRateManagerInstance24_next);
        };
        bool CUnmannedTraderTaxRateManagerIsOwnerGuild26_wrapper(struct CUnmannedTraderTaxRateManager* _this, char byRace, unsigned int dwGuildSerial)
        {
           return CUnmannedTraderTaxRateManagerIsOwnerGuild26_user(_this, byRace, dwGuildSerial, CUnmannedTraderTaxRateManagerIsOwnerGuild26_next);
        };
        bool CUnmannedTraderTaxRateManagerLoad28_wrapper(struct CUnmannedTraderTaxRateManager* _this)
        {
           return CUnmannedTraderTaxRateManagerLoad28_user(_this, CUnmannedTraderTaxRateManagerLoad28_next);
        };
        void CUnmannedTraderTaxRateManagerLoop30_wrapper(struct CUnmannedTraderTaxRateManager* _this)
        {
           CUnmannedTraderTaxRateManagerLoop30_user(_this, CUnmannedTraderTaxRateManagerLoop30_next);
        };
        void CUnmannedTraderTaxRateManagerSendTaxRate32_wrapper(struct CUnmannedTraderTaxRateManager* _this, int n, char byRace)
        {
           CUnmannedTraderTaxRateManagerSendTaxRate32_user(_this, n, byRace, CUnmannedTraderTaxRateManagerSendTaxRate32_next);
        };
        void CUnmannedTraderTaxRateManagerSendTaxRatePatriarch34_wrapper(struct CUnmannedTraderTaxRateManager* _this, int n, char byRace)
        {
           CUnmannedTraderTaxRateManagerSendTaxRatePatriarch34_user(_this, n, byRace, CUnmannedTraderTaxRateManagerSendTaxRatePatriarch34_next);
        };
        void CUnmannedTraderTaxRateManagerSetGuildMaintainMoney36_wrapper(struct CUnmannedTraderTaxRateManager* _this, char byRace, unsigned int dwTax, unsigned int dwSeller)
        {
           CUnmannedTraderTaxRateManagerSetGuildMaintainMoney36_user(_this, byRace, dwTax, dwSeller, CUnmannedTraderTaxRateManagerSetGuildMaintainMoney36_next);
        };
        void CUnmannedTraderTaxRateManagerSetPatriarchTaxMoney38_wrapper(struct CUnmannedTraderTaxRateManager* _this, char byRace, unsigned int dwTax)
        {
           CUnmannedTraderTaxRateManagerSetPatriarchTaxMoney38_user(_this, byRace, dwTax, CUnmannedTraderTaxRateManagerSetPatriarchTaxMoney38_next);
        };
        void CUnmannedTraderTaxRateManagerSetSuggested40_wrapper(struct CUnmannedTraderTaxRateManager* _this, char byRace, char byMatterType, unsigned int dwMatterDst, char* wszMatterDst, unsigned int dwNext)
        {
           CUnmannedTraderTaxRateManagerSetSuggested40_user(_this, byRace, byMatterType, dwMatterDst, wszMatterDst, dwNext, CUnmannedTraderTaxRateManagerSetSuggested40_next);
        };
        
        void CUnmannedTraderTaxRateManagerdtor_CUnmannedTraderTaxRateManager44_wrapper(struct CUnmannedTraderTaxRateManager* _this)
        {
           CUnmannedTraderTaxRateManagerdtor_CUnmannedTraderTaxRateManager44_user(_this, CUnmannedTraderTaxRateManagerdtor_CUnmannedTraderTaxRateManager44_next);
        };
        
        ::std::array<hook_record, 21> CUnmannedTraderTaxRateManager_functions = 
        {
            _hook_record {
                (LPVOID)0x14038ecd0L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerctor_CUnmannedTraderTaxRateManager2_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerctor_CUnmannedTraderTaxRateManager2_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerctor_CUnmannedTraderTaxRateManager2_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderTaxRateManager::*)())&CUnmannedTraderTaxRateManager::ctor_CUnmannedTraderTaxRateManager)
            },
            _hook_record {
                (LPVOID)0x14038df40L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerChangeOwner4_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerChangeOwner4_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerChangeOwner4_wrapper),
                (LPVOID)cast_pointer_function((int(CUnmannedTraderTaxRateManager::*)(char, struct CGuild*))&CUnmannedTraderTaxRateManager::ChangeOwner)
            },
            _hook_record {
                (LPVOID)0x14038e6d0L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerCheatChangeTaxRate6_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerCheatChangeTaxRate6_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerCheatChangeTaxRate6_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderTaxRateManager::*)(char, unsigned int, char*))&CUnmannedTraderTaxRateManager::CheatChangeTaxRate)
            },
            _hook_record {
                (LPVOID)0x14038e7c0L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerCleanUp8_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerCleanUp8_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerCleanUp8_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderTaxRateManager::*)())&CUnmannedTraderTaxRateManager::CleanUp)
            },
            _hook_record {
                (LPVOID)0x14038e5e0L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerCompleteCreate10_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerCompleteCreate10_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerCompleteCreate10_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderTaxRateManager::*)(uint16_t))&CUnmannedTraderTaxRateManager::CompleteCreate)
            },
            _hook_record {
                (LPVOID)0x14038e390L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerDQSCompleteInAtradTaxMoney12_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerDQSCompleteInAtradTaxMoney12_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerDQSCompleteInAtradTaxMoney12_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderTaxRateManager::*)(char, char*))&CUnmannedTraderTaxRateManager::DQSCompleteInAtradTaxMoney)
            },
            _hook_record {
                (LPVOID)0x14038daf0L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerDestroy14_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerDestroy14_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerDestroy14_wrapper),
                (LPVOID)cast_pointer_function((void(*)())&CUnmannedTraderTaxRateManager::Destroy)
            },
            _hook_record {
                (LPVOID)0x14038e550L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerGetSuggestedTime16_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerGetSuggestedTime16_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerGetSuggestedTime16_wrapper),
                (LPVOID)cast_pointer_function((unsigned int(CUnmannedTraderTaxRateManager::*)(char))&CUnmannedTraderTaxRateManager::GetSuggestedTime)
            },
            _hook_record {
                (LPVOID)0x14038e110L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerGetTax18_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerGetTax18_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerGetTax18_wrapper),
                (LPVOID)cast_pointer_function((unsigned int(CUnmannedTraderTaxRateManager::*)(char, unsigned int, unsigned int))&CUnmannedTraderTaxRateManager::GetTax)
            },
            _hook_record {
                (LPVOID)0x14038e080L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerGetTaxRate20_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerGetTaxRate20_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerGetTaxRate20_wrapper),
                (LPVOID)cast_pointer_function((float(CUnmannedTraderTaxRateManager::*)(char))&CUnmannedTraderTaxRateManager::GetTaxRate)
            },
            _hook_record {
                (LPVOID)0x14038db70L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerInit22_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerInit22_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerInit22_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderTaxRateManager::*)(struct CLogFile*))&CUnmannedTraderTaxRateManager::Init)
            },
            _hook_record {
                (LPVOID)0x14038da30L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerInstance24_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerInstance24_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerInstance24_wrapper),
                (LPVOID)cast_pointer_function((struct CUnmannedTraderTaxRateManager*(*)())&CUnmannedTraderTaxRateManager::Instance)
            },
            _hook_record {
                (LPVOID)0x14038dfe0L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerIsOwnerGuild26_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerIsOwnerGuild26_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerIsOwnerGuild26_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderTaxRateManager::*)(char, unsigned int))&CUnmannedTraderTaxRateManager::IsOwnerGuild)
            },
            _hook_record {
                (LPVOID)0x14038dd80L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerLoad28_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerLoad28_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerLoad28_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderTaxRateManager::*)())&CUnmannedTraderTaxRateManager::Load)
            },
            _hook_record {
                (LPVOID)0x14038de90L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerLoop30_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerLoop30_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerLoop30_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderTaxRateManager::*)())&CUnmannedTraderTaxRateManager::Loop)
            },
            _hook_record {
                (LPVOID)0x14038e420L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerSendTaxRate32_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerSendTaxRate32_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerSendTaxRate32_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderTaxRateManager::*)(int, char))&CUnmannedTraderTaxRateManager::SendTaxRate)
            },
            _hook_record {
                (LPVOID)0x14038e4c0L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerSendTaxRatePatriarch34_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerSendTaxRatePatriarch34_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerSendTaxRatePatriarch34_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderTaxRateManager::*)(int, char))&CUnmannedTraderTaxRateManager::SendTaxRatePatriarch)
            },
            _hook_record {
                (LPVOID)0x14038e260L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerSetGuildMaintainMoney36_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerSetGuildMaintainMoney36_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerSetGuildMaintainMoney36_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderTaxRateManager::*)(char, unsigned int, unsigned int))&CUnmannedTraderTaxRateManager::SetGuildMaintainMoney)
            },
            _hook_record {
                (LPVOID)0x14038e300L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerSetPatriarchTaxMoney38_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerSetPatriarchTaxMoney38_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerSetPatriarchTaxMoney38_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderTaxRateManager::*)(char, unsigned int))&CUnmannedTraderTaxRateManager::SetPatriarchTaxMoney)
            },
            _hook_record {
                (LPVOID)0x14038e1b0L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerSetSuggested40_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerSetSuggested40_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerSetSuggested40_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderTaxRateManager::*)(char, char, unsigned int, char*, unsigned int))&CUnmannedTraderTaxRateManager::SetSuggested)
            },
            _hook_record {
                (LPVOID)0x14038eda0L,
                (LPVOID *)&CUnmannedTraderTaxRateManagerdtor_CUnmannedTraderTaxRateManager44_user,
                (LPVOID *)&CUnmannedTraderTaxRateManagerdtor_CUnmannedTraderTaxRateManager44_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderTaxRateManagerdtor_CUnmannedTraderTaxRateManager44_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderTaxRateManager::*)())&CUnmannedTraderTaxRateManager::dtor_CUnmannedTraderTaxRateManager)
            },
        };
    }; // end namespace Detail
END_ATF_NAMESPACE
