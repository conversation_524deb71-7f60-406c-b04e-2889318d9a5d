#include <CWeeklyGuildRankManagerDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace Detail
    {
        
        Info::CWeeklyGuildRankManagerctor_CWeeklyGuildRankManager2_ptr CWeeklyGuildRankManagerctor_CWeeklyGuildRankManager2_next(nullptr);
        Info::CWeeklyGuildRankManagerctor_CWeeklyGuildRankManager2_clbk CWeeklyGuildRankManagerctor_CWeeklyGuildRankManager2_user(nullptr);
        
        Info::CWeeklyGuildRankManagerCompleteLoadeTodayRank4_ptr CWeeklyGuildRankManagerCompleteLoadeTodayRank4_next(nullptr);
        Info::CWeeklyGuildRankManagerCompleteLoadeTodayRank4_clbk CWeeklyGuildRankManagerCompleteLoadeTodayRank4_user(nullptr);
        
        Info::CWeeklyGuildRankManagerCompleteUpdateClear6_ptr CWeeklyGuildRankManagerCompleteUpdateClear6_next(nullptr);
        Info::CWeeklyGuildRankManagerCompleteUpdateClear6_clbk CWeeklyGuildRankManagerCompleteUpdateClear6_user(nullptr);
        
        Info::CWeeklyGuildRankManagerCompleteUpdateWeeklyOwner8_ptr CWeeklyGuildRankManagerCompleteUpdateWeeklyOwner8_next(nullptr);
        Info::CWeeklyGuildRankManagerCompleteUpdateWeeklyOwner8_clbk CWeeklyGuildRankManagerCompleteUpdateWeeklyOwner8_user(nullptr);
        
        Info::CWeeklyGuildRankManagerCreatePvpPointGuildRank10_ptr CWeeklyGuildRankManagerCreatePvpPointGuildRank10_next(nullptr);
        Info::CWeeklyGuildRankManagerCreatePvpPointGuildRank10_clbk CWeeklyGuildRankManagerCreatePvpPointGuildRank10_user(nullptr);
        
        Info::CWeeklyGuildRankManagerDestroy12_ptr CWeeklyGuildRankManagerDestroy12_next(nullptr);
        Info::CWeeklyGuildRankManagerDestroy12_clbk CWeeklyGuildRankManagerDestroy12_user(nullptr);
        
        Info::CWeeklyGuildRankManagerGetPrevOwnerGuild14_ptr CWeeklyGuildRankManagerGetPrevOwnerGuild14_next(nullptr);
        Info::CWeeklyGuildRankManagerGetPrevOwnerGuild14_clbk CWeeklyGuildRankManagerGetPrevOwnerGuild14_user(nullptr);
        
        Info::CWeeklyGuildRankManagerGetPrevRankDate16_ptr CWeeklyGuildRankManagerGetPrevRankDate16_next(nullptr);
        Info::CWeeklyGuildRankManagerGetPrevRankDate16_clbk CWeeklyGuildRankManagerGetPrevRankDate16_user(nullptr);
        
        Info::CWeeklyGuildRankManagerGetTodayRankDate18_ptr CWeeklyGuildRankManagerGetTodayRankDate18_next(nullptr);
        Info::CWeeklyGuildRankManagerGetTodayRankDate18_clbk CWeeklyGuildRankManagerGetTodayRankDate18_user(nullptr);
        
        Info::CWeeklyGuildRankManagerInit20_ptr CWeeklyGuildRankManagerInit20_next(nullptr);
        Info::CWeeklyGuildRankManagerInit20_clbk CWeeklyGuildRankManagerInit20_user(nullptr);
        
        Info::CWeeklyGuildRankManagerInitNextSetOwnerDate22_ptr CWeeklyGuildRankManagerInitNextSetOwnerDate22_next(nullptr);
        Info::CWeeklyGuildRankManagerInitNextSetOwnerDate22_clbk CWeeklyGuildRankManagerInitNextSetOwnerDate22_user(nullptr);
        
        Info::CWeeklyGuildRankManagerInsertDefaultWeeklyPvpPointSumRecord24_ptr CWeeklyGuildRankManagerInsertDefaultWeeklyPvpPointSumRecord24_next(nullptr);
        Info::CWeeklyGuildRankManagerInsertDefaultWeeklyPvpPointSumRecord24_clbk CWeeklyGuildRankManagerInsertDefaultWeeklyPvpPointSumRecord24_user(nullptr);
        
        Info::CWeeklyGuildRankManagerInsertSettlementOwner26_ptr CWeeklyGuildRankManagerInsertSettlementOwner26_next(nullptr);
        Info::CWeeklyGuildRankManagerInsertSettlementOwner26_clbk CWeeklyGuildRankManagerInsertSettlementOwner26_user(nullptr);
        
        Info::CWeeklyGuildRankManagerInstance28_ptr CWeeklyGuildRankManagerInstance28_next(nullptr);
        Info::CWeeklyGuildRankManagerInstance28_clbk CWeeklyGuildRankManagerInstance28_user(nullptr);
        
        Info::CWeeklyGuildRankManagerIsEmptyRank30_ptr CWeeklyGuildRankManagerIsEmptyRank30_next(nullptr);
        Info::CWeeklyGuildRankManagerIsEmptyRank30_clbk CWeeklyGuildRankManagerIsEmptyRank30_user(nullptr);
        
        Info::CWeeklyGuildRankManagerLoad32_ptr CWeeklyGuildRankManagerLoad32_next(nullptr);
        Info::CWeeklyGuildRankManagerLoad32_clbk CWeeklyGuildRankManagerLoad32_user(nullptr);
        
        Info::CWeeklyGuildRankManagerLoadINILastRankTime34_ptr CWeeklyGuildRankManagerLoadINILastRankTime34_next(nullptr);
        Info::CWeeklyGuildRankManagerLoadINILastRankTime34_clbk CWeeklyGuildRankManagerLoadINILastRankTime34_user(nullptr);
        
        Info::CWeeklyGuildRankManagerLoadPrevOwner36_ptr CWeeklyGuildRankManagerLoadPrevOwner36_next(nullptr);
        Info::CWeeklyGuildRankManagerLoadPrevOwner36_clbk CWeeklyGuildRankManagerLoadPrevOwner36_user(nullptr);
        
        Info::CWeeklyGuildRankManagerLoadPrevTable38_ptr CWeeklyGuildRankManagerLoadPrevTable38_next(nullptr);
        Info::CWeeklyGuildRankManagerLoadPrevTable38_clbk CWeeklyGuildRankManagerLoadPrevTable38_user(nullptr);
        
        Info::CWeeklyGuildRankManagerLoop40_ptr CWeeklyGuildRankManagerLoop40_next(nullptr);
        Info::CWeeklyGuildRankManagerLoop40_clbk CWeeklyGuildRankManagerLoop40_user(nullptr);
        
        Info::CWeeklyGuildRankManagerOrderRank42_ptr CWeeklyGuildRankManagerOrderRank42_next(nullptr);
        Info::CWeeklyGuildRankManagerOrderRank42_clbk CWeeklyGuildRankManagerOrderRank42_user(nullptr);
        
        Info::CWeeklyGuildRankManagerPushDQSIncWeeklyPvpPointSum44_ptr CWeeklyGuildRankManagerPushDQSIncWeeklyPvpPointSum44_next(nullptr);
        Info::CWeeklyGuildRankManagerPushDQSIncWeeklyPvpPointSum44_clbk CWeeklyGuildRankManagerPushDQSIncWeeklyPvpPointSum44_user(nullptr);
        
        Info::CWeeklyGuildRankManagerPushSettlementOwnerDBLog46_ptr CWeeklyGuildRankManagerPushSettlementOwnerDBLog46_next(nullptr);
        Info::CWeeklyGuildRankManagerPushSettlementOwnerDBLog46_clbk CWeeklyGuildRankManagerPushSettlementOwnerDBLog46_user(nullptr);
        
        Info::CWeeklyGuildRankManagerSaveINI48_ptr CWeeklyGuildRankManagerSaveINI48_next(nullptr);
        Info::CWeeklyGuildRankManagerSaveINI48_clbk CWeeklyGuildRankManagerSaveINI48_user(nullptr);
        
        Info::CWeeklyGuildRankManagerSelectOwnerGuild50_ptr CWeeklyGuildRankManagerSelectOwnerGuild50_next(nullptr);
        Info::CWeeklyGuildRankManagerSelectOwnerGuild50_clbk CWeeklyGuildRankManagerSelectOwnerGuild50_user(nullptr);
        
        Info::CWeeklyGuildRankManagerSend52_ptr CWeeklyGuildRankManagerSend52_next(nullptr);
        Info::CWeeklyGuildRankManagerSend52_clbk CWeeklyGuildRankManagerSend52_user(nullptr);
        
        Info::CWeeklyGuildRankManagerSetNextRankDate54_ptr CWeeklyGuildRankManagerSetNextRankDate54_next(nullptr);
        Info::CWeeklyGuildRankManagerSetNextRankDate54_clbk CWeeklyGuildRankManagerSetNextRankDate54_user(nullptr);
        
        Info::CWeeklyGuildRankManagerSetSettlementAreaManageOwnerGuild56_ptr CWeeklyGuildRankManagerSetSettlementAreaManageOwnerGuild56_next(nullptr);
        Info::CWeeklyGuildRankManagerSetSettlementAreaManageOwnerGuild56_clbk CWeeklyGuildRankManagerSetSettlementAreaManageOwnerGuild56_user(nullptr);
        
        Info::CWeeklyGuildRankManagerUpdateOwnerGuild58_ptr CWeeklyGuildRankManagerUpdateOwnerGuild58_next(nullptr);
        Info::CWeeklyGuildRankManagerUpdateOwnerGuild58_clbk CWeeklyGuildRankManagerUpdateOwnerGuild58_user(nullptr);
        
        Info::CWeeklyGuildRankManagerUpdateRankDBRecord60_ptr CWeeklyGuildRankManagerUpdateRankDBRecord60_next(nullptr);
        Info::CWeeklyGuildRankManagerUpdateRankDBRecord60_clbk CWeeklyGuildRankManagerUpdateRankDBRecord60_user(nullptr);
        
        Info::CWeeklyGuildRankManagerUpdateTodayRank62_ptr CWeeklyGuildRankManagerUpdateTodayRank62_next(nullptr);
        Info::CWeeklyGuildRankManagerUpdateTodayRank62_clbk CWeeklyGuildRankManagerUpdateTodayRank62_user(nullptr);
        
        Info::CWeeklyGuildRankManagerUpdateTodayTable64_ptr CWeeklyGuildRankManagerUpdateTodayTable64_next(nullptr);
        Info::CWeeklyGuildRankManagerUpdateTodayTable64_clbk CWeeklyGuildRankManagerUpdateTodayTable64_user(nullptr);
        
        Info::CWeeklyGuildRankManagerUpdateWeeklyOwner66_ptr CWeeklyGuildRankManagerUpdateWeeklyOwner66_next(nullptr);
        Info::CWeeklyGuildRankManagerUpdateWeeklyOwner66_clbk CWeeklyGuildRankManagerUpdateWeeklyOwner66_user(nullptr);
        
        
        Info::CWeeklyGuildRankManagerdtor_CWeeklyGuildRankManager70_ptr CWeeklyGuildRankManagerdtor_CWeeklyGuildRankManager70_next(nullptr);
        Info::CWeeklyGuildRankManagerdtor_CWeeklyGuildRankManager70_clbk CWeeklyGuildRankManagerdtor_CWeeklyGuildRankManager70_user(nullptr);
        
        
        void CWeeklyGuildRankManagerctor_CWeeklyGuildRankManager2_wrapper(struct CWeeklyGuildRankManager* _this)
        {
           CWeeklyGuildRankManagerctor_CWeeklyGuildRankManager2_user(_this, CWeeklyGuildRankManagerctor_CWeeklyGuildRankManager2_next);
        };
        void CWeeklyGuildRankManagerCompleteLoadeTodayRank4_wrapper(struct CWeeklyGuildRankManager* _this, char byRet, char* pLoadData)
        {
           CWeeklyGuildRankManagerCompleteLoadeTodayRank4_user(_this, byRet, pLoadData, CWeeklyGuildRankManagerCompleteLoadeTodayRank4_next);
        };
        void CWeeklyGuildRankManagerCompleteUpdateClear6_wrapper(struct CWeeklyGuildRankManager* _this, char byRet)
        {
           CWeeklyGuildRankManagerCompleteUpdateClear6_user(_this, byRet, CWeeklyGuildRankManagerCompleteUpdateClear6_next);
        };
        void CWeeklyGuildRankManagerCompleteUpdateWeeklyOwner8_wrapper(struct CWeeklyGuildRankManager* _this, char byRet, char* pLoadData)
        {
           CWeeklyGuildRankManagerCompleteUpdateWeeklyOwner8_user(_this, byRet, pLoadData, CWeeklyGuildRankManagerCompleteUpdateWeeklyOwner8_next);
        };
        bool CWeeklyGuildRankManagerCreatePvpPointGuildRank10_wrapper(struct CWeeklyGuildRankManager* _this, char* szDate)
        {
           return CWeeklyGuildRankManagerCreatePvpPointGuildRank10_user(_this, szDate, CWeeklyGuildRankManagerCreatePvpPointGuildRank10_next);
        };
        void CWeeklyGuildRankManagerDestroy12_wrapper()
        {
           CWeeklyGuildRankManagerDestroy12_user(CWeeklyGuildRankManagerDestroy12_next);
        };
        struct CGuild* CWeeklyGuildRankManagerGetPrevOwnerGuild14_wrapper(struct CWeeklyGuildRankManager* _this, char byRace, char byNth)
        {
           return CWeeklyGuildRankManagerGetPrevOwnerGuild14_user(_this, byRace, byNth, CWeeklyGuildRankManagerGetPrevOwnerGuild14_next);
        };
        void CWeeklyGuildRankManagerGetPrevRankDate16_wrapper(struct CWeeklyGuildRankManager* _this, char* szDate, int iBuffSize)
        {
           CWeeklyGuildRankManagerGetPrevRankDate16_user(_this, szDate, iBuffSize, CWeeklyGuildRankManagerGetPrevRankDate16_next);
        };
        void CWeeklyGuildRankManagerGetTodayRankDate18_wrapper(struct CWeeklyGuildRankManager* _this, char* szDate, int iBuffSize)
        {
           CWeeklyGuildRankManagerGetTodayRankDate18_user(_this, szDate, iBuffSize, CWeeklyGuildRankManagerGetTodayRankDate18_next);
        };
        bool CWeeklyGuildRankManagerInit20_wrapper(struct CWeeklyGuildRankManager* _this)
        {
           return CWeeklyGuildRankManagerInit20_user(_this, CWeeklyGuildRankManagerInit20_next);
        };
        bool CWeeklyGuildRankManagerInitNextSetOwnerDate22_wrapper(struct CWeeklyGuildRankManager* _this)
        {
           return CWeeklyGuildRankManagerInitNextSetOwnerDate22_user(_this, CWeeklyGuildRankManagerInitNextSetOwnerDate22_next);
        };
        bool CWeeklyGuildRankManagerInsertDefaultWeeklyPvpPointSumRecord24_wrapper(struct CWeeklyGuildRankManager* _this)
        {
           return CWeeklyGuildRankManagerInsertDefaultWeeklyPvpPointSumRecord24_user(_this, CWeeklyGuildRankManagerInsertDefaultWeeklyPvpPointSumRecord24_next);
        };
        bool CWeeklyGuildRankManagerInsertSettlementOwner26_wrapper(struct CWeeklyGuildRankManager* _this, struct CRFWorldDatabase* pkWorldDB, char* pData)
        {
           return CWeeklyGuildRankManagerInsertSettlementOwner26_user(_this, pkWorldDB, pData, CWeeklyGuildRankManagerInsertSettlementOwner26_next);
        };
        struct CWeeklyGuildRankManager* CWeeklyGuildRankManagerInstance28_wrapper()
        {
           return CWeeklyGuildRankManagerInstance28_user(CWeeklyGuildRankManagerInstance28_next);
        };
        bool CWeeklyGuildRankManagerIsEmptyRank30_wrapper(struct CWeeklyGuildRankManager* _this, struct _pvppoint_guild_rank_info* pkInfo)
        {
           return CWeeklyGuildRankManagerIsEmptyRank30_user(_this, pkInfo, CWeeklyGuildRankManagerIsEmptyRank30_next);
        };
        bool CWeeklyGuildRankManagerLoad32_wrapper(struct CWeeklyGuildRankManager* _this)
        {
           return CWeeklyGuildRankManagerLoad32_user(_this, CWeeklyGuildRankManagerLoad32_next);
        };
        int64_t CWeeklyGuildRankManagerLoadINILastRankTime34_wrapper(struct CWeeklyGuildRankManager* _this)
        {
           return CWeeklyGuildRankManagerLoadINILastRankTime34_user(_this, CWeeklyGuildRankManagerLoadINILastRankTime34_next);
        };
        bool CWeeklyGuildRankManagerLoadPrevOwner36_wrapper(struct CWeeklyGuildRankManager* _this)
        {
           return CWeeklyGuildRankManagerLoadPrevOwner36_user(_this, CWeeklyGuildRankManagerLoadPrevOwner36_next);
        };
        bool CWeeklyGuildRankManagerLoadPrevTable38_wrapper(struct CWeeklyGuildRankManager* _this, char* szDate, struct _pvppoint_guild_rank_info* kInfo)
        {
           return CWeeklyGuildRankManagerLoadPrevTable38_user(_this, szDate, kInfo, CWeeklyGuildRankManagerLoadPrevTable38_next);
        };
        void CWeeklyGuildRankManagerLoop40_wrapper(struct CWeeklyGuildRankManager* _this)
        {
           CWeeklyGuildRankManagerLoop40_user(_this, CWeeklyGuildRankManagerLoop40_next);
        };
        void CWeeklyGuildRankManagerOrderRank42_wrapper(struct CWeeklyGuildRankManager* _this, struct _pvppoint_guild_rank_info* pkInfo)
        {
           CWeeklyGuildRankManagerOrderRank42_user(_this, pkInfo, CWeeklyGuildRankManagerOrderRank42_next);
        };
        bool CWeeklyGuildRankManagerPushDQSIncWeeklyPvpPointSum44_wrapper(struct CWeeklyGuildRankManager* _this, unsigned int dwGuildSerial, long double dPoint)
        {
           return CWeeklyGuildRankManagerPushDQSIncWeeklyPvpPointSum44_user(_this, dwGuildSerial, dPoint, CWeeklyGuildRankManagerPushDQSIncWeeklyPvpPointSum44_next);
        };
        void CWeeklyGuildRankManagerPushSettlementOwnerDBLog46_wrapper(struct CWeeklyGuildRankManager* _this, char* pInfo)
        {
           CWeeklyGuildRankManagerPushSettlementOwnerDBLog46_user(_this, pInfo, CWeeklyGuildRankManagerPushSettlementOwnerDBLog46_next);
        };
        bool CWeeklyGuildRankManagerSaveINI48_wrapper(struct CWeeklyGuildRankManager* _this)
        {
           return CWeeklyGuildRankManagerSaveINI48_user(_this, CWeeklyGuildRankManagerSaveINI48_next);
        };
        bool CWeeklyGuildRankManagerSelectOwnerGuild50_wrapper(struct CWeeklyGuildRankManager* _this, char* szDate, struct _weeklyguildrank_owner_info* pkInfo)
        {
           return CWeeklyGuildRankManagerSelectOwnerGuild50_user(_this, szDate, pkInfo, CWeeklyGuildRankManagerSelectOwnerGuild50_next);
        };
        void CWeeklyGuildRankManagerSend52_wrapper(struct CWeeklyGuildRankManager* _this, unsigned int dwVer, char byTabRace, struct CPlayer* pkPlayer)
        {
           CWeeklyGuildRankManagerSend52_user(_this, dwVer, byTabRace, pkPlayer, CWeeklyGuildRankManagerSend52_next);
        };
        void CWeeklyGuildRankManagerSetNextRankDate54_wrapper(struct CWeeklyGuildRankManager* _this)
        {
           CWeeklyGuildRankManagerSetNextRankDate54_user(_this, CWeeklyGuildRankManagerSetNextRankDate54_next);
        };
        void CWeeklyGuildRankManagerSetSettlementAreaManageOwnerGuild56_wrapper(struct CWeeklyGuildRankManager* _this)
        {
           CWeeklyGuildRankManagerSetSettlementAreaManageOwnerGuild56_user(_this, CWeeklyGuildRankManagerSetSettlementAreaManageOwnerGuild56_next);
        };
        bool CWeeklyGuildRankManagerUpdateOwnerGuild58_wrapper(struct CWeeklyGuildRankManager* _this, char* szDate)
        {
           return CWeeklyGuildRankManagerUpdateOwnerGuild58_user(_this, szDate, CWeeklyGuildRankManagerUpdateOwnerGuild58_next);
        };
        bool CWeeklyGuildRankManagerUpdateRankDBRecord60_wrapper(struct CWeeklyGuildRankManager* _this, char* szDate, struct _pvppoint_guild_rank_info* pkInfo)
        {
           return CWeeklyGuildRankManagerUpdateRankDBRecord60_user(_this, szDate, pkInfo, CWeeklyGuildRankManagerUpdateRankDBRecord60_next);
        };
        bool CWeeklyGuildRankManagerUpdateTodayRank62_wrapper(struct CWeeklyGuildRankManager* _this, char* pLoadData)
        {
           return CWeeklyGuildRankManagerUpdateTodayRank62_user(_this, pLoadData, CWeeklyGuildRankManagerUpdateTodayRank62_next);
        };
        int CWeeklyGuildRankManagerUpdateTodayTable64_wrapper(struct CWeeklyGuildRankManager* _this, char* szDate, struct _pvppoint_guild_rank_info* pkInfo)
        {
           return CWeeklyGuildRankManagerUpdateTodayTable64_user(_this, szDate, pkInfo, CWeeklyGuildRankManagerUpdateTodayTable64_next);
        };
        bool CWeeklyGuildRankManagerUpdateWeeklyOwner66_wrapper(struct CWeeklyGuildRankManager* _this, char* pLoadData)
        {
           return CWeeklyGuildRankManagerUpdateWeeklyOwner66_user(_this, pLoadData, CWeeklyGuildRankManagerUpdateWeeklyOwner66_next);
        };
        
        void CWeeklyGuildRankManagerdtor_CWeeklyGuildRankManager70_wrapper(struct CWeeklyGuildRankManager* _this)
        {
           CWeeklyGuildRankManagerdtor_CWeeklyGuildRankManager70_user(_this, CWeeklyGuildRankManagerdtor_CWeeklyGuildRankManager70_next);
        };
        
        ::std::array<hook_record, 34> CWeeklyGuildRankManager_functions = 
        {
            _hook_record {
                (LPVOID)0x1402cc280L,
                (LPVOID *)&CWeeklyGuildRankManagerctor_CWeeklyGuildRankManager2_user,
                (LPVOID *)&CWeeklyGuildRankManagerctor_CWeeklyGuildRankManager2_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerctor_CWeeklyGuildRankManager2_wrapper),
                (LPVOID)cast_pointer_function((void(CWeeklyGuildRankManager::*)())&CWeeklyGuildRankManager::ctor_CWeeklyGuildRankManager)
            },
            _hook_record {
                (LPVOID)0x1402cca60L,
                (LPVOID *)&CWeeklyGuildRankManagerCompleteLoadeTodayRank4_user,
                (LPVOID *)&CWeeklyGuildRankManagerCompleteLoadeTodayRank4_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerCompleteLoadeTodayRank4_wrapper),
                (LPVOID)cast_pointer_function((void(CWeeklyGuildRankManager::*)(char, char*))&CWeeklyGuildRankManager::CompleteLoadeTodayRank)
            },
            _hook_record {
                (LPVOID)0x1402ccd30L,
                (LPVOID *)&CWeeklyGuildRankManagerCompleteUpdateClear6_user,
                (LPVOID *)&CWeeklyGuildRankManagerCompleteUpdateClear6_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerCompleteUpdateClear6_wrapper),
                (LPVOID)cast_pointer_function((void(CWeeklyGuildRankManager::*)(char))&CWeeklyGuildRankManager::CompleteUpdateClear)
            },
            _hook_record {
                (LPVOID)0x1402ccc10L,
                (LPVOID *)&CWeeklyGuildRankManagerCompleteUpdateWeeklyOwner8_user,
                (LPVOID *)&CWeeklyGuildRankManagerCompleteUpdateWeeklyOwner8_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerCompleteUpdateWeeklyOwner8_wrapper),
                (LPVOID)cast_pointer_function((void(CWeeklyGuildRankManager::*)(char, char*))&CWeeklyGuildRankManager::CompleteUpdateWeeklyOwner)
            },
            _hook_record {
                (LPVOID)0x1402ce3a0L,
                (LPVOID *)&CWeeklyGuildRankManagerCreatePvpPointGuildRank10_user,
                (LPVOID *)&CWeeklyGuildRankManagerCreatePvpPointGuildRank10_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerCreatePvpPointGuildRank10_wrapper),
                (LPVOID)cast_pointer_function((bool(CWeeklyGuildRankManager::*)(char*))&CWeeklyGuildRankManager::CreatePvpPointGuildRank)
            },
            _hook_record {
                (LPVOID)0x1402cc3a0L,
                (LPVOID *)&CWeeklyGuildRankManagerDestroy12_user,
                (LPVOID *)&CWeeklyGuildRankManagerDestroy12_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerDestroy12_wrapper),
                (LPVOID)cast_pointer_function((void(*)())&CWeeklyGuildRankManager::Destroy)
            },
            _hook_record {
                (LPVOID)0x1402d78d0L,
                (LPVOID *)&CWeeklyGuildRankManagerGetPrevOwnerGuild14_user,
                (LPVOID *)&CWeeklyGuildRankManagerGetPrevOwnerGuild14_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerGetPrevOwnerGuild14_wrapper),
                (LPVOID)cast_pointer_function((struct CGuild*(CWeeklyGuildRankManager::*)(char, char))&CWeeklyGuildRankManager::GetPrevOwnerGuild)
            },
            _hook_record {
                (LPVOID)0x1402cd690L,
                (LPVOID *)&CWeeklyGuildRankManagerGetPrevRankDate16_user,
                (LPVOID *)&CWeeklyGuildRankManagerGetPrevRankDate16_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerGetPrevRankDate16_wrapper),
                (LPVOID)cast_pointer_function((void(CWeeklyGuildRankManager::*)(char*, int))&CWeeklyGuildRankManager::GetPrevRankDate)
            },
            _hook_record {
                (LPVOID)0x1402cd920L,
                (LPVOID *)&CWeeklyGuildRankManagerGetTodayRankDate18_user,
                (LPVOID *)&CWeeklyGuildRankManagerGetTodayRankDate18_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerGetTodayRankDate18_wrapper),
                (LPVOID)cast_pointer_function((void(CWeeklyGuildRankManager::*)(char*, int))&CWeeklyGuildRankManager::GetTodayRankDate)
            },
            _hook_record {
                (LPVOID)0x1402cc420L,
                (LPVOID *)&CWeeklyGuildRankManagerInit20_user,
                (LPVOID *)&CWeeklyGuildRankManagerInit20_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerInit20_wrapper),
                (LPVOID)cast_pointer_function((bool(CWeeklyGuildRankManager::*)())&CWeeklyGuildRankManager::Init)
            },
            _hook_record {
                (LPVOID)0x1402cd250L,
                (LPVOID *)&CWeeklyGuildRankManagerInitNextSetOwnerDate22_user,
                (LPVOID *)&CWeeklyGuildRankManagerInitNextSetOwnerDate22_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerInitNextSetOwnerDate22_wrapper),
                (LPVOID)cast_pointer_function((bool(CWeeklyGuildRankManager::*)())&CWeeklyGuildRankManager::InitNextSetOwnerDate)
            },
            _hook_record {
                (LPVOID)0x1402ce0e0L,
                (LPVOID *)&CWeeklyGuildRankManagerInsertDefaultWeeklyPvpPointSumRecord24_user,
                (LPVOID *)&CWeeklyGuildRankManagerInsertDefaultWeeklyPvpPointSumRecord24_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerInsertDefaultWeeklyPvpPointSumRecord24_wrapper),
                (LPVOID)cast_pointer_function((bool(CWeeklyGuildRankManager::*)())&CWeeklyGuildRankManager::InsertDefaultWeeklyPvpPointSumRecord)
            },
            _hook_record {
                (LPVOID)0x1402cce80L,
                (LPVOID *)&CWeeklyGuildRankManagerInsertSettlementOwner26_user,
                (LPVOID *)&CWeeklyGuildRankManagerInsertSettlementOwner26_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerInsertSettlementOwner26_wrapper),
                (LPVOID)cast_pointer_function((bool(CWeeklyGuildRankManager::*)(struct CRFWorldDatabase*, char*))&CWeeklyGuildRankManager::InsertSettlementOwner)
            },
            _hook_record {
                (LPVOID)0x1402cc2e0L,
                (LPVOID *)&CWeeklyGuildRankManagerInstance28_user,
                (LPVOID *)&CWeeklyGuildRankManagerInstance28_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerInstance28_wrapper),
                (LPVOID)cast_pointer_function((struct CWeeklyGuildRankManager*(*)())&CWeeklyGuildRankManager::Instance)
            },
            _hook_record {
                (LPVOID)0x1402cdb80L,
                (LPVOID *)&CWeeklyGuildRankManagerIsEmptyRank30_user,
                (LPVOID *)&CWeeklyGuildRankManagerIsEmptyRank30_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerIsEmptyRank30_wrapper),
                (LPVOID)cast_pointer_function((bool(CWeeklyGuildRankManager::*)(struct _pvppoint_guild_rank_info*))&CWeeklyGuildRankManager::IsEmptyRank)
            },
            _hook_record {
                (LPVOID)0x1402cc490L,
                (LPVOID *)&CWeeklyGuildRankManagerLoad32_user,
                (LPVOID *)&CWeeklyGuildRankManagerLoad32_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerLoad32_wrapper),
                (LPVOID)cast_pointer_function((bool(CWeeklyGuildRankManager::*)())&CWeeklyGuildRankManager::Load)
            },
            _hook_record {
                (LPVOID)0x1402cd0f0L,
                (LPVOID *)&CWeeklyGuildRankManagerLoadINILastRankTime34_user,
                (LPVOID *)&CWeeklyGuildRankManagerLoadINILastRankTime34_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerLoadINILastRankTime34_wrapper),
                (LPVOID)cast_pointer_function((int64_t(CWeeklyGuildRankManager::*)())&CWeeklyGuildRankManager::LoadINILastRankTime)
            },
            _hook_record {
                (LPVOID)0x1402cd4f0L,
                (LPVOID *)&CWeeklyGuildRankManagerLoadPrevOwner36_user,
                (LPVOID *)&CWeeklyGuildRankManagerLoadPrevOwner36_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerLoadPrevOwner36_wrapper),
                (LPVOID)cast_pointer_function((bool(CWeeklyGuildRankManager::*)())&CWeeklyGuildRankManager::LoadPrevOwner)
            },
            _hook_record {
                (LPVOID)0x1402ce140L,
                (LPVOID *)&CWeeklyGuildRankManagerLoadPrevTable38_user,
                (LPVOID *)&CWeeklyGuildRankManagerLoadPrevTable38_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerLoadPrevTable38_wrapper),
                (LPVOID)cast_pointer_function((bool(CWeeklyGuildRankManager::*)(char*, struct _pvppoint_guild_rank_info*))&CWeeklyGuildRankManager::LoadPrevTable)
            },
            _hook_record {
                (LPVOID)0x1402cc700L,
                (LPVOID *)&CWeeklyGuildRankManagerLoop40_user,
                (LPVOID *)&CWeeklyGuildRankManagerLoop40_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerLoop40_wrapper),
                (LPVOID)cast_pointer_function((void(CWeeklyGuildRankManager::*)())&CWeeklyGuildRankManager::Loop)
            },
            _hook_record {
                (LPVOID)0x1402cdc00L,
                (LPVOID *)&CWeeklyGuildRankManagerOrderRank42_user,
                (LPVOID *)&CWeeklyGuildRankManagerOrderRank42_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerOrderRank42_wrapper),
                (LPVOID)cast_pointer_function((void(CWeeklyGuildRankManager::*)(struct _pvppoint_guild_rank_info*))&CWeeklyGuildRankManager::OrderRank)
            },
            _hook_record {
                (LPVOID)0x1402ccda0L,
                (LPVOID *)&CWeeklyGuildRankManagerPushDQSIncWeeklyPvpPointSum44_user,
                (LPVOID *)&CWeeklyGuildRankManagerPushDQSIncWeeklyPvpPointSum44_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerPushDQSIncWeeklyPvpPointSum44_wrapper),
                (LPVOID)cast_pointer_function((bool(CWeeklyGuildRankManager::*)(unsigned int, long double))&CWeeklyGuildRankManager::PushDQSIncWeeklyPvpPointSum)
            },
            _hook_record {
                (LPVOID)0x1402cde90L,
                (LPVOID *)&CWeeklyGuildRankManagerPushSettlementOwnerDBLog46_user,
                (LPVOID *)&CWeeklyGuildRankManagerPushSettlementOwnerDBLog46_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerPushSettlementOwnerDBLog46_wrapper),
                (LPVOID)cast_pointer_function((void(CWeeklyGuildRankManager::*)(char*))&CWeeklyGuildRankManager::PushSettlementOwnerDBLog)
            },
            _hook_record {
                (LPVOID)0x1402cd9d0L,
                (LPVOID *)&CWeeklyGuildRankManagerSaveINI48_user,
                (LPVOID *)&CWeeklyGuildRankManagerSaveINI48_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerSaveINI48_wrapper),
                (LPVOID)cast_pointer_function((bool(CWeeklyGuildRankManager::*)())&CWeeklyGuildRankManager::SaveINI)
            },
            _hook_record {
                (LPVOID)0x1402ce690L,
                (LPVOID *)&CWeeklyGuildRankManagerSelectOwnerGuild50_user,
                (LPVOID *)&CWeeklyGuildRankManagerSelectOwnerGuild50_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerSelectOwnerGuild50_wrapper),
                (LPVOID)cast_pointer_function((bool(CWeeklyGuildRankManager::*)(char*, struct _weeklyguildrank_owner_info*))&CWeeklyGuildRankManager::SelectOwnerGuild)
            },
            _hook_record {
                (LPVOID)0x1402cd030L,
                (LPVOID *)&CWeeklyGuildRankManagerSend52_user,
                (LPVOID *)&CWeeklyGuildRankManagerSend52_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerSend52_wrapper),
                (LPVOID)cast_pointer_function((void(CWeeklyGuildRankManager::*)(unsigned int, char, struct CPlayer*))&CWeeklyGuildRankManager::Send)
            },
            _hook_record {
                (LPVOID)0x1402cd3f0L,
                (LPVOID *)&CWeeklyGuildRankManagerSetNextRankDate54_user,
                (LPVOID *)&CWeeklyGuildRankManagerSetNextRankDate54_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerSetNextRankDate54_wrapper),
                (LPVOID)cast_pointer_function((void(CWeeklyGuildRankManager::*)())&CWeeklyGuildRankManager::SetNextRankDate)
            },
            _hook_record {
                (LPVOID)0x1402cdd60L,
                (LPVOID *)&CWeeklyGuildRankManagerSetSettlementAreaManageOwnerGuild56_user,
                (LPVOID *)&CWeeklyGuildRankManagerSetSettlementAreaManageOwnerGuild56_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerSetSettlementAreaManageOwnerGuild56_wrapper),
                (LPVOID)cast_pointer_function((void(CWeeklyGuildRankManager::*)())&CWeeklyGuildRankManager::SetSettlementAreaManageOwnerGuild)
            },
            _hook_record {
                (LPVOID)0x1402ce600L,
                (LPVOID *)&CWeeklyGuildRankManagerUpdateOwnerGuild58_user,
                (LPVOID *)&CWeeklyGuildRankManagerUpdateOwnerGuild58_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerUpdateOwnerGuild58_wrapper),
                (LPVOID)cast_pointer_function((bool(CWeeklyGuildRankManager::*)(char*))&CWeeklyGuildRankManager::UpdateOwnerGuild)
            },
            _hook_record {
                (LPVOID)0x1402ce4e0L,
                (LPVOID *)&CWeeklyGuildRankManagerUpdateRankDBRecord60_user,
                (LPVOID *)&CWeeklyGuildRankManagerUpdateRankDBRecord60_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerUpdateRankDBRecord60_wrapper),
                (LPVOID)cast_pointer_function((bool(CWeeklyGuildRankManager::*)(char*, struct _pvppoint_guild_rank_info*))&CWeeklyGuildRankManager::UpdateRankDBRecord)
            },
            _hook_record {
                (LPVOID)0x1402cc7f0L,
                (LPVOID *)&CWeeklyGuildRankManagerUpdateTodayRank62_user,
                (LPVOID *)&CWeeklyGuildRankManagerUpdateTodayRank62_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerUpdateTodayRank62_wrapper),
                (LPVOID)cast_pointer_function((bool(CWeeklyGuildRankManager::*)(char*))&CWeeklyGuildRankManager::UpdateTodayRank)
            },
            _hook_record {
                (LPVOID)0x1402ce250L,
                (LPVOID *)&CWeeklyGuildRankManagerUpdateTodayTable64_user,
                (LPVOID *)&CWeeklyGuildRankManagerUpdateTodayTable64_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerUpdateTodayTable64_wrapper),
                (LPVOID)cast_pointer_function((int(CWeeklyGuildRankManager::*)(char*, struct _pvppoint_guild_rank_info*))&CWeeklyGuildRankManager::UpdateTodayTable)
            },
            _hook_record {
                (LPVOID)0x1402cc950L,
                (LPVOID *)&CWeeklyGuildRankManagerUpdateWeeklyOwner66_user,
                (LPVOID *)&CWeeklyGuildRankManagerUpdateWeeklyOwner66_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerUpdateWeeklyOwner66_wrapper),
                (LPVOID)cast_pointer_function((bool(CWeeklyGuildRankManager::*)(char*))&CWeeklyGuildRankManager::UpdateWeeklyOwner)
            },
            _hook_record {
                (LPVOID)0x1402cfac0L,
                (LPVOID *)&CWeeklyGuildRankManagerdtor_CWeeklyGuildRankManager70_user,
                (LPVOID *)&CWeeklyGuildRankManagerdtor_CWeeklyGuildRankManager70_next,
                (LPVOID)cast_pointer_function(CWeeklyGuildRankManagerdtor_CWeeklyGuildRankManager70_wrapper),
                (LPVOID)cast_pointer_function((void(CWeeklyGuildRankManager::*)())&CWeeklyGuildRankManager::dtor_CWeeklyGuildRankManager)
            },
        };
    }; // end namespace Detail
END_ATF_NAMESPACE
