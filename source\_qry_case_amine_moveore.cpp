#include <_qry_case_amine_moveore.hpp>


START_ATF_NAMESPACE
    _qry_case_amine_moveore::_qry_case_amine_moveore()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_amine_moveore*);
        (org_ptr(0x1402d4350L))(this);
    };
    void _qry_case_amine_moveore::ctor__qry_case_amine_moveore()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_amine_moveore*);
        (org_ptr(0x1402d4350L))(this);
    };
    int _qry_case_amine_moveore::size()
    {
        using org_ptr = int (WINAPIV*)(struct _qry_case_amine_moveore*);
        return (org_ptr(0x1402d4370L))(this);
    };
END_ATF_NAMESPACE
