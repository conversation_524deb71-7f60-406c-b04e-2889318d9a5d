// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _worlddb_itemcombineex_info
    {
        struct __itemcombineex_key
        {
            int lK;
            unsigned int dwD;
            unsigned int dwU;
        };
        bool bIsResult;
        unsigned int dwCheckKey;
        char byDlgType;
        unsigned int dwDalant;
        char byItemListCount;
        char byItemSelectCount;
        __itemcombineex_key invenKey[24];
        unsigned int dwResultEffectType;
        unsigned int dwResultEffectMsgCode;
    };
END_ATF_NAMESPACE
