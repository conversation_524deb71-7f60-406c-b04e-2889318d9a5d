// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_base_fld.hpp>


START_ATF_NAMESPACE
    struct  _WorldSchedule_fld : _base_fld
    {
        int m_nHour;
        int m_nMin;
        int m_nEventCode;
        int m_nEventInfo1;
        int m_nEventInfo2;
        int m_nEventInfo3;
    };
END_ATF_NAMESPACE
