#include <_throw_skill_result_one_zocl.hpp>


START_ATF_NAMESPACE
    _throw_skill_result_one_zocl::_throw_skill_result_one_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _throw_skill_result_one_zocl*);
        (org_ptr(0x1400efd70L))(this);
    };
    void _throw_skill_result_one_zocl::ctor__throw_skill_result_one_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _throw_skill_result_one_zocl*);
        (org_ptr(0x1400efd70L))(this);
    };
    
END_ATF_NAMESPACE
