#include <_npc_quest_list_result_zocl.hpp>


START_ATF_NAMESPACE
    _npc_quest_list_result_zocl::_npc_quest_list_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _npc_quest_list_result_zocl*);
        (org_ptr(0x1400efd20L))(this);
    };
    void _npc_quest_list_result_zocl::ctor__npc_quest_list_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _npc_quest_list_result_zocl*);
        (org_ptr(0x1400efd20L))(this);
    };
END_ATF_NAMESPACE
