// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _unit_pack_fill_request_clzo
    {
        struct  __list
        {
            char bySpareIndex;
            unsigned __int16 wBulletIndex;
        };
        char bySlotIndex;
        char byFillNum;
        int bUseNPCLinkIntem;
        __list List[8];
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
