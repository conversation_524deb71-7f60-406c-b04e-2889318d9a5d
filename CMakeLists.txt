cmake_minimum_required(VERSION 3.16)

# Project configuration
project(NexusPro 
    VERSION 1.0.0
    DESCRIPTION "NexusPro - Header-only C++ Project"
    LANGUAGES CXX
)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Platform-specific settings
if(WIN32)
    set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
    add_definitions(-DWIN32_LEAN_AND_MEAN)
    add_definitions(-DNOMINMAX)
endif()

# Compiler-specific settings
if(MSVC)
    # Enable parallel compilation
    add_compile_options(/MP)
    # Disable specific warnings for large codebases
    add_compile_options(/wd4996 /wd4267 /wd4244 /wd4018)
    # Set warning level
    add_compile_options(/W3)
else()
    # GCC/Clang settings
    add_compile_options(-Wall -Wextra -Wno-unused-parameter)
endif()

# Function to recursively collect all .hpp files
function(collect_hpp_files directory result_var)
    file(GLOB_RECURSE hpp_files "${directory}/*.hpp")
    set(${result_var} ${hpp_files} PARENT_SCOPE)
endfunction()

# Collect all .hpp files from ATF directory
collect_hpp_files("${CMAKE_CURRENT_SOURCE_DIR}/ATF" ATF_HPP_FILES)

# Collect all .cpp files from source directory
file(GLOB_RECURSE SOURCE_CPP_FILES "${CMAKE_CURRENT_SOURCE_DIR}/source/*.cpp")

# Create a custom target that treats .hpp files as sources
# This allows IDEs to recognize them as part of the project
add_custom_target(ATF_Headers
    SOURCES ${ATF_HPP_FILES}
)

# Create the main ATF library with implementations
add_library(ATF STATIC ${SOURCE_CPP_FILES})

# Create a header-only interface library for external use
add_library(ATF_Interface INTERFACE)

# Add include directories for the main library
target_include_directories(ATF PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/ATF>
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/ATF/common>
    $<INSTALL_INTERFACE:include>
)

# Add include directories for the interface library
target_include_directories(ATF_Interface INTERFACE
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/ATF>
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/ATF/common>
    $<INSTALL_INTERFACE:include>
)

# Link system libraries (Windows specific)
if(WIN32)
    target_link_libraries(ATF PUBLIC
        kernel32
        user32
        gdi32
        winspool
        comdlg32
        advapi32
        shell32
        ole32
        oleaut32
        uuid
        odbc32
        odbccp32
        ws2_32
    )

    target_link_libraries(ATF_Interface INTERFACE
        kernel32
        user32
        gdi32
        winspool
        comdlg32
        advapi32
        shell32
        ole32
        oleaut32
        uuid
        odbc32
        odbccp32
        ws2_32
    )
endif()

# Create a simple test executable to verify compilation
add_executable(test_compilation
    test_main.cpp
)

target_link_libraries(test_compilation PRIVATE ATF)

# Set target properties
set_target_properties(test_compilation PROPERTIES
    OUTPUT_NAME "nexuspro_test"
    DEBUG_POSTFIX "_d"
)

# Installation configuration
include(GNUInstallDirs)

# Install the header files
install(DIRECTORY ATF/
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/ATF
    FILES_MATCHING PATTERN "*.hpp"
)

# Install both libraries
install(TARGETS ATF ATF_Interface
    EXPORT ATFTargets
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    INCLUDES DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
)

# Create and install the config files
install(EXPORT ATFTargets
    FILE ATFTargets.cmake
    NAMESPACE ATF::
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/ATF
)

# Create config file
include(CMakePackageConfigHelpers)

configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/cmake/ATFConfig.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/ATFConfig.cmake"
    INSTALL_DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/ATF
)

write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/ATFConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY SameMajorVersion
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/ATFConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/ATFConfigVersion.cmake"
    DESTINATION ${CMAKE_INSTALL_LIBDIR}/cmake/ATF
)

# Development tools and utilities
option(BUILD_TOOLS "Build development tools" OFF)

if(BUILD_TOOLS)
    # Add a tool to analyze header dependencies
    add_executable(header_analyzer
        tools/header_analyzer.cpp
    )
    
    target_link_libraries(header_analyzer PRIVATE ATF)
endif()

# Documentation
option(BUILD_DOCS "Build documentation" OFF)

if(BUILD_DOCS)
    find_package(Doxygen)
    if(DOXYGEN_FOUND)
        set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/docs/Doxyfile.in)
        set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
        
        configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
        
        add_custom_target(docs ALL
            COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating API documentation with Doxygen"
            VERBATIM
        )
    endif()
endif()

# Print configuration summary
message(STATUS "=== NexusPro Configuration Summary ===")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "Platform: ${CMAKE_SYSTEM_NAME}")
list(LENGTH ATF_HPP_FILES ATF_HEADER_COUNT)
list(LENGTH SOURCE_CPP_FILES SOURCE_FILE_COUNT)
message(STATUS "ATF headers: ${ATF_HEADER_COUNT}")
message(STATUS "Source files: ${SOURCE_FILE_COUNT}")
math(EXPR COVERAGE_PERCENT "${SOURCE_FILE_COUNT} * 100 / ${ATF_HEADER_COUNT}")
message(STATUS "Implementation coverage: ~${COVERAGE_PERCENT}%")
message(STATUS "Build tools: ${BUILD_TOOLS}")
message(STATUS "Build docs: ${BUILD_DOCS}")
message(STATUS "=====================================")
