# NexusPro - Game Reverse Engineering Framework

A comprehensive C++ project combining reverse-engineered headers with implementation wrappers, designed for game modding and analysis. Fully compatible with **Visual Studio 2022**.

## Overview

This project contains a complete reverse-engineered game framework with two main components:

- **ATF Directory**: 11,551 header files (.hpp) with game structure definitions
- **Source Directory**: 2,104 implementation files (.cpp) with function wrappers

The build system is designed to:

- Combine headers and implementations into a complete game modding framework
- Provide proper IDE integration for navigation and editing
- Support both Windows (Visual Studio) and Unix-like systems (Linux, macOS)
- Create both static libraries and header-only interfaces for integration

## Project Structure

```
NexusPro/
├── ATF/                    # 11,551 header files (.hpp)
│   ├── common/            # Core framework headers
│   │   ├── ATFCore.hpp    # Main framework core
│   │   ├── ATFRegister.hpp # Component registration
│   │   └── common.h       # Common definitions
│   ├── CPlayer.hpp        # Player class (1,371 lines)
│   ├── CGuild.hpp         # Guild system
│   └── *.hpp              # Game structures and classes
├── source/                # 2,104 implementation files (.cpp)
│   ├── CPlayer.cpp        # Player implementation (5,140 lines)
│   ├── CGuild.cpp         # Guild implementation
│   └── *.cpp              # Function wrappers and hooks
├── cmake/                 # CMake configuration files
├── tools/                 # Development utilities
├── build_vs.bat          # Windows build script
├── build_unix.sh         # Unix build script
├── test_main.cpp         # Test application
├── CMakeLists.txt        # CMake configuration
└── README.md             # This file
```

## Features

- **Header-Only Library**: All functionality is contained in header files
- **Cross-Platform**: Supports Windows, Linux, and macOS
- **IDE Integration**: All headers are recognized as source files in IDEs
- **Automatic Discovery**: CMake automatically finds all `.hpp` files
- **Test Application**: Includes a test program to verify compilation
- **Package Support**: Can be installed and used as a CMake package

## Building the Project

### Windows (Visual Studio)

1. **Prerequisites**:
   - Visual Studio 2017 or later
   - CMake 3.16 or later

2. **Build**:
   ```cmd
# Run the build script
   build_vs.bat
```

   Or manually:
   ```cmd
mkdir build
   cd build
   cmake .. -G "Visual Studio 17 2022" -A x64
   cmake --build . --config Release
```

### Linux/macOS (Unix)

1. **Prerequisites**:
   - GCC 7+ or Clang 6+
   - CMake 3.16 or later
   - Make

2. **Build**:
   ```bash
# Run the build script
   ./build_unix.sh
```

   Or manually:
   ```bash
mkdir build
   cd build
   cmake .. -DCMAKE_BUILD_TYPE=Release
   make -j$(nproc)
```

## Usage

### As a Header-Only Library

Include the main header in your project:

```cpp
#include "ATF/ATF.hpp"

int main() {
    // Use ATF functionality
    return 0;
}
```

### CMake Integration

If you've installed the library:

```cmake
find_package(ATF REQUIRED)
target_link_libraries(your_target PRIVATE ATF::ATF)
```

### Direct Integration

Add the project as a subdirectory:

```cmake
add_subdirectory(path/to/NexusPro)
target_link_libraries(your_target PRIVATE ATF)
```

## Configuration Options

The CMake build system supports several options:

- `BUILD_TOOLS`: Build development tools (default: OFF)
- `BUILD_DOCS`: Build documentation with Doxygen (default: OFF)

Example:
```bash
cmake .. -DBUILD_TOOLS=ON -DBUILD_DOCS=ON
```

## Development

### Adding New Headers

1. Place new `.hpp` files in the `ATF` directory or subdirectories
2. CMake will automatically discover them on the next build
3. No need to modify build files

### IDE Setup

#### Visual Studio
- Open the generated `.sln` file in the `build` directory
- All headers will be visible in the Solution Explorer

#### Visual Studio Code
- Install the C/C++ extension
- Open the project folder
- CMake Tools extension will provide IntelliSense

#### CLion
- Open the project folder
- CLion will automatically detect the CMake configuration

## Testing

The project includes a test application (`test_main.cpp`) that verifies:

- Basic compilation of all headers
- Header inclusion functionality
- ATF-specific features
- Performance characteristics

Run the test:
```bash
# After building
./build/nexuspro_test

# Interactive mode
./build/nexuspro_test --interactive
```

## Troubleshooting

### Common Issues

1. **CMake not found**:
   - Install CMake from https://cmake.org/
   - Add CMake to your system PATH

2. **Compiler not found**:
   - Windows: Install Visual Studio with C++ workload
   - Linux: `sudo apt-get install build-essential`
   - macOS: `xcode-select --install`

3. **Header compilation errors**:
   - Check that all dependencies are available
   - Verify Windows SDK is installed (Windows)
   - Check compiler version compatibility

### Build Logs

Build logs are available in:
- Windows: `build/CMakeFiles/CMakeOutput.log`
- Unix: `build/CMakeFiles/CMakeOutput.log`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add your headers to the appropriate directory
4. Test compilation
5. Submit a pull request

## License

This project structure and build system are provided as-is. Check individual header files for their specific licensing terms.

## Technical Details

- **C++ Standard**: C++17
- **CMake Version**: 3.16+
- **Header Count**: 2000+ files
- **Platform Support**: Windows, Linux, macOS
- **Architecture**: x64 (primary), x86 (compatible)

## Performance

The build system is optimized for:
- Fast incremental builds
- Parallel compilation
- Minimal memory usage during compilation
- IDE responsiveness with large header counts
message(STATUS "Implementation coverage: ~${COVERAGE_PERCENT}%")
message(STATUS "Build tools: ${BUILD_TOOLS}")
message(STATUS "Build docs: ${BUILD_DOCS}")
message(STATUS "=====================================")
