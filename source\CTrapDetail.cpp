#include <CTrapDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace Detail
    {
        Info::CTrapAttack2_ptr CTrapAttack2_next(nullptr);
        Info::CTrapAttack2_clbk CTrapAttack2_user(nullptr);
        
        Info::CTrapAttackableHeight4_ptr CTrapAttackableHeight4_next(nullptr);
        Info::CTrapAttackableHeight4_clbk CTrapAttackableHeight4_user(nullptr);
        
        
        Info::CTrapctor_CTrap6_ptr CTrapctor_CTrap6_next(nullptr);
        Info::CTrapctor_CTrap6_clbk CTrapctor_CTrap6_user(nullptr);
        
        Info::CTrapCheckTranspar8_ptr CTrapCheckTranspar8_next(nullptr);
        Info::CTrapCheckTranspar8_clbk CTrapCheckTranspar8_user(nullptr);
        
        Info::CTrapCreate10_ptr CTrapCreate10_next(nullptr);
        Info::CTrapCreate10_clbk CTrapCreate10_user(nullptr);
        
        Info::CTrapDestroy12_ptr CTrapDestroy12_next(nullptr);
        Info::CTrapDestroy12_clbk CTrapDestroy12_user(nullptr);
        
        Info::CTrapGetAttackDP14_ptr CTrapGetAttackDP14_next(nullptr);
        Info::CTrapGetAttackDP14_clbk CTrapGetAttackDP14_user(nullptr);
        
        Info::CTrapGetAttackRange16_ptr CTrapGetAttackRange16_next(nullptr);
        Info::CTrapGetAttackRange16_clbk CTrapGetAttackRange16_user(nullptr);
        
        Info::CTrapGetDefFC18_ptr CTrapGetDefFC18_next(nullptr);
        Info::CTrapGetDefFC18_clbk CTrapGetDefFC18_user(nullptr);
        
        Info::CTrapGetDefFacing20_ptr CTrapGetDefFacing20_next(nullptr);
        Info::CTrapGetDefFacing20_clbk CTrapGetDefFacing20_user(nullptr);
        
        Info::CTrapGetDefGap22_ptr CTrapGetDefGap22_next(nullptr);
        Info::CTrapGetDefGap22_clbk CTrapGetDefGap22_user(nullptr);
        
        Info::CTrapGetDefSkill24_ptr CTrapGetDefSkill24_next(nullptr);
        Info::CTrapGetDefSkill24_clbk CTrapGetDefSkill24_user(nullptr);
        
        Info::CTrapGetFireTol26_ptr CTrapGetFireTol26_next(nullptr);
        Info::CTrapGetFireTol26_clbk CTrapGetFireTol26_user(nullptr);
        
        Info::CTrapGetGenAttackProb28_ptr CTrapGetGenAttackProb28_next(nullptr);
        Info::CTrapGetGenAttackProb28_clbk CTrapGetGenAttackProb28_user(nullptr);
        
        Info::CTrapGetHP30_ptr CTrapGetHP30_next(nullptr);
        Info::CTrapGetHP30_clbk CTrapGetHP30_user(nullptr);
        
        Info::CTrapGetLevel32_ptr CTrapGetLevel32_next(nullptr);
        Info::CTrapGetLevel32_clbk CTrapGetLevel32_user(nullptr);
        
        Info::CTrapGetMaxHP34_ptr CTrapGetMaxHP34_next(nullptr);
        Info::CTrapGetMaxHP34_clbk CTrapGetMaxHP34_user(nullptr);
        
        Info::CTrapGetNewSerial36_ptr CTrapGetNewSerial36_next(nullptr);
        Info::CTrapGetNewSerial36_clbk CTrapGetNewSerial36_user(nullptr);
        
        Info::CTrapGetObjName38_ptr CTrapGetObjName38_next(nullptr);
        Info::CTrapGetObjName38_clbk CTrapGetObjName38_user(nullptr);
        
        Info::CTrapGetObjRace40_ptr CTrapGetObjRace40_next(nullptr);
        Info::CTrapGetObjRace40_clbk CTrapGetObjRace40_user(nullptr);
        
        Info::CTrapGetSoilTol42_ptr CTrapGetSoilTol42_next(nullptr);
        Info::CTrapGetSoilTol42_clbk CTrapGetSoilTol42_user(nullptr);
        
        Info::CTrapGetWaterTol44_ptr CTrapGetWaterTol44_next(nullptr);
        Info::CTrapGetWaterTol44_clbk CTrapGetWaterTol44_user(nullptr);
        
        Info::CTrapGetWeaponAdjust46_ptr CTrapGetWeaponAdjust46_next(nullptr);
        Info::CTrapGetWeaponAdjust46_clbk CTrapGetWeaponAdjust46_user(nullptr);
        
        Info::CTrapGetWeaponClass48_ptr CTrapGetWeaponClass48_next(nullptr);
        Info::CTrapGetWeaponClass48_clbk CTrapGetWeaponClass48_user(nullptr);
        
        Info::CTrapGetWidth50_ptr CTrapGetWidth50_next(nullptr);
        Info::CTrapGetWidth50_clbk CTrapGetWidth50_user(nullptr);
        
        Info::CTrapGetWindTol52_ptr CTrapGetWindTol52_next(nullptr);
        Info::CTrapGetWindTol52_clbk CTrapGetWindTol52_user(nullptr);
        
        Info::CTrapInit54_ptr CTrapInit54_next(nullptr);
        Info::CTrapInit54_clbk CTrapInit54_user(nullptr);
        
        Info::CTrapIsBeAttackedAble56_ptr CTrapIsBeAttackedAble56_next(nullptr);
        Info::CTrapIsBeAttackedAble56_clbk CTrapIsBeAttackedAble56_user(nullptr);
        
        Info::CTrapIsHaveEmpty58_ptr CTrapIsHaveEmpty58_next(nullptr);
        Info::CTrapIsHaveEmpty58_clbk CTrapIsHaveEmpty58_user(nullptr);
        
        Info::CTrapIsInTown60_ptr CTrapIsInTown60_next(nullptr);
        Info::CTrapIsInTown60_clbk CTrapIsInTown60_user(nullptr);
        
        Info::CTrapLoop62_ptr CTrapLoop62_next(nullptr);
        Info::CTrapLoop62_clbk CTrapLoop62_user(nullptr);
        
        Info::CTrapMasterNetClose64_ptr CTrapMasterNetClose64_next(nullptr);
        Info::CTrapMasterNetClose64_clbk CTrapMasterNetClose64_user(nullptr);
        
        Info::CTrapMasterReStart66_ptr CTrapMasterReStart66_next(nullptr);
        Info::CTrapMasterReStart66_clbk CTrapMasterReStart66_user(nullptr);
        
        Info::CTrapOutOfSec68_ptr CTrapOutOfSec68_next(nullptr);
        Info::CTrapOutOfSec68_clbk CTrapOutOfSec68_user(nullptr);
        
        Info::CTrapRecvKillMessage70_ptr CTrapRecvKillMessage70_next(nullptr);
        Info::CTrapRecvKillMessage70_clbk CTrapRecvKillMessage70_user(nullptr);
        
        Info::CTrapSearchNearEnemy72_ptr CTrapSearchNearEnemy72_next(nullptr);
        Info::CTrapSearchNearEnemy72_clbk CTrapSearchNearEnemy72_user(nullptr);
        
        Info::CTrapSendMsg_AlterTranspar74_ptr CTrapSendMsg_AlterTranspar74_next(nullptr);
        Info::CTrapSendMsg_AlterTranspar74_clbk CTrapSendMsg_AlterTranspar74_user(nullptr);
        
        Info::CTrapSendMsg_Attack76_ptr CTrapSendMsg_Attack76_next(nullptr);
        Info::CTrapSendMsg_Attack76_clbk CTrapSendMsg_Attack76_user(nullptr);
        
        Info::CTrapSendMsg_Create78_ptr CTrapSendMsg_Create78_next(nullptr);
        Info::CTrapSendMsg_Create78_clbk CTrapSendMsg_Create78_user(nullptr);
        
        Info::CTrapSendMsg_Destroy80_ptr CTrapSendMsg_Destroy80_next(nullptr);
        Info::CTrapSendMsg_Destroy80_clbk CTrapSendMsg_Destroy80_user(nullptr);
        
        Info::CTrapSendMsg_FixPosition82_ptr CTrapSendMsg_FixPosition82_next(nullptr);
        Info::CTrapSendMsg_FixPosition82_clbk CTrapSendMsg_FixPosition82_user(nullptr);
        
        Info::CTrapSendMsg_TrapCompleteInform84_ptr CTrapSendMsg_TrapCompleteInform84_next(nullptr);
        Info::CTrapSendMsg_TrapCompleteInform84_clbk CTrapSendMsg_TrapCompleteInform84_user(nullptr);
        
        Info::CTrapSetDamage86_ptr CTrapSetDamage86_next(nullptr);
        Info::CTrapSetDamage86_clbk CTrapSetDamage86_user(nullptr);
        
        
        Info::CTrapdtor_CTrap92_ptr CTrapdtor_CTrap92_next(nullptr);
        Info::CTrapdtor_CTrap92_clbk CTrapdtor_CTrap92_user(nullptr);
        
        void CTrapAttack2_wrapper(struct CTrap* _this, struct CCharacter* pTarget)
        {
           CTrapAttack2_user(_this, pTarget, CTrapAttack2_next);
        };
        int CTrapAttackableHeight4_wrapper(struct CTrap* _this)
        {
           return CTrapAttackableHeight4_user(_this, CTrapAttackableHeight4_next);
        };
        
        void CTrapctor_CTrap6_wrapper(struct CTrap* _this)
        {
           CTrapctor_CTrap6_user(_this, CTrapctor_CTrap6_next);
        };
        void CTrapCheckTranspar8_wrapper(struct CTrap* _this)
        {
           CTrapCheckTranspar8_user(_this, CTrapCheckTranspar8_next);
        };
        bool CTrapCreate10_wrapper(struct CTrap* _this, struct _trap_create_setdata* pData)
        {
           return CTrapCreate10_user(_this, pData, CTrapCreate10_next);
        };
        bool CTrapDestroy12_wrapper(struct CTrap* _this, char byDesType)
        {
           return CTrapDestroy12_user(_this, byDesType, CTrapDestroy12_next);
        };
        int CTrapGetAttackDP14_wrapper(struct CTrap* _this)
        {
           return CTrapGetAttackDP14_user(_this, CTrapGetAttackDP14_next);
        };
        float CTrapGetAttackRange16_wrapper(struct CTrap* _this)
        {
           return CTrapGetAttackRange16_user(_this, CTrapGetAttackRange16_next);
        };
        int CTrapGetDefFC18_wrapper(struct CTrap* _this, int nAttactPart, struct CCharacter* pAttChar, int* pnConvertPart)
        {
           return CTrapGetDefFC18_user(_this, nAttactPart, pAttChar, pnConvertPart, CTrapGetDefFC18_next);
        };
        float CTrapGetDefFacing20_wrapper(struct CTrap* _this, int nPart)
        {
           return CTrapGetDefFacing20_user(_this, nPart, CTrapGetDefFacing20_next);
        };
        float CTrapGetDefGap22_wrapper(struct CTrap* _this, int nPart)
        {
           return CTrapGetDefGap22_user(_this, nPart, CTrapGetDefGap22_next);
        };
        int CTrapGetDefSkill24_wrapper(struct CTrap* _this, bool bBackAttack)
        {
           return CTrapGetDefSkill24_user(_this, bBackAttack, CTrapGetDefSkill24_next);
        };
        int CTrapGetFireTol26_wrapper(struct CTrap* _this)
        {
           return CTrapGetFireTol26_user(_this, CTrapGetFireTol26_next);
        };
        int CTrapGetGenAttackProb28_wrapper(struct CTrap* _this, struct CCharacter* pDst, int nPart, bool bBackAttack)
        {
           return CTrapGetGenAttackProb28_user(_this, pDst, nPart, bBackAttack, CTrapGetGenAttackProb28_next);
        };
        int CTrapGetHP30_wrapper(struct CTrap* _this)
        {
           return CTrapGetHP30_user(_this, CTrapGetHP30_next);
        };
        int CTrapGetLevel32_wrapper(struct CTrap* _this)
        {
           return CTrapGetLevel32_user(_this, CTrapGetLevel32_next);
        };
        int CTrapGetMaxHP34_wrapper(struct CTrap* _this)
        {
           return CTrapGetMaxHP34_user(_this, CTrapGetMaxHP34_next);
        };
        unsigned int CTrapGetNewSerial36_wrapper()
        {
           return CTrapGetNewSerial36_user(CTrapGetNewSerial36_next);
        };
        char* CTrapGetObjName38_wrapper(struct CTrap* _this)
        {
           return CTrapGetObjName38_user(_this, CTrapGetObjName38_next);
        };
        int CTrapGetObjRace40_wrapper(struct CTrap* _this)
        {
           return CTrapGetObjRace40_user(_this, CTrapGetObjRace40_next);
        };
        int CTrapGetSoilTol42_wrapper(struct CTrap* _this)
        {
           return CTrapGetSoilTol42_user(_this, CTrapGetSoilTol42_next);
        };
        int CTrapGetWaterTol44_wrapper(struct CTrap* _this)
        {
           return CTrapGetWaterTol44_user(_this, CTrapGetWaterTol44_next);
        };
        float CTrapGetWeaponAdjust46_wrapper(struct CTrap* _this)
        {
           return CTrapGetWeaponAdjust46_user(_this, CTrapGetWeaponAdjust46_next);
        };
        int CTrapGetWeaponClass48_wrapper(struct CTrap* _this)
        {
           return CTrapGetWeaponClass48_user(_this, CTrapGetWeaponClass48_next);
        };
        float CTrapGetWidth50_wrapper(struct CTrap* _this)
        {
           return CTrapGetWidth50_user(_this, CTrapGetWidth50_next);
        };
        int CTrapGetWindTol52_wrapper(struct CTrap* _this)
        {
           return CTrapGetWindTol52_user(_this, CTrapGetWindTol52_next);
        };
        bool CTrapInit54_wrapper(struct CTrap* _this, struct _object_id* pID)
        {
           return CTrapInit54_user(_this, pID, CTrapInit54_next);
        };
        bool CTrapIsBeAttackedAble56_wrapper(struct CTrap* _this, bool bFirst)
        {
           return CTrapIsBeAttackedAble56_user(_this, bFirst, CTrapIsBeAttackedAble56_next);
        };
        bool CTrapIsHaveEmpty58_wrapper()
        {
           return CTrapIsHaveEmpty58_user(CTrapIsHaveEmpty58_next);
        };
        bool CTrapIsInTown60_wrapper(struct CTrap* _this)
        {
           return CTrapIsInTown60_user(_this, CTrapIsInTown60_next);
        };
        void CTrapLoop62_wrapper(struct CTrap* _this)
        {
           CTrapLoop62_user(_this, CTrapLoop62_next);
        };
        void CTrapMasterNetClose64_wrapper(struct CTrap* _this, long double dPvPPoint)
        {
           CTrapMasterNetClose64_user(_this, dPvPPoint, CTrapMasterNetClose64_next);
        };
        void CTrapMasterReStart66_wrapper(struct CTrap* _this, struct CPlayer* pMaster)
        {
           CTrapMasterReStart66_user(_this, pMaster, CTrapMasterReStart66_next);
        };
        void CTrapOutOfSec68_wrapper(struct CTrap* _this)
        {
           CTrapOutOfSec68_user(_this, CTrapOutOfSec68_next);
        };
        void CTrapRecvKillMessage70_wrapper(struct CTrap* _this, struct CCharacter* pDier)
        {
           CTrapRecvKillMessage70_user(_this, pDier, CTrapRecvKillMessage70_next);
        };
        struct CCharacter* CTrapSearchNearEnemy72_wrapper(struct CTrap* _this)
        {
           return CTrapSearchNearEnemy72_user(_this, CTrapSearchNearEnemy72_next);
        };
        void CTrapSendMsg_AlterTranspar74_wrapper(struct CTrap* _this, bool bTranspar)
        {
           CTrapSendMsg_AlterTranspar74_user(_this, bTranspar, CTrapSendMsg_AlterTranspar74_next);
        };
        void CTrapSendMsg_Attack76_wrapper(struct CTrap* _this, struct CAttack* pAt)
        {
           CTrapSendMsg_Attack76_user(_this, pAt, CTrapSendMsg_Attack76_next);
        };
        void CTrapSendMsg_Create78_wrapper(struct CTrap* _this)
        {
           CTrapSendMsg_Create78_user(_this, CTrapSendMsg_Create78_next);
        };
        void CTrapSendMsg_Destroy80_wrapper(struct CTrap* _this, char byDesType)
        {
           CTrapSendMsg_Destroy80_user(_this, byDesType, CTrapSendMsg_Destroy80_next);
        };
        void CTrapSendMsg_FixPosition82_wrapper(struct CTrap* _this, int n)
        {
           CTrapSendMsg_FixPosition82_user(_this, n, CTrapSendMsg_FixPosition82_next);
        };
        void CTrapSendMsg_TrapCompleteInform84_wrapper(struct CTrap* _this)
        {
           CTrapSendMsg_TrapCompleteInform84_user(_this, CTrapSendMsg_TrapCompleteInform84_next);
        };
        int CTrapSetDamage86_wrapper(struct CTrap* _this, int nDam, struct CCharacter* pDst, int nDstLv, bool bCrt, int nAttackType, unsigned int dwAttackSerial, bool bJadeReturn)
        {
           return CTrapSetDamage86_user(_this, nDam, pDst, nDstLv, bCrt, nAttackType, dwAttackSerial, bJadeReturn, CTrapSetDamage86_next);
        };
        
        void CTrapdtor_CTrap92_wrapper(struct CTrap* _this)
        {
           CTrapdtor_CTrap92_user(_this, CTrapdtor_CTrap92_next);
        };
        
        ::std::array<hook_record, 44> CTrap_functions = 
        {
            _hook_record {
                (LPVOID)0x14013ebf0L,
                (LPVOID *)&CTrapAttack2_user,
                (LPVOID *)&CTrapAttack2_next,
                (LPVOID)cast_pointer_function(CTrapAttack2_wrapper),
                (LPVOID)cast_pointer_function((void(CTrap::*)(struct CCharacter*))&CTrap::Attack)
            },
            _hook_record {
                (LPVOID)0x1401412d0L,
                (LPVOID *)&CTrapAttackableHeight4_user,
                (LPVOID *)&CTrapAttackableHeight4_next,
                (LPVOID)cast_pointer_function(CTrapAttackableHeight4_wrapper),
                (LPVOID)cast_pointer_function((int(CTrap::*)())&CTrap::AttackableHeight)
            },
            _hook_record {
                (LPVOID)0x14013e7a0L,
                (LPVOID *)&CTrapctor_CTrap6_user,
                (LPVOID *)&CTrapctor_CTrap6_next,
                (LPVOID)cast_pointer_function(CTrapctor_CTrap6_wrapper),
                (LPVOID)cast_pointer_function((void(CTrap::*)())&CTrap::ctor_CTrap)
            },
            _hook_record {
                (LPVOID)0x14013ef00L,
                (LPVOID *)&CTrapCheckTranspar8_user,
                (LPVOID *)&CTrapCheckTranspar8_next,
                (LPVOID)cast_pointer_function(CTrapCheckTranspar8_wrapper),
                (LPVOID)cast_pointer_function((void(CTrap::*)())&CTrap::CheckTranspar)
            },
            _hook_record {
                (LPVOID)0x14013e960L,
                (LPVOID *)&CTrapCreate10_user,
                (LPVOID *)&CTrapCreate10_next,
                (LPVOID)cast_pointer_function(CTrapCreate10_wrapper),
                (LPVOID)cast_pointer_function((bool(CTrap::*)(struct _trap_create_setdata*))&CTrap::Create)
            },
            _hook_record {
                (LPVOID)0x14013eb10L,
                (LPVOID *)&CTrapDestroy12_user,
                (LPVOID *)&CTrapDestroy12_next,
                (LPVOID)cast_pointer_function(CTrapDestroy12_wrapper),
                (LPVOID)cast_pointer_function((bool(CTrap::*)(char))&CTrap::Destroy)
            },
            _hook_record {
                (LPVOID)0x1401412b0L,
                (LPVOID *)&CTrapGetAttackDP14_user,
                (LPVOID *)&CTrapGetAttackDP14_next,
                (LPVOID)cast_pointer_function(CTrapGetAttackDP14_wrapper),
                (LPVOID)cast_pointer_function((int(CTrap::*)())&CTrap::GetAttackDP)
            },
            _hook_record {
                (LPVOID)0x140141070L,
                (LPVOID *)&CTrapGetAttackRange16_user,
                (LPVOID *)&CTrapGetAttackRange16_next,
                (LPVOID)cast_pointer_function(CTrapGetAttackRange16_wrapper),
                (LPVOID)cast_pointer_function((float(CTrap::*)())&CTrap::GetAttackRange)
            },
            _hook_record {
                (LPVOID)0x1401410c0L,
                (LPVOID *)&CTrapGetDefFC18_user,
                (LPVOID *)&CTrapGetDefFC18_next,
                (LPVOID)cast_pointer_function(CTrapGetDefFC18_wrapper),
                (LPVOID)cast_pointer_function((int(CTrap::*)(int, struct CCharacter*, int*))&CTrap::GetDefFC)
            },
            _hook_record {
                (LPVOID)0x140141210L,
                (LPVOID *)&CTrapGetDefFacing20_user,
                (LPVOID *)&CTrapGetDefFacing20_next,
                (LPVOID)cast_pointer_function(CTrapGetDefFacing20_wrapper),
                (LPVOID)cast_pointer_function((float(CTrap::*)(int))&CTrap::GetDefFacing)
            },
            _hook_record {
                (LPVOID)0x1401411c0L,
                (LPVOID *)&CTrapGetDefGap22_user,
                (LPVOID *)&CTrapGetDefGap22_next,
                (LPVOID)cast_pointer_function(CTrapGetDefGap22_wrapper),
                (LPVOID)cast_pointer_function((float(CTrap::*)(int))&CTrap::GetDefGap)
            },
            _hook_record {
                (LPVOID)0x140141110L,
                (LPVOID *)&CTrapGetDefSkill24_user,
                (LPVOID *)&CTrapGetDefSkill24_next,
                (LPVOID)cast_pointer_function(CTrapGetDefSkill24_wrapper),
                (LPVOID)cast_pointer_function((int(CTrap::*)(bool))&CTrap::GetDefSkill)
            },
            _hook_record {
                (LPVOID)0x140141140L,
                (LPVOID *)&CTrapGetFireTol26_user,
                (LPVOID *)&CTrapGetFireTol26_next,
                (LPVOID)cast_pointer_function(CTrapGetFireTol26_wrapper),
                (LPVOID)cast_pointer_function((int(CTrap::*)())&CTrap::GetFireTol)
            },
            _hook_record {
                (LPVOID)0x14013f290L,
                (LPVOID *)&CTrapGetGenAttackProb28_user,
                (LPVOID *)&CTrapGetGenAttackProb28_next,
                (LPVOID)cast_pointer_function(CTrapGetGenAttackProb28_wrapper),
                (LPVOID)cast_pointer_function((int(CTrap::*)(struct CCharacter*, int, bool))&CTrap::GetGenAttackProb)
            },
            _hook_record {
                (LPVOID)0x140140ff0L,
                (LPVOID *)&CTrapGetHP30_user,
                (LPVOID *)&CTrapGetHP30_next,
                (LPVOID)cast_pointer_function(CTrapGetHP30_wrapper),
                (LPVOID)cast_pointer_function((int(CTrap::*)())&CTrap::GetHP)
            },
            _hook_record {
                (LPVOID)0x1401410f0L,
                (LPVOID *)&CTrapGetLevel32_user,
                (LPVOID *)&CTrapGetLevel32_next,
                (LPVOID)cast_pointer_function(CTrapGetLevel32_wrapper),
                (LPVOID)cast_pointer_function((int(CTrap::*)())&CTrap::GetLevel)
            },
            _hook_record {
                (LPVOID)0x140141010L,
                (LPVOID *)&CTrapGetMaxHP34_user,
                (LPVOID *)&CTrapGetMaxHP34_next,
                (LPVOID)cast_pointer_function(CTrapGetMaxHP34_wrapper),
                (LPVOID)cast_pointer_function((int(CTrap::*)())&CTrap::GetMaxHP)
            },
            _hook_record {
                (LPVOID)0x1401413a0L,
                (LPVOID *)&CTrapGetNewSerial36_user,
                (LPVOID *)&CTrapGetNewSerial36_next,
                (LPVOID)cast_pointer_function(CTrapGetNewSerial36_wrapper),
                (LPVOID)cast_pointer_function((unsigned int(*)())&CTrap::GetNewSerial)
            },
            _hook_record {
                (LPVOID)0x14013f3d0L,
                (LPVOID *)&CTrapGetObjName38_user,
                (LPVOID *)&CTrapGetObjName38_next,
                (LPVOID)cast_pointer_function(CTrapGetObjName38_wrapper),
                (LPVOID)cast_pointer_function((char*(CTrap::*)())&CTrap::GetObjName)
            },
            _hook_record {
                (LPVOID)0x140141310L,
                (LPVOID *)&CTrapGetObjRace40_user,
                (LPVOID *)&CTrapGetObjRace40_next,
                (LPVOID)cast_pointer_function(CTrapGetObjRace40_wrapper),
                (LPVOID)cast_pointer_function((int(CTrap::*)())&CTrap::GetObjRace)
            },
            _hook_record {
                (LPVOID)0x140141180L,
                (LPVOID *)&CTrapGetSoilTol42_user,
                (LPVOID *)&CTrapGetSoilTol42_next,
                (LPVOID)cast_pointer_function(CTrapGetSoilTol42_wrapper),
                (LPVOID)cast_pointer_function((int(CTrap::*)())&CTrap::GetSoilTol)
            },
            _hook_record {
                (LPVOID)0x140141160L,
                (LPVOID *)&CTrapGetWaterTol44_user,
                (LPVOID *)&CTrapGetWaterTol44_next,
                (LPVOID)cast_pointer_function(CTrapGetWaterTol44_wrapper),
                (LPVOID)cast_pointer_function((int(CTrap::*)())&CTrap::GetWaterTol)
            },
            _hook_record {
                (LPVOID)0x140141260L,
                (LPVOID *)&CTrapGetWeaponAdjust46_user,
                (LPVOID *)&CTrapGetWeaponAdjust46_next,
                (LPVOID)cast_pointer_function(CTrapGetWeaponAdjust46_wrapper),
                (LPVOID)cast_pointer_function((float(CTrap::*)())&CTrap::GetWeaponAdjust)
            },
            _hook_record {
                (LPVOID)0x1401412a0L,
                (LPVOID *)&CTrapGetWeaponClass48_user,
                (LPVOID *)&CTrapGetWeaponClass48_next,
                (LPVOID)cast_pointer_function(CTrapGetWeaponClass48_wrapper),
                (LPVOID)cast_pointer_function((int(CTrap::*)())&CTrap::GetWeaponClass)
            },
            _hook_record {
                (LPVOID)0x140141030L,
                (LPVOID *)&CTrapGetWidth50_user,
                (LPVOID *)&CTrapGetWidth50_next,
                (LPVOID)cast_pointer_function(CTrapGetWidth50_wrapper),
                (LPVOID)cast_pointer_function((float(CTrap::*)())&CTrap::GetWidth)
            },
            _hook_record {
                (LPVOID)0x1401411a0L,
                (LPVOID *)&CTrapGetWindTol52_user,
                (LPVOID *)&CTrapGetWindTol52_next,
                (LPVOID)cast_pointer_function(CTrapGetWindTol52_wrapper),
                (LPVOID)cast_pointer_function((int(CTrap::*)())&CTrap::GetWindTol)
            },
            _hook_record {
                (LPVOID)0x14013e850L,
                (LPVOID *)&CTrapInit54_user,
                (LPVOID *)&CTrapInit54_next,
                (LPVOID)cast_pointer_function(CTrapInit54_wrapper),
                (LPVOID)cast_pointer_function((bool(CTrap::*)(struct _object_id*))&CTrap::Init)
            },
            _hook_record {
                (LPVOID)0x1401412e0L,
                (LPVOID *)&CTrapIsBeAttackedAble56_user,
                (LPVOID *)&CTrapIsBeAttackedAble56_next,
                (LPVOID)cast_pointer_function(CTrapIsBeAttackedAble56_wrapper),
                (LPVOID)cast_pointer_function((bool(CTrap::*)(bool))&CTrap::IsBeAttackedAble)
            },
            _hook_record {
                (LPVOID)0x140140440L,
                (LPVOID *)&CTrapIsHaveEmpty58_user,
                (LPVOID *)&CTrapIsHaveEmpty58_next,
                (LPVOID)cast_pointer_function(CTrapIsHaveEmpty58_wrapper),
                (LPVOID)cast_pointer_function((bool(*)())&CTrap::IsHaveEmpty)
            },
            _hook_record {
                (LPVOID)0x140141300L,
                (LPVOID *)&CTrapIsInTown60_user,
                (LPVOID *)&CTrapIsInTown60_next,
                (LPVOID)cast_pointer_function(CTrapIsInTown60_wrapper),
                (LPVOID)cast_pointer_function((bool(CTrap::*)())&CTrap::IsInTown)
            },
            _hook_record {
                (LPVOID)0x14013f040L,
                (LPVOID *)&CTrapLoop62_user,
                (LPVOID *)&CTrapLoop62_next,
                (LPVOID)cast_pointer_function(CTrapLoop62_wrapper),
                (LPVOID)cast_pointer_function((void(CTrap::*)())&CTrap::Loop)
            },
            _hook_record {
                (LPVOID)0x14013ef90L,
                (LPVOID *)&CTrapMasterNetClose64_user,
                (LPVOID *)&CTrapMasterNetClose64_next,
                (LPVOID)cast_pointer_function(CTrapMasterNetClose64_wrapper),
                (LPVOID)cast_pointer_function((void(CTrap::*)(long double))&CTrap::MasterNetClose)
            },
            _hook_record {
                (LPVOID)0x14013efd0L,
                (LPVOID *)&CTrapMasterReStart66_user,
                (LPVOID *)&CTrapMasterReStart66_next,
                (LPVOID)cast_pointer_function(CTrapMasterReStart66_wrapper),
                (LPVOID)cast_pointer_function((void(CTrap::*)(struct CPlayer*))&CTrap::MasterReStart)
            },
            _hook_record {
                (LPVOID)0x140140fa0L,
                (LPVOID *)&CTrapOutOfSec68_user,
                (LPVOID *)&CTrapOutOfSec68_next,
                (LPVOID)cast_pointer_function(CTrapOutOfSec68_wrapper),
                (LPVOID)cast_pointer_function((void(CTrap::*)())&CTrap::OutOfSec)
            },
            _hook_record {
                (LPVOID)0x14013f4f0L,
                (LPVOID *)&CTrapRecvKillMessage70_user,
                (LPVOID *)&CTrapRecvKillMessage70_next,
                (LPVOID)cast_pointer_function(CTrapRecvKillMessage70_wrapper),
                (LPVOID)cast_pointer_function((void(CTrap::*)(struct CCharacter*))&CTrap::RecvKillMessage)
            },
            _hook_record {
                (LPVOID)0x14013fe20L,
                (LPVOID *)&CTrapSearchNearEnemy72_user,
                (LPVOID *)&CTrapSearchNearEnemy72_next,
                (LPVOID)cast_pointer_function(CTrapSearchNearEnemy72_wrapper),
                (LPVOID)cast_pointer_function((struct CCharacter*(CTrap::*)())&CTrap::SearchNearEnemy)
            },
            _hook_record {
                (LPVOID)0x14013fd90L,
                (LPVOID *)&CTrapSendMsg_AlterTranspar74_user,
                (LPVOID *)&CTrapSendMsg_AlterTranspar74_next,
                (LPVOID)cast_pointer_function(CTrapSendMsg_AlterTranspar74_wrapper),
                (LPVOID)cast_pointer_function((void(CTrap::*)(bool))&CTrap::SendMsg_AlterTranspar)
            },
            _hook_record {
                (LPVOID)0x14013fa00L,
                (LPVOID *)&CTrapSendMsg_Attack76_user,
                (LPVOID *)&CTrapSendMsg_Attack76_next,
                (LPVOID)cast_pointer_function(CTrapSendMsg_Attack76_wrapper),
                (LPVOID)cast_pointer_function((void(CTrap::*)(struct CAttack*))&CTrap::SendMsg_Attack)
            },
            _hook_record {
                (LPVOID)0x14013f7f0L,
                (LPVOID *)&CTrapSendMsg_Create78_user,
                (LPVOID *)&CTrapSendMsg_Create78_next,
                (LPVOID)cast_pointer_function(CTrapSendMsg_Create78_wrapper),
                (LPVOID)cast_pointer_function((void(CTrap::*)())&CTrap::SendMsg_Create)
            },
            _hook_record {
                (LPVOID)0x14013f900L,
                (LPVOID *)&CTrapSendMsg_Destroy80_user,
                (LPVOID *)&CTrapSendMsg_Destroy80_next,
                (LPVOID)cast_pointer_function(CTrapSendMsg_Destroy80_wrapper),
                (LPVOID)cast_pointer_function((void(CTrap::*)(char))&CTrap::SendMsg_Destroy)
            },
            _hook_record {
                (LPVOID)0x14013fb80L,
                (LPVOID *)&CTrapSendMsg_FixPosition82_user,
                (LPVOID *)&CTrapSendMsg_FixPosition82_next,
                (LPVOID)cast_pointer_function(CTrapSendMsg_FixPosition82_wrapper),
                (LPVOID)cast_pointer_function((void(CTrap::*)(int))&CTrap::SendMsg_FixPosition)
            },
            _hook_record {
                (LPVOID)0x14013fd10L,
                (LPVOID *)&CTrapSendMsg_TrapCompleteInform84_user,
                (LPVOID *)&CTrapSendMsg_TrapCompleteInform84_next,
                (LPVOID)cast_pointer_function(CTrapSendMsg_TrapCompleteInform84_wrapper),
                (LPVOID)cast_pointer_function((void(CTrap::*)())&CTrap::SendMsg_TrapCompleteInform)
            },
            _hook_record {
                (LPVOID)0x14013f1c0L,
                (LPVOID *)&CTrapSetDamage86_user,
                (LPVOID *)&CTrapSetDamage86_next,
                (LPVOID)cast_pointer_function(CTrapSetDamage86_wrapper),
                (LPVOID)cast_pointer_function((int(CTrap::*)(int, struct CCharacter*, int, bool, int, unsigned int, bool))&CTrap::SetDamage)
            },
            _hook_record {
                (LPVOID)0x14013e800L,
                (LPVOID *)&CTrapdtor_CTrap92_user,
                (LPVOID *)&CTrapdtor_CTrap92_next,
                (LPVOID)cast_pointer_function(CTrapdtor_CTrap92_wrapper),
                (LPVOID)cast_pointer_function((void(CTrap::*)())&CTrap::dtor_CTrap)
            },
        };
    }; // end namespace Detail
END_ATF_NAMESPACE
