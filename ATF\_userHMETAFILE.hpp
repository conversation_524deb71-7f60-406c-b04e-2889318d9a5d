// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _userHMETAFILE
    {
        union __MIDL_IWinTypes_0004
        {
            int hInproc;
            _BYTE_BLOB *hRemote;
            __int64 hInproc64;
        };
        int fContext;
        __MIDL_IWinTypes_0004 u;
    };
END_ATF_NAMESPACE
