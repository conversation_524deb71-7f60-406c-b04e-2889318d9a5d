#include <GUILD_BATTLE__CGuildBattleScheduleDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Detail
        {
            
            Info::GUILD_BATTLE__CGuildBattleSchedulector_CGuildBattleSchedule2_ptr GUILD_BATTLE__CGuildBattleSchedulector_CGuildBattleSchedule2_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleSchedulector_CGuildBattleSchedule2_clbk GUILD_BATTLE__CGuildBattleSchedulector_CGuildBattleSchedule2_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleCheck4_ptr GUILD_BATTLE__CGuildBattleScheduleCheck4_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleCheck4_clbk GUILD_BATTLE__CGuildBattleScheduleCheck4_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleClear6_ptr GUILD_BATTLE__CGuildBattleScheduleClear6_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleClear6_clbk GUILD_BATTLE__CGuildBattleScheduleClear6_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleClearDB8_ptr GUILD_BATTLE__CGuildBattleScheduleClearDB8_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleClearDB8_clbk GUILD_BATTLE__CGuildBattleScheduleClearDB8_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleGetBattleTime10_ptr GUILD_BATTLE__CGuildBattleScheduleGetBattleTime10_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleGetBattleTime10_clbk GUILD_BATTLE__CGuildBattleScheduleGetBattleTime10_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleGetBattleTurm12_ptr GUILD_BATTLE__CGuildBattleScheduleGetBattleTurm12_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleGetBattleTurm12_clbk GUILD_BATTLE__CGuildBattleScheduleGetBattleTurm12_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleGetLeftTime14_ptr GUILD_BATTLE__CGuildBattleScheduleGetLeftTime14_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleGetLeftTime14_clbk GUILD_BATTLE__CGuildBattleScheduleGetLeftTime14_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleGetRealStartTime16_ptr GUILD_BATTLE__CGuildBattleScheduleGetRealStartTime16_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleGetRealStartTime16_clbk GUILD_BATTLE__CGuildBattleScheduleGetRealStartTime16_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleGetSID18_ptr GUILD_BATTLE__CGuildBattleScheduleGetSID18_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleGetSID18_clbk GUILD_BATTLE__CGuildBattleScheduleGetSID18_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleGetState20_ptr GUILD_BATTLE__CGuildBattleScheduleGetState20_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleGetState20_clbk GUILD_BATTLE__CGuildBattleScheduleGetState20_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleGetTime22_ptr GUILD_BATTLE__CGuildBattleScheduleGetTime22_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleGetTime22_clbk GUILD_BATTLE__CGuildBattleScheduleGetTime22_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleIsDone24_ptr GUILD_BATTLE__CGuildBattleScheduleIsDone24_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleIsDone24_clbk GUILD_BATTLE__CGuildBattleScheduleIsDone24_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleIsEmpty26_ptr GUILD_BATTLE__CGuildBattleScheduleIsEmpty26_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleIsEmpty26_clbk GUILD_BATTLE__CGuildBattleScheduleIsEmpty26_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleIsProc28_ptr GUILD_BATTLE__CGuildBattleScheduleIsProc28_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleIsProc28_clbk GUILD_BATTLE__CGuildBattleScheduleIsProc28_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleIsWait30_ptr GUILD_BATTLE__CGuildBattleScheduleIsWait30_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleIsWait30_clbk GUILD_BATTLE__CGuildBattleScheduleIsWait30_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleLoad32_ptr GUILD_BATTLE__CGuildBattleScheduleLoad32_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleLoad32_clbk GUILD_BATTLE__CGuildBattleScheduleLoad32_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleProcess34_ptr GUILD_BATTLE__CGuildBattleScheduleProcess34_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleProcess34_clbk GUILD_BATTLE__CGuildBattleScheduleProcess34_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleSet36_ptr GUILD_BATTLE__CGuildBattleScheduleSet36_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleSet36_clbk GUILD_BATTLE__CGuildBattleScheduleSet36_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleSetProcState38_ptr GUILD_BATTLE__CGuildBattleScheduleSetProcState38_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleSetProcState38_clbk GUILD_BATTLE__CGuildBattleScheduleSetProcState38_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleSetStateList40_ptr GUILD_BATTLE__CGuildBattleScheduleSetStateList40_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleSetStateList40_clbk GUILD_BATTLE__CGuildBattleScheduleSetStateList40_user(nullptr);
            
            
            Info::GUILD_BATTLE__CGuildBattleScheduledtor_CGuildBattleSchedule46_ptr GUILD_BATTLE__CGuildBattleScheduledtor_CGuildBattleSchedule46_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduledtor_CGuildBattleSchedule46_clbk GUILD_BATTLE__CGuildBattleScheduledtor_CGuildBattleSchedule46_user(nullptr);
            
            
            void GUILD_BATTLE__CGuildBattleSchedulector_CGuildBattleSchedule2_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this, unsigned int dwScheduleID)
            {
               GUILD_BATTLE__CGuildBattleSchedulector_CGuildBattleSchedule2_user(_this, dwScheduleID, GUILD_BATTLE__CGuildBattleSchedulector_CGuildBattleSchedule2_next);
            };
            int GUILD_BATTLE__CGuildBattleScheduleCheck4_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this)
            {
               return GUILD_BATTLE__CGuildBattleScheduleCheck4_user(_this, GUILD_BATTLE__CGuildBattleScheduleCheck4_next);
            };
            void GUILD_BATTLE__CGuildBattleScheduleClear6_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this)
            {
               GUILD_BATTLE__CGuildBattleScheduleClear6_user(_this, GUILD_BATTLE__CGuildBattleScheduleClear6_next);
            };
            bool GUILD_BATTLE__CGuildBattleScheduleClearDB8_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this)
            {
               return GUILD_BATTLE__CGuildBattleScheduleClearDB8_user(_this, GUILD_BATTLE__CGuildBattleScheduleClearDB8_next);
            };
            struct ATL::CTimeSpan* GUILD_BATTLE__CGuildBattleScheduleGetBattleTime10_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this, struct ATL::CTimeSpan* result)
            {
               return GUILD_BATTLE__CGuildBattleScheduleGetBattleTime10_user(_this, result, GUILD_BATTLE__CGuildBattleScheduleGetBattleTime10_next);
            };
            int GUILD_BATTLE__CGuildBattleScheduleGetBattleTurm12_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this)
            {
               return GUILD_BATTLE__CGuildBattleScheduleGetBattleTurm12_user(_this, GUILD_BATTLE__CGuildBattleScheduleGetBattleTurm12_next);
            };
            bool GUILD_BATTLE__CGuildBattleScheduleGetLeftTime14_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this, char* byHour, char* byMin, char* bySec)
            {
               return GUILD_BATTLE__CGuildBattleScheduleGetLeftTime14_user(_this, byHour, byMin, bySec, GUILD_BATTLE__CGuildBattleScheduleGetLeftTime14_next);
            };
            int64_t GUILD_BATTLE__CGuildBattleScheduleGetRealStartTime16_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this)
            {
               return GUILD_BATTLE__CGuildBattleScheduleGetRealStartTime16_user(_this, GUILD_BATTLE__CGuildBattleScheduleGetRealStartTime16_next);
            };
            unsigned int GUILD_BATTLE__CGuildBattleScheduleGetSID18_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this)
            {
               return GUILD_BATTLE__CGuildBattleScheduleGetSID18_user(_this, GUILD_BATTLE__CGuildBattleScheduleGetSID18_next);
            };
            int GUILD_BATTLE__CGuildBattleScheduleGetState20_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this)
            {
               return GUILD_BATTLE__CGuildBattleScheduleGetState20_user(_this, GUILD_BATTLE__CGuildBattleScheduleGetState20_next);
            };
            struct ATL::CTime* GUILD_BATTLE__CGuildBattleScheduleGetTime22_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this, struct ATL::CTime* result)
            {
               return GUILD_BATTLE__CGuildBattleScheduleGetTime22_user(_this, result, GUILD_BATTLE__CGuildBattleScheduleGetTime22_next);
            };
            bool GUILD_BATTLE__CGuildBattleScheduleIsDone24_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this)
            {
               return GUILD_BATTLE__CGuildBattleScheduleIsDone24_user(_this, GUILD_BATTLE__CGuildBattleScheduleIsDone24_next);
            };
            bool GUILD_BATTLE__CGuildBattleScheduleIsEmpty26_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this)
            {
               return GUILD_BATTLE__CGuildBattleScheduleIsEmpty26_user(_this, GUILD_BATTLE__CGuildBattleScheduleIsEmpty26_next);
            };
            bool GUILD_BATTLE__CGuildBattleScheduleIsProc28_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this)
            {
               return GUILD_BATTLE__CGuildBattleScheduleIsProc28_user(_this, GUILD_BATTLE__CGuildBattleScheduleIsProc28_next);
            };
            bool GUILD_BATTLE__CGuildBattleScheduleIsWait30_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this)
            {
               return GUILD_BATTLE__CGuildBattleScheduleIsWait30_user(_this, GUILD_BATTLE__CGuildBattleScheduleIsWait30_next);
            };
            bool GUILD_BATTLE__CGuildBattleScheduleLoad32_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this, bool bToday, unsigned int dwScheduleID, char ucState, int64_t tTime, uint16_t wTumeMin)
            {
               return GUILD_BATTLE__CGuildBattleScheduleLoad32_user(_this, bToday, dwScheduleID, ucState, tTime, wTumeMin, GUILD_BATTLE__CGuildBattleScheduleLoad32_next);
            };
            int GUILD_BATTLE__CGuildBattleScheduleProcess34_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this)
            {
               return GUILD_BATTLE__CGuildBattleScheduleProcess34_user(_this, GUILD_BATTLE__CGuildBattleScheduleProcess34_next);
            };
            char GUILD_BATTLE__CGuildBattleScheduleSet36_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt)
            {
               return GUILD_BATTLE__CGuildBattleScheduleSet36_user(_this, dwStartTimeInx, dwElapseTimeCnt, GUILD_BATTLE__CGuildBattleScheduleSet36_next);
            };
            void GUILD_BATTLE__CGuildBattleScheduleSetProcState38_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this)
            {
               GUILD_BATTLE__CGuildBattleScheduleSetProcState38_user(_this, GUILD_BATTLE__CGuildBattleScheduleSetProcState38_next);
            };
            void GUILD_BATTLE__CGuildBattleScheduleSetStateList40_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this, struct GUILD_BATTLE::CGuildBattleStateList* pkStateList)
            {
               GUILD_BATTLE__CGuildBattleScheduleSetStateList40_user(_this, pkStateList, GUILD_BATTLE__CGuildBattleScheduleSetStateList40_next);
            };
            
            void GUILD_BATTLE__CGuildBattleScheduledtor_CGuildBattleSchedule46_wrapper(struct GUILD_BATTLE::CGuildBattleSchedule* _this)
            {
               GUILD_BATTLE__CGuildBattleScheduledtor_CGuildBattleSchedule46_user(_this, GUILD_BATTLE__CGuildBattleScheduledtor_CGuildBattleSchedule46_next);
            };
            
            ::std::array<hook_record, 21> CGuildBattleSchedule_functions = 
            {
                _hook_record {
                    (LPVOID)0x1403d9b00L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleSchedulector_CGuildBattleSchedule2_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleSchedulector_CGuildBattleSchedule2_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleSchedulector_CGuildBattleSchedule2_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleSchedule::*)(unsigned int))&GUILD_BATTLE::CGuildBattleSchedule::ctor_CGuildBattleSchedule)
                },
                _hook_record {
                    (LPVOID)0x1403d9de0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleCheck4_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleCheck4_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleCheck4_wrapper),
                    (LPVOID)cast_pointer_function((int(GUILD_BATTLE::CGuildBattleSchedule::*)())&GUILD_BATTLE::CGuildBattleSchedule::Check)
                },
                _hook_record {
                    (LPVOID)0x1403d9e90L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleClear6_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleClear6_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleClear6_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleSchedule::*)())&GUILD_BATTLE::CGuildBattleSchedule::Clear)
                },
                _hook_record {
                    (LPVOID)0x1403d9f10L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleClearDB8_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleClearDB8_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleClearDB8_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleSchedule::*)())&GUILD_BATTLE::CGuildBattleSchedule::ClearDB)
                },
                _hook_record {
                    (LPVOID)0x1403d9120L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleGetBattleTime10_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleGetBattleTime10_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleGetBattleTime10_wrapper),
                    (LPVOID)cast_pointer_function((struct ATL::CTimeSpan*(GUILD_BATTLE::CGuildBattleSchedule::*)(struct ATL::CTimeSpan*))&GUILD_BATTLE::CGuildBattleSchedule::GetBattleTime)
                },
                _hook_record {
                    (LPVOID)0x1403d9440L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleGetBattleTurm12_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleGetBattleTurm12_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleGetBattleTurm12_wrapper),
                    (LPVOID)cast_pointer_function((int(GUILD_BATTLE::CGuildBattleSchedule::*)())&GUILD_BATTLE::CGuildBattleSchedule::GetBattleTurm)
                },
                _hook_record {
                    (LPVOID)0x1403da220L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleGetLeftTime14_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleGetLeftTime14_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleGetLeftTime14_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleSchedule::*)(char*, char*, char*))&GUILD_BATTLE::CGuildBattleSchedule::GetLeftTime)
                },
                _hook_record {
                    (LPVOID)0x1403d93f0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleGetRealStartTime16_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleGetRealStartTime16_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleGetRealStartTime16_wrapper),
                    (LPVOID)cast_pointer_function((int64_t(GUILD_BATTLE::CGuildBattleSchedule::*)())&GUILD_BATTLE::CGuildBattleSchedule::GetRealStartTime)
                },
                _hook_record {
                    (LPVOID)0x1403d9100L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleGetSID18_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleGetSID18_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleGetSID18_wrapper),
                    (LPVOID)cast_pointer_function((unsigned int(GUILD_BATTLE::CGuildBattleSchedule::*)())&GUILD_BATTLE::CGuildBattleSchedule::GetSID)
                },
                _hook_record {
                    (LPVOID)0x1403d93d0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleGetState20_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleGetState20_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleGetState20_wrapper),
                    (LPVOID)cast_pointer_function((int(GUILD_BATTLE::CGuildBattleSchedule::*)())&GUILD_BATTLE::CGuildBattleSchedule::GetState)
                },
                _hook_record {
                    (LPVOID)0x1403deac0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleGetTime22_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleGetTime22_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleGetTime22_wrapper),
                    (LPVOID)cast_pointer_function((struct ATL::CTime*(GUILD_BATTLE::CGuildBattleSchedule::*)(struct ATL::CTime*))&GUILD_BATTLE::CGuildBattleSchedule::GetTime)
                },
                _hook_record {
                    (LPVOID)0x1403de9f0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleIsDone24_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleIsDone24_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleIsDone24_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleSchedule::*)())&GUILD_BATTLE::CGuildBattleSchedule::IsDone)
                },
                _hook_record {
                    (LPVOID)0x1403de990L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleIsEmpty26_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleIsEmpty26_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleIsEmpty26_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleSchedule::*)())&GUILD_BATTLE::CGuildBattleSchedule::IsEmpty)
                },
                _hook_record {
                    (LPVOID)0x1403deaf0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleIsProc28_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleIsProc28_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleIsProc28_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleSchedule::*)())&GUILD_BATTLE::CGuildBattleSchedule::IsProc)
                },
                _hook_record {
                    (LPVOID)0x1403deb50L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleIsWait30_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleIsWait30_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleIsWait30_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleSchedule::*)())&GUILD_BATTLE::CGuildBattleSchedule::IsWait)
                },
                _hook_record {
                    (LPVOID)0x1403d9f60L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleLoad32_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleLoad32_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleLoad32_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleSchedule::*)(bool, unsigned int, char, int64_t, uint16_t))&GUILD_BATTLE::CGuildBattleSchedule::Load)
                },
                _hook_record {
                    (LPVOID)0x1403da3b0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleProcess34_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleProcess34_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleProcess34_wrapper),
                    (LPVOID)cast_pointer_function((int(GUILD_BATTLE::CGuildBattleSchedule::*)())&GUILD_BATTLE::CGuildBattleSchedule::Process)
                },
                _hook_record {
                    (LPVOID)0x1403d9b90L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleSet36_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleSet36_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleSet36_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CGuildBattleSchedule::*)(unsigned int, unsigned int))&GUILD_BATTLE::CGuildBattleSchedule::Set)
                },
                _hook_record {
                    (LPVOID)0x1403debb0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleSetProcState38_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleSetProcState38_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleSetProcState38_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleSchedule::*)())&GUILD_BATTLE::CGuildBattleSchedule::SetProcState)
                },
                _hook_record {
                    (LPVOID)0x1403d9150L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleSetStateList40_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleSetStateList40_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleSetStateList40_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleSchedule::*)(struct GUILD_BATTLE::CGuildBattleStateList*))&GUILD_BATTLE::CGuildBattleSchedule::SetStateList)
                },
                _hook_record {
                    (LPVOID)0x1403d9b80L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduledtor_CGuildBattleSchedule46_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduledtor_CGuildBattleSchedule46_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduledtor_CGuildBattleSchedule46_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleSchedule::*)())&GUILD_BATTLE::CGuildBattleSchedule::dtor_CGuildBattleSchedule)
                },
            };
        }; // end namespace Detail
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE
