// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    namespace vc_attributes
    {
        template<>
        struct implementsAttribute
        {
            const char *interfaces;
            const char *dispinterfaces;
        };
    }; // end namespace vc_attributes
END_ATF_NAMESPACE
