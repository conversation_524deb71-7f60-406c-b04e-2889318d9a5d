#include <GUILD_BATTLE__CNormalGuildBattleFieldListDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Detail
        {
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListctor_CNormalGuildBattleFieldList2_ptr GUILD_BATTLE__CNormalGuildBattleFieldListctor_CNormalGuildBattleFieldList2_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListctor_CNormalGuildBattleFieldList2_clbk GUILD_BATTLE__CNormalGuildBattleFieldListctor_CNormalGuildBattleFieldList2_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListDestroy4_ptr GUILD_BATTLE__CNormalGuildBattleFieldListDestroy4_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListDestroy4_clbk GUILD_BATTLE__CNormalGuildBattleFieldListDestroy4_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetCircleZone6_ptr GUILD_BATTLE__CNormalGuildBattleFieldListGetCircleZone6_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetCircleZone6_clbk GUILD_BATTLE__CNormalGuildBattleFieldListGetCircleZone6_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetField8_ptr GUILD_BATTLE__CNormalGuildBattleFieldListGetField8_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetField8_clbk GUILD_BATTLE__CNormalGuildBattleFieldListGetField8_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetField10_ptr GUILD_BATTLE__CNormalGuildBattleFieldListGetField10_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetField10_clbk GUILD_BATTLE__CNormalGuildBattleFieldListGetField10_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapFieldByRace12_ptr GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapFieldByRace12_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapFieldByRace12_clbk GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapFieldByRace12_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapInxByRace14_ptr GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapInxByRace14_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapInxByRace14_clbk GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapInxByRace14_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetMapCnt16_ptr GUILD_BATTLE__CNormalGuildBattleFieldListGetMapCnt16_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetMapCnt16_clbk GUILD_BATTLE__CNormalGuildBattleFieldListGetMapCnt16_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInx18_ptr GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInx18_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInx18_clbk GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInx18_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInxList20_ptr GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInxList20_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInxList20_clbk GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInxList20_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetRegener22_ptr GUILD_BATTLE__CNormalGuildBattleFieldListGetRegener22_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetRegener22_clbk GUILD_BATTLE__CNormalGuildBattleFieldListGetRegener22_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetStone24_ptr GUILD_BATTLE__CNormalGuildBattleFieldListGetStone24_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListGetStone24_clbk GUILD_BATTLE__CNormalGuildBattleFieldListGetStone24_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListInit26_ptr GUILD_BATTLE__CNormalGuildBattleFieldListInit26_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListInit26_clbk GUILD_BATTLE__CNormalGuildBattleFieldListInit26_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListInitUseField28_ptr GUILD_BATTLE__CNormalGuildBattleFieldListInitUseField28_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListInitUseField28_clbk GUILD_BATTLE__CNormalGuildBattleFieldListInitUseField28_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListInstance30_ptr GUILD_BATTLE__CNormalGuildBattleFieldListInstance30_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListInstance30_clbk GUILD_BATTLE__CNormalGuildBattleFieldListInstance30_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListIsRegistedMapInx32_ptr GUILD_BATTLE__CNormalGuildBattleFieldListIsRegistedMapInx32_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListIsRegistedMapInx32_clbk GUILD_BATTLE__CNormalGuildBattleFieldListIsRegistedMapInx32_user(nullptr);
            
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListdtor_CNormalGuildBattleFieldList36_ptr GUILD_BATTLE__CNormalGuildBattleFieldListdtor_CNormalGuildBattleFieldList36_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldListdtor_CNormalGuildBattleFieldList36_clbk GUILD_BATTLE__CNormalGuildBattleFieldListdtor_CNormalGuildBattleFieldList36_user(nullptr);
            
            
            void GUILD_BATTLE__CNormalGuildBattleFieldListctor_CNormalGuildBattleFieldList2_wrapper(struct GUILD_BATTLE::CNormalGuildBattleFieldList* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleFieldListctor_CNormalGuildBattleFieldList2_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldListctor_CNormalGuildBattleFieldList2_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleFieldListDestroy4_wrapper()
            {
               GUILD_BATTLE__CNormalGuildBattleFieldListDestroy4_user(GUILD_BATTLE__CNormalGuildBattleFieldListDestroy4_next);
            };
            struct CCircleZone* GUILD_BATTLE__CNormalGuildBattleFieldListGetCircleZone6_wrapper(struct GUILD_BATTLE::CNormalGuildBattleFieldList* _this, int iInx)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldListGetCircleZone6_user(_this, iInx, GUILD_BATTLE__CNormalGuildBattleFieldListGetCircleZone6_next);
            };
            struct GUILD_BATTLE::CNormalGuildBattleField* GUILD_BATTLE__CNormalGuildBattleFieldListGetField8_wrapper(struct GUILD_BATTLE::CNormalGuildBattleFieldList* _this, char byRace, unsigned int dwMapCode)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldListGetField8_user(_this, byRace, dwMapCode, GUILD_BATTLE__CNormalGuildBattleFieldListGetField8_next);
            };
            struct GUILD_BATTLE::CNormalGuildBattleField* GUILD_BATTLE__CNormalGuildBattleFieldListGetField10_wrapper(struct GUILD_BATTLE::CNormalGuildBattleFieldList* _this, unsigned int dwMapID)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldListGetField10_user(_this, dwMapID, GUILD_BATTLE__CNormalGuildBattleFieldListGetField10_next);
            };
            struct GUILD_BATTLE::CNormalGuildBattleField* GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapFieldByRace12_wrapper(struct GUILD_BATTLE::CNormalGuildBattleFieldList* _this, char byRace)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapFieldByRace12_user(_this, byRace, GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapFieldByRace12_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapInxByRace14_wrapper(struct GUILD_BATTLE::CNormalGuildBattleFieldList* _this, char byRace, char* byInx)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapInxByRace14_user(_this, byRace, byInx, GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapInxByRace14_next);
            };
            unsigned int GUILD_BATTLE__CNormalGuildBattleFieldListGetMapCnt16_wrapper(struct GUILD_BATTLE::CNormalGuildBattleFieldList* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldListGetMapCnt16_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldListGetMapCnt16_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInx18_wrapper(struct GUILD_BATTLE::CNormalGuildBattleFieldList* _this, char byRace, unsigned int dwMapCode, unsigned int* dwMapInx)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInx18_user(_this, byRace, dwMapCode, dwMapInx, GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInx18_next);
            };
            unsigned int GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInxList20_wrapper(struct GUILD_BATTLE::CNormalGuildBattleFieldList* _this, char byRace, char* pbyInxList)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInxList20_user(_this, byRace, pbyInxList, GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInxList20_next);
            };
            struct CGravityStoneRegener* GUILD_BATTLE__CNormalGuildBattleFieldListGetRegener22_wrapper(struct GUILD_BATTLE::CNormalGuildBattleFieldList* _this, int iInx)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldListGetRegener22_user(_this, iInx, GUILD_BATTLE__CNormalGuildBattleFieldListGetRegener22_next);
            };
            struct CGravityStone* GUILD_BATTLE__CNormalGuildBattleFieldListGetStone24_wrapper(struct GUILD_BATTLE::CNormalGuildBattleFieldList* _this, int iInx)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldListGetStone24_user(_this, iInx, GUILD_BATTLE__CNormalGuildBattleFieldListGetStone24_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleFieldListInit26_wrapper(struct GUILD_BATTLE::CNormalGuildBattleFieldList* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldListInit26_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldListInit26_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleFieldListInitUseField28_wrapper(struct GUILD_BATTLE::CNormalGuildBattleFieldList* _this, char byRace, char* szKeyName, char* szStrBuff, char** szParseBuff)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldListInitUseField28_user(_this, byRace, szKeyName, szStrBuff, szParseBuff, GUILD_BATTLE__CNormalGuildBattleFieldListInitUseField28_next);
            };
            struct GUILD_BATTLE::CNormalGuildBattleFieldList* GUILD_BATTLE__CNormalGuildBattleFieldListInstance30_wrapper()
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldListInstance30_user(GUILD_BATTLE__CNormalGuildBattleFieldListInstance30_next);
            };
            int GUILD_BATTLE__CNormalGuildBattleFieldListIsRegistedMapInx32_wrapper(struct GUILD_BATTLE::CNormalGuildBattleFieldList* _this, char byRace, char** szParseBuff)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldListIsRegistedMapInx32_user(_this, byRace, szParseBuff, GUILD_BATTLE__CNormalGuildBattleFieldListIsRegistedMapInx32_next);
            };
            
            void GUILD_BATTLE__CNormalGuildBattleFieldListdtor_CNormalGuildBattleFieldList36_wrapper(struct GUILD_BATTLE::CNormalGuildBattleFieldList* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleFieldListdtor_CNormalGuildBattleFieldList36_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldListdtor_CNormalGuildBattleFieldList36_next);
            };
            
            ::std::array<hook_record, 17> CNormalGuildBattleFieldList_functions = 
            {
                _hook_record {
                    (LPVOID)0x1403ee250L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListctor_CNormalGuildBattleFieldList2_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListctor_CNormalGuildBattleFieldList2_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldListctor_CNormalGuildBattleFieldList2_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleFieldList::*)())&GUILD_BATTLE::CNormalGuildBattleFieldList::ctor_CNormalGuildBattleFieldList)
                },
                _hook_record {
                    (LPVOID)0x1403ee420L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListDestroy4_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListDestroy4_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldListDestroy4_wrapper),
                    (LPVOID)cast_pointer_function((void(*)())&GUILD_BATTLE::CNormalGuildBattleFieldList::Destroy)
                },
                _hook_record {
                    (LPVOID)0x1403eed90L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetCircleZone6_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetCircleZone6_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldListGetCircleZone6_wrapper),
                    (LPVOID)cast_pointer_function((struct CCircleZone*(GUILD_BATTLE::CNormalGuildBattleFieldList::*)(int))&GUILD_BATTLE::CNormalGuildBattleFieldList::GetCircleZone)
                },
                _hook_record {
                    (LPVOID)0x1403ee870L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetField8_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetField8_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldListGetField8_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CNormalGuildBattleField*(GUILD_BATTLE::CNormalGuildBattleFieldList::*)(char, unsigned int))&GUILD_BATTLE::CNormalGuildBattleFieldList::GetField)
                },
                _hook_record {
                    (LPVOID)0x1403ee950L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetField10_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetField10_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldListGetField10_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CNormalGuildBattleField*(GUILD_BATTLE::CNormalGuildBattleFieldList::*)(unsigned int))&GUILD_BATTLE::CNormalGuildBattleFieldList::GetField)
                },
                _hook_record {
                    (LPVOID)0x1403eeb10L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapFieldByRace12_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapFieldByRace12_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapFieldByRace12_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CNormalGuildBattleField*(GUILD_BATTLE::CNormalGuildBattleFieldList::*)(char))&GUILD_BATTLE::CNormalGuildBattleFieldList::GetFirstMapFieldByRace)
                },
                _hook_record {
                    (LPVOID)0x1403eea80L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapInxByRace14_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapInxByRace14_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldListGetFirstMapInxByRace14_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleFieldList::*)(char, char*))&GUILD_BATTLE::CNormalGuildBattleFieldList::GetFirstMapInxByRace)
                },
                _hook_record {
                    (LPVOID)0x1403d0b10L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetMapCnt16_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetMapCnt16_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldListGetMapCnt16_wrapper),
                    (LPVOID)cast_pointer_function((unsigned int(GUILD_BATTLE::CNormalGuildBattleFieldList::*)())&GUILD_BATTLE::CNormalGuildBattleFieldList::GetMapCnt)
                },
                _hook_record {
                    (LPVOID)0x1403ee990L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInx18_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInx18_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInx18_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleFieldList::*)(char, unsigned int, unsigned int*))&GUILD_BATTLE::CNormalGuildBattleFieldList::GetMapInx)
                },
                _hook_record {
                    (LPVOID)0x1403eeb70L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInxList20_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInxList20_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldListGetMapInxList20_wrapper),
                    (LPVOID)cast_pointer_function((unsigned int(GUILD_BATTLE::CNormalGuildBattleFieldList::*)(char, char*))&GUILD_BATTLE::CNormalGuildBattleFieldList::GetMapInxList)
                },
                _hook_record {
                    (LPVOID)0x1403eecf0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetRegener22_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetRegener22_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldListGetRegener22_wrapper),
                    (LPVOID)cast_pointer_function((struct CGravityStoneRegener*(GUILD_BATTLE::CNormalGuildBattleFieldList::*)(int))&GUILD_BATTLE::CNormalGuildBattleFieldList::GetRegener)
                },
                _hook_record {
                    (LPVOID)0x1403eec50L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetStone24_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListGetStone24_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldListGetStone24_wrapper),
                    (LPVOID)cast_pointer_function((struct CGravityStone*(GUILD_BATTLE::CNormalGuildBattleFieldList::*)(int))&GUILD_BATTLE::CNormalGuildBattleFieldList::GetStone)
                },
                _hook_record {
                    (LPVOID)0x1403ee4a0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListInit26_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListInit26_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldListInit26_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleFieldList::*)())&GUILD_BATTLE::CNormalGuildBattleFieldList::Init)
                },
                _hook_record {
                    (LPVOID)0x1403eee30L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListInitUseField28_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListInitUseField28_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldListInitUseField28_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleFieldList::*)(char, char*, char*, char**))&GUILD_BATTLE::CNormalGuildBattleFieldList::InitUseField)
                },
                _hook_record {
                    (LPVOID)0x1403ee360L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListInstance30_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListInstance30_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldListInstance30_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CNormalGuildBattleFieldList*(*)())&GUILD_BATTLE::CNormalGuildBattleFieldList::Instance)
                },
                _hook_record {
                    (LPVOID)0x1403ef0c0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListIsRegistedMapInx32_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListIsRegistedMapInx32_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldListIsRegistedMapInx32_wrapper),
                    (LPVOID)cast_pointer_function((int(GUILD_BATTLE::CNormalGuildBattleFieldList::*)(char, char**))&GUILD_BATTLE::CNormalGuildBattleFieldList::IsRegistedMapInx)
                },
                _hook_record {
                    (LPVOID)0x1403ee2c0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListdtor_CNormalGuildBattleFieldList36_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldListdtor_CNormalGuildBattleFieldList36_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldListdtor_CNormalGuildBattleFieldList36_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleFieldList::*)())&GUILD_BATTLE::CNormalGuildBattleFieldList::dtor_CNormalGuildBattleFieldList)
                },
            };
        }; // end namespace Detail
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE
