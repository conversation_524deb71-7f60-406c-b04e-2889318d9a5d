// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 8)
    struct _worlddb_guild_battle_info
    {
        struct __list
        {
            unsigned int dwID;
            unsigned int dwP1GuildSerial;
            unsigned int dwP2GuildSerial;
            unsigned int dwMapID;
            char by<PERSON><PERSON><PERSON>;
        };
        unsigned __int16 wCount;
        __list list[69];
    };    
    #pragma pack(pop)
    static_assert(ATF::checkSize<_worlddb_guild_battle_info, 1384>(), "_worlddb_guild_battle_info");
END_ATF_NAMESPACE
