#include <windows.h>
#include "ATFMod.hpp"
#include "modifications/PlayerMod.hpp"

// Include other modification headers as needed
// #include "modifications/GuildMod.hpp"
// #include "modifications/CombatMod.hpp"
// #include "modifications/ItemMod.hpp"
// #include "modifications/NetworkMod.hpp"
// #include "modifications/UtilityMod.hpp"

using namespace ATFMod;

// Global variables
static bool g_DllInitialized = false;
static HMODULE g_hModule = nullptr;

// Function to register all modifications
void RegisterModifications() {
    auto* modManager = ATFModManager::GetInstance();
    
    // Register PlayerMod
    auto playerMod = std::make_unique<PlayerMod>();
    modManager->RegisterModification(std::move(playerMod));
    
    // Register other modifications
    // auto guildMod = std::make_unique<GuildMod>();
    // modManager->RegisterModification(std::move(guildMod));
    
    // auto combatMod = std::make_unique<CombatMod>();
    // modManager->RegisterModification(std::move(combatMod));
    
    // auto itemMod = std::make_unique<ItemMod>();
    // modManager->RegisterModification(std::move(itemMod));
    
    // auto networkMod = std::make_unique<NetworkMod>();
    // modManager->RegisterModification(std::move(networkMod));
    
    // auto utilityMod = std::make_unique<UtilityMod>();
    // modManager->RegisterModification(std::move(utilityMod));
    
    ATF_LOG_INFO("All modifications registered");
}

// Initialization function called when DLL is loaded
BOOL InitializeDLL() {
    if (g_DllInitialized) {
        return TRUE;
    }
    
    // Set up logging
    Logger::SetLevel(Logger::INFO);
    Logger::SetLogFile("ATFMod.log");
    
    ATF_LOG_INFO("=== ATF Monolithic DLL Starting ===");
    ATF_LOG_INFO("DLL Base Address: 0x%p", g_hModule);
    ATF_LOG_INFO("Process ID: %d", GetCurrentProcessId());
    
    // Initialize the modification manager
    if (!ATFModManager::GetInstance()->Initialize()) {
        ATF_LOG_ERROR("Failed to initialize ATF Modification Manager");
        return FALSE;
    }
    
    // Register all modifications
    RegisterModifications();
    
    g_DllInitialized = true;
    ATF_LOG_INFO("ATF Monolithic DLL initialized successfully");
    
    return TRUE;
}

// Cleanup function called when DLL is unloaded
void CleanupDLL() {
    if (!g_DllInitialized) {
        return;
    }
    
    ATF_LOG_INFO("=== ATF Monolithic DLL Shutting Down ===");
    
    // Shutdown the modification manager
    ATFModManager::GetInstance()->Shutdown();
    
    g_DllInitialized = false;
    ATF_LOG_INFO("ATF Monolithic DLL shutdown complete");
}

// Thread function for initialization (to avoid blocking DLL loading)
DWORD WINAPI InitializationThread(LPVOID lpParam) {
    // Wait a bit for the game to fully load
    Sleep(1000);
    
    // Initialize the DLL
    if (!InitializeDLL()) {
        ATF_LOG_ERROR("Failed to initialize DLL in thread");
        return 1;
    }
    
    return 0;
}

// DLL Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        {
            g_hModule = hModule;
            
            // Disable DLL_THREAD_ATTACH and DLL_THREAD_DETACH notifications
            DisableThreadLibraryCalls(hModule);
            
            // Create initialization thread to avoid blocking
            HANDLE hThread = CreateThread(nullptr, 0, InitializationThread, nullptr, 0, nullptr);
            if (hThread) {
                CloseHandle(hThread);
            } else {
                // If thread creation fails, try direct initialization
                return InitializeDLL();
            }
        }
        break;
        
    case DLL_PROCESS_DETACH:
        CleanupDLL();
        break;
        
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
        // These are disabled by DisableThreadLibraryCalls
        break;
    }
    
    return TRUE;
}

// Console application entry point (for testing)
#ifdef _CONSOLE
int main() {
    std::cout << "ATF Monolithic DLL Test Console" << std::endl;
    
    // Simulate DLL loading
    if (!InitializeDLL()) {
        std::cerr << "Failed to initialize DLL" << std::endl;
        return 1;
    }
    
    std::cout << "DLL initialized successfully" << std::endl;
    std::cout << "Press Enter to shutdown..." << std::endl;
    std::cin.get();
    
    // Simulate DLL unloading
    CleanupDLL();
    
    return 0;
}
#endif

// Additional exported functions for external control
extern "C" {
    // Function to enable/disable specific modifications
    __declspec(dllexport) bool ATFMod_EnableModification(const char* modName, bool enable) {
        if (!g_DllInitialized) {
            return false;
        }
        
        auto* mod = ATFModManager::GetInstance()->GetModification(modName);
        if (!mod) {
            ATF_LOG_ERROR("Modification '%s' not found", modName);
            return false;
        }
        
        mod->SetEnabled(enable);
        ATF_LOG_INFO("Modification '%s' %s", modName, enable ? "enabled" : "disabled");
        return true;
    }
    
    // Function to check if a modification is loaded
    __declspec(dllexport) bool ATFMod_IsModificationLoaded(const char* modName) {
        if (!g_DllInitialized) {
            return false;
        }
        
        return ATFModManager::GetInstance()->GetModification(modName) != nullptr;
    }
    
    // Function to get list of loaded modifications
    __declspec(dllexport) int ATFMod_GetLoadedModifications(char* buffer, int bufferSize) {
        if (!g_DllInitialized || !buffer || bufferSize <= 0) {
            return 0;
        }
        
        auto mods = ATFModManager::GetInstance()->GetLoadedModifications();
        std::string result;
        
        for (size_t i = 0; i < mods.size(); ++i) {
            if (i > 0) result += ";";
            result += mods[i];
        }
        
        int copySize = min(static_cast<int>(result.length()), bufferSize - 1);
        memcpy(buffer, result.c_str(), copySize);
        buffer[copySize] = '\0';
        
        return copySize;
    }
    
    // Function to execute console commands
    __declspec(dllexport) bool ATFMod_ExecuteCommand(const char* command) {
        if (!g_DllInitialized || !command) {
            return false;
        }
        
        std::string cmd(command);
        ATF_LOG_INFO("Executing command: %s", command);
        
        // Parse and execute commands
        if (cmd.find("player.godmode") == 0) {
            auto* playerMod = static_cast<PlayerMod*>(
                ATFModManager::GetInstance()->GetModification("PlayerMod"));
            if (playerMod) {
                bool enable = cmd.find("true") != std::string::npos || cmd.find("1") != std::string::npos;
                playerMod->SetGodMode(enable);
                return true;
            }
        }
        else if (cmd.find("player.money") == 0) {
            auto* playerMod = static_cast<PlayerMod*>(
                ATFModManager::GetInstance()->GetModification("PlayerMod"));
            if (playerMod) {
                bool enable = cmd.find("true") != std::string::npos || cmd.find("1") != std::string::npos;
                playerMod->SetUnlimitedMoney(enable);
                return true;
            }
        }
        else if (cmd.find("player.speed") == 0) {
            auto* playerMod = static_cast<PlayerMod*>(
                ATFModManager::GetInstance()->GetModification("PlayerMod"));
            if (playerMod) {
                // Parse speed multiplier
                size_t pos = cmd.find(' ');
                if (pos != std::string::npos) {
                    float multiplier = std::stof(cmd.substr(pos + 1));
                    playerMod->SetSpeedHack(true, multiplier);
                    return true;
                }
            }
        }
        
        ATF_LOG_WARNING("Unknown command: %s", command);
        return false;
    }
}
