# ATF Monolithic DLL Modification Framework

## 🎯 **Overview**

The ATF Monolithic DLL is a comprehensive game modification framework that allows you to modify game behavior through a single, powerful DLL injection. This approach provides:

- **Unified Modification System**: All game modifications in one DLL
- **Memory Hooking**: Intercept and modify game functions
- **Runtime Configuration**: Enable/disable features without restarting
- **Modular Architecture**: Easy to add new modifications
- **Safe Injection**: Proper initialization and cleanup

## 🏗️ **Architecture**

### **Core Components**

1. **ATFModManager**: Central manager for all modifications
2. **HookManager**: Handles function hooking and interception
3. **MemoryPatcher**: Direct memory patching capabilities
4. **ConfigManager**: Configuration file management
5. **Logger**: Comprehensive logging system

### **Modification Modules**

- **PlayerMod**: Player enhancements (god mode, unlimited money, speed hack)
- **GuildMod**: Guild system modifications
- **CombatMod**: Combat system enhancements
- **ItemMod**: Item system modifications
- **NetworkMod**: Network protocol modifications
- **UtilityMod**: General utility features

## 🚀 **Quick Start**

### **1. Building the DLL**

```bash
# Navigate to the monolithic_dll directory
cd monolithic_dll

# Create build directory
mkdir build && cd build

# Configure with CMake
cmake .. -DCMAKE_BUILD_TYPE=Release

# Build the DLL
cmake --build . --config Release
```

### **2. Injecting the DLL**

```bash
# Using the included injector
ATFInjector.exe game.exe ATFMod.dll

# Or using any DLL injector
# 1. Start the target game
# 2. Inject ATFMod.dll into the game process
# 3. The DLL will automatically initialize
```

### **3. Configuration**

The DLL creates an `ATFMod.ini` configuration file:

```ini
[PlayerMod]
GodMode=false
UnlimitedMoney=false
SpeedHack=false
SpeedMultiplier=2.0

[GuildMod]
AutoAcceptInvites=false
GuildBattleEnhancements=false

[Logging]
Level=1
LogFile=ATFMod.log
```

## 🛠️ **Usage Examples**

### **Player Modifications**

```cpp
// Enable god mode
ATFMod_ExecuteCommand("player.godmode true");

// Enable unlimited money
ATFMod_ExecuteCommand("player.money true");

// Set speed hack with 3x multiplier
ATFMod_ExecuteCommand("player.speed 3.0");
```

### **Programmatic Control**

```cpp
// Enable a specific modification
ATFMod_EnableModification("PlayerMod", true);

// Check if modification is loaded
bool loaded = ATFMod_IsModificationLoaded("PlayerMod");

// Get list of loaded modifications
char buffer[1024];
int count = ATFMod_GetLoadedModifications(buffer, sizeof(buffer));
```

## 🔧 **Technical Details**

### **Memory Hooking Process**

1. **Function Discovery**: Locate target functions in game memory
2. **Hook Installation**: Replace function prologue with jump to hook
3. **Trampoline Creation**: Preserve original function for calling
4. **Interception**: Custom code executes before/after original
5. **Cleanup**: Restore original functions on DLL unload

### **Hook Example**

```cpp
// Original game function
void CPlayer::AddDalant(unsigned int amount, bool apply);

// Hook function
void HookedAddDalant(CPlayer* player, unsigned int amount, bool apply) {
    // Modify behavior
    if (unlimitedMoney) {
        amount *= 10; // 10x money multiplier
    }
    
    // Call original function
    originalAddDalant(player, amount, apply);
}
```

### **Memory Addresses**

The framework uses hardcoded memory addresses from reverse engineering:

```cpp
// Example addresses (these need to be updated for your game version)
void* addDalantAddr = reinterpret_cast<void*>(0x140055b00L);
void* addGoldAddr = reinterpret_cast<void*>(0x140055d30L);
void* alterExpAddr = reinterpret_cast<void*>(0x14005bb50L);
```

## ⚙️ **Configuration Options**

### **Build Options**

- `BUILD_CONSOLE_TEST`: Build console test application
- `USE_MINHOOK`: Use MinHook library for hooking
- `ENABLE_LOGGING`: Enable detailed logging

### **Runtime Configuration**

```ini
[General]
AutoInitialize=true
LoadAllModifications=true

[Logging]
Level=1          # 0=Debug, 1=Info, 2=Warning, 3=Error
LogFile=ATFMod.log
ConsoleOutput=true

[PlayerMod]
Enabled=true
GodMode=false
UnlimitedMoney=false
SpeedHack=false
SpeedMultiplier=2.0

[Security]
AntiDetection=true
HideFromProcessList=false
```

## 🔒 **Security Considerations**

### **Anti-Cheat Evasion**

- **Function Hooking**: Use advanced hooking techniques
- **Memory Protection**: Avoid detectable memory patterns
- **Timing**: Randomize hook installation timing
- **Stealth**: Hide DLL from process enumeration

### **Safe Practices**

- Always backup game files before modifying
- Test modifications in offline mode first
- Use proper error handling and cleanup
- Respect game's terms of service

## 📝 **Adding New Modifications**

### **1. Create Modification Class**

```cpp
class MyMod : public IGameModification {
public:
    bool Initialize() override;
    void Shutdown() override;
    const char* GetName() const override { return "MyMod"; }
    Priority GetPriority() const override { return Priority::NORMAL; }
    bool IsEnabled() const override { return enabled; }
    void SetEnabled(bool enable) override;

private:
    bool enabled = false;
};
```

### **2. Register Modification**

```cpp
// In DllMain.cpp RegisterModifications()
auto myMod = std::make_unique<MyMod>();
modManager->RegisterModification(std::move(myMod));
```

### **3. Implement Hooks**

```cpp
bool MyMod::Initialize() {
    auto* hookManager = ATFModManager::GetInstance()->GetHookManager();
    
    // Install hooks
    hookManager->InstallHook("MyMod_Function", targetAddr, hookFunction);
    hookManager->EnableHook("MyMod_Function");
    
    return true;
}
```

## 🐛 **Debugging**

### **Log Analysis**

```
[2024-01-20 15:30:45.123] [INFO] ATF Monolithic DLL Starting
[2024-01-20 15:30:45.124] [INFO] DLL Base Address: 0x180000000
[2024-01-20 15:30:45.125] [INFO] HookManager initialized successfully
[2024-01-20 15:30:45.126] [INFO] PlayerMod hooks installed successfully
[2024-01-20 15:30:45.127] [INFO] ATF Monolithic DLL initialized successfully
```

### **Common Issues**

1. **Hook Installation Fails**
   - Check memory addresses are correct for game version
   - Ensure sufficient privileges (run as administrator)
   - Verify MinHook library is available

2. **DLL Injection Fails**
   - Game may have anti-cheat protection
   - Try different injection methods
   - Check DLL architecture matches game (x64/x86)

3. **Game Crashes**
   - Review hook implementations for errors
   - Check memory access violations
   - Ensure proper cleanup in destructors

## 📚 **API Reference**

### **Exported Functions**

```cpp
// Initialize the modification system
bool ATFMod_Initialize();

// Shutdown and cleanup
void ATFMod_Shutdown();

// Get version information
const char* ATFMod_GetVersion();

// Enable/disable specific modifications
bool ATFMod_EnableModification(const char* modName, bool enable);

// Check if modification is loaded
bool ATFMod_IsModificationLoaded(const char* modName);

// Get list of loaded modifications
int ATFMod_GetLoadedModifications(char* buffer, int bufferSize);

// Execute console commands
bool ATFMod_ExecuteCommand(const char* command);
```

### **Console Commands**

```
player.godmode <true/false>     - Enable/disable god mode
player.money <true/false>       - Enable/disable unlimited money
player.speed <multiplier>       - Set speed hack multiplier
guild.autojoin <true/false>     - Auto-join guild battles
combat.damage <multiplier>      - Set damage multiplier
item.repair                     - Repair all items
```

## 📄 **License and Legal**

This framework is provided for educational and research purposes only. Users are responsible for:

- Complying with game terms of service
- Respecting intellectual property rights
- Using modifications responsibly
- Not distributing copyrighted game content

## 🤝 **Contributing**

To contribute new modifications:

1. Fork the repository
2. Create a new modification class
3. Implement proper hooking and cleanup
4. Add configuration options
5. Test thoroughly
6. Submit a pull request

## 📞 **Support**

For issues and questions:

- Check the log files for error messages
- Review the documentation and examples
- Test with minimal modifications first
- Report bugs with detailed reproduction steps
