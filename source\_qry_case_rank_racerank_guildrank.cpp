#include <_qry_case_rank_racerank_guildrank.hpp>


START_ATF_NAMESPACE
    void _qry_case_rank_racerank_guildrank::ClearRetParam()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_rank_racerank_guildrank*);
        (org_ptr(0x1403443f0L))(this);
    };
    _qry_case_rank_racerank_guildrank::_qry_case_rank_racerank_guildrank()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_rank_racerank_guildrank*);
        (org_ptr(0x1403447a0L))(this);
    };
    void _qry_case_rank_racerank_guildrank::ctor__qry_case_rank_racerank_guildrank()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_rank_racerank_guildrank*);
        (org_ptr(0x1403447a0L))(this);
    };
    
END_ATF_NAMESPACE
