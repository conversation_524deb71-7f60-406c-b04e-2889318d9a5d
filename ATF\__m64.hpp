// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    union __m64
    {
        unsigned __int64 m64_u64;
        float m64_f32[2];
        char m64_i8[8];
        __int16 m64_i16[4];
        int m64_i32[2];
        __int64 m64_i64;
        char m64_u8[8];
        unsigned __int16 m64_u16[4];
        unsigned int m64_u32[2];
    };
END_ATF_NAMESPACE
