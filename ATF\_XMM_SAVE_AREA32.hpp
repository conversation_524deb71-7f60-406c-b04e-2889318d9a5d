// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_M128.hpp>


START_ATF_NAMESPACE
    struct _XMM_SAVE_AREA32
    {
        unsigned __int16 ControlWord;
        unsigned __int16 StatusWord;
        char TagWord;
        char Reserved1;
        unsigned __int16 ErrorOpcode;
        unsigned int ErrorOffset;
        unsigned __int16 ErrorSelector;
        unsigned __int16 Reserved2;
        unsigned int DataOffset;
        unsigned __int16 DataSelector;
        unsigned __int16 Reserved3;
        unsigned int MxCsr;
        unsigned int MxCsr_Mask;
        _M128 FloatRegisters[8];
        _M128 XmmRegisters[16];
        char Reserved4[96];
    };    
    static_assert(ATF::checkSize<_XMM_SAVE_AREA32, 512>(), "_XMM_SAVE_AREA32");
END_ATF_NAMESPACE
