#include <_unmannedtrader_buy_item_result_zocl.hpp>


START_ATF_NAMESPACE
    _unmannedtrader_buy_item_result_zocl::_unmannedtrader_buy_item_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _unmannedtrader_buy_item_result_zocl*);
        (org_ptr(0x140351860L))(this);
    };
    void _unmannedtrader_buy_item_result_zocl::ctor__unmannedtrader_buy_item_result_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _unmannedtrader_buy_item_result_zocl*);
        (org_ptr(0x140351860L))(this);
    };
    int _unmannedtrader_buy_item_result_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _unmannedtrader_buy_item_result_zocl*);
        return (org_ptr(0x140351890L))(this);
    };
    
END_ATF_NAMESPACE
