#include <_suggested_matter_change_taxrate.hpp>


START_ATF_NAMESPACE
    _suggested_matter_change_taxrate::_suggested_matter_change_taxrate()
    {
        using org_ptr = void (WINAPIV*)(struct _suggested_matter_change_taxrate*);
        (org_ptr(0x1402d97c0L))(this);
    };
    void _suggested_matter_change_taxrate::ctor__suggested_matter_change_taxrate()
    {
        using org_ptr = void (WINAPIV*)(struct _suggested_matter_change_taxrate*);
        (org_ptr(0x1402d97c0L))(this);
    };
    void _suggested_matter_change_taxrate::init()
    {
        using org_ptr = void (WINAPIV*)(struct _suggested_matter_change_taxrate*);
        (org_ptr(0x1402d9810L))(this);
    };
END_ATF_NAMESPACE
