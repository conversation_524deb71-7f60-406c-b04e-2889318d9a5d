#include <_personal_automine_attacked_zocl.hpp>


START_ATF_NAMESPACE
    _personal_automine_attacked_zocl::_personal_automine_attacked_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_attacked_zocl*);
        (org_ptr(0x1402de2f0L))(this);
    };
    void _personal_automine_attacked_zocl::ctor__personal_automine_attacked_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _personal_automine_attacked_zocl*);
        (org_ptr(0x1402de2f0L))(this);
    };
    int _personal_automine_attacked_zocl::size()
    {
        using org_ptr = int (WINAPIV*)(struct _personal_automine_attacked_zocl*);
        return (org_ptr(0x1402de340L))(this);
    };
END_ATF_NAMESPACE
