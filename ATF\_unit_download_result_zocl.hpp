// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_UNIT_DB_BASE.hpp>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct _unit_download_result_zocl
    {
        struct  _list
        {
            char bySlotIndex;
            _UNIT_DB_BASE::_LIST UnitData;
        public:
            _list();
            void ctor__list();
        };
        char byUnitNum;
        _list UnitList[4];
    public:
        _unit_download_result_zocl();
        void ctor__unit_download_result_zocl();
        int size();
    };
    #pragma pack(pop)
    static_assert(ATF::checkSize<_unit_download_result_zocl, 253>(), "_unit_download_result_zocl");
END_ATF_NAMESPACE
