// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _unmannedtrader_search_list_request_clzo
    {
        char byStoreIndex;
        char byDivision;
        char byClass;
        char bySubClass;
        char bySortType;
        unsigned int dwVer;
        char byPage;
        int bUseNpcLink;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
