#include <PatriarchElectProcessorDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace Detail
    {
        Info::PatriarchElectProcessorCheatClearPatriarch2_ptr PatriarchElectProcessorCheatClearPatriarch2_next(nullptr);
        Info::PatriarchElectProcessorCheatClearPatriarch2_clbk PatriarchElectProcessorCheatClearPatriarch2_user(nullptr);
        
        Info::PatriarchElectProcessorCheatSetPatriarch4_ptr PatriarchElectProcessorCheatSetPatriarch4_next(nullptr);
        Info::PatriarchElectProcessorCheatSetPatriarch4_clbk PatriarchElectProcessorCheatSetPatriarch4_user(nullptr);
        
        Info::PatriarchElectProcessorCompleteCheckInvalidChar6_ptr PatriarchElectProcessorCompleteCheckInvalidChar6_next(nullptr);
        Info::PatriarchElectProcessorCompleteCheckInvalidChar6_clbk PatriarchElectProcessorCompleteCheckInvalidChar6_user(nullptr);
        
        Info::PatriarchElectProcessorCompleteInsertElect8_ptr PatriarchElectProcessorCompleteInsertElect8_next(nullptr);
        Info::PatriarchElectProcessorCompleteInsertElect8_clbk PatriarchElectProcessorCompleteInsertElect8_user(nullptr);
        
        Info::PatriarchElectProcessorCompleteInsertPatriarch10_ptr PatriarchElectProcessorCompleteInsertPatriarch10_next(nullptr);
        Info::PatriarchElectProcessorCompleteInsertPatriarch10_clbk PatriarchElectProcessorCompleteInsertPatriarch10_user(nullptr);
        
        Info::PatriarchElectProcessorCompleteItemChargeRefund12_ptr PatriarchElectProcessorCompleteItemChargeRefund12_next(nullptr);
        Info::PatriarchElectProcessorCompleteItemChargeRefund12_clbk PatriarchElectProcessorCompleteItemChargeRefund12_user(nullptr);
        
        Info::PatriarchElectProcessorCompleteRequestRefund14_ptr PatriarchElectProcessorCompleteRequestRefund14_next(nullptr);
        Info::PatriarchElectProcessorCompleteRequestRefund14_clbk PatriarchElectProcessorCompleteRequestRefund14_user(nullptr);
        
        Info::PatriarchElectProcessorCompleteSelectElect16_ptr PatriarchElectProcessorCompleteSelectElect16_next(nullptr);
        Info::PatriarchElectProcessorCompleteSelectElect16_clbk PatriarchElectProcessorCompleteSelectElect16_user(nullptr);
        
        Info::PatriarchElectProcessorDestroy18_ptr PatriarchElectProcessorDestroy18_next(nullptr);
        Info::PatriarchElectProcessorDestroy18_clbk PatriarchElectProcessorDestroy18_user(nullptr);
        
        Info::PatriarchElectProcessorDoit20_ptr PatriarchElectProcessorDoit20_next(nullptr);
        Info::PatriarchElectProcessorDoit20_clbk PatriarchElectProcessorDoit20_user(nullptr);
        
        Info::PatriarchElectProcessorForceChangeProcessor22_ptr PatriarchElectProcessorForceChangeProcessor22_next(nullptr);
        Info::PatriarchElectProcessorForceChangeProcessor22_clbk PatriarchElectProcessorForceChangeProcessor22_user(nullptr);
        
        Info::PatriarchElectProcessorGetCurrPatriarchElectSerial24_ptr PatriarchElectProcessorGetCurrPatriarchElectSerial24_next(nullptr);
        Info::PatriarchElectProcessorGetCurrPatriarchElectSerial24_clbk PatriarchElectProcessorGetCurrPatriarchElectSerial24_user(nullptr);
        
        Info::PatriarchElectProcessorGetElectSerial26_ptr PatriarchElectProcessorGetElectSerial26_next(nullptr);
        Info::PatriarchElectProcessorGetElectSerial26_clbk PatriarchElectProcessorGetElectSerial26_user(nullptr);
        
        Info::PatriarchElectProcessorGetProcessorType28_ptr PatriarchElectProcessorGetProcessorType28_next(nullptr);
        Info::PatriarchElectProcessorGetProcessorType28_clbk PatriarchElectProcessorGetProcessorType28_user(nullptr);
        
        Info::PatriarchElectProcessorGetTimeCheck30_ptr PatriarchElectProcessorGetTimeCheck30_next(nullptr);
        Info::PatriarchElectProcessorGetTimeCheck30_clbk PatriarchElectProcessorGetTimeCheck30_user(nullptr);
        
        Info::PatriarchElectProcessorInitProcess32_ptr PatriarchElectProcessorInitProcess32_next(nullptr);
        Info::PatriarchElectProcessorInitProcess32_clbk PatriarchElectProcessorInitProcess32_user(nullptr);
        
        Info::PatriarchElectProcessorInitialize34_ptr PatriarchElectProcessorInitialize34_next(nullptr);
        Info::PatriarchElectProcessorInitialize34_clbk PatriarchElectProcessorInitialize34_user(nullptr);
        
        Info::PatriarchElectProcessorInsert_Elect36_ptr PatriarchElectProcessorInsert_Elect36_next(nullptr);
        Info::PatriarchElectProcessorInsert_Elect36_clbk PatriarchElectProcessorInsert_Elect36_user(nullptr);
        
        Info::PatriarchElectProcessorInsert_PatrirchItemChargeRefund38_ptr PatriarchElectProcessorInsert_PatrirchItemChargeRefund38_next(nullptr);
        Info::PatriarchElectProcessorInsert_PatrirchItemChargeRefund38_clbk PatriarchElectProcessorInsert_PatrirchItemChargeRefund38_user(nullptr);
        
        Info::PatriarchElectProcessorInstance40_ptr PatriarchElectProcessorInstance40_next(nullptr);
        Info::PatriarchElectProcessorInstance40_clbk PatriarchElectProcessorInstance40_user(nullptr);
        
        Info::PatriarchElectProcessorLoadDatabae42_ptr PatriarchElectProcessorLoadDatabae42_next(nullptr);
        Info::PatriarchElectProcessorLoadDatabae42_clbk PatriarchElectProcessorLoadDatabae42_user(nullptr);
        
        Info::PatriarchElectProcessorLoadElectState44_ptr PatriarchElectProcessorLoadElectState44_next(nullptr);
        Info::PatriarchElectProcessorLoadElectState44_clbk PatriarchElectProcessorLoadElectState44_user(nullptr);
        
        Info::PatriarchElectProcessorLoop46_ptr PatriarchElectProcessorLoop46_next(nullptr);
        Info::PatriarchElectProcessorLoop46_clbk PatriarchElectProcessorLoop46_user(nullptr);
        
        
        Info::PatriarchElectProcessorctor_PatriarchElectProcessor48_ptr PatriarchElectProcessorctor_PatriarchElectProcessor48_next(nullptr);
        Info::PatriarchElectProcessorctor_PatriarchElectProcessor48_clbk PatriarchElectProcessorctor_PatriarchElectProcessor48_user(nullptr);
        
        Info::PatriarchElectProcessorPushDQSCheckInvalidChar50_ptr PatriarchElectProcessorPushDQSCheckInvalidChar50_next(nullptr);
        Info::PatriarchElectProcessorPushDQSCheckInvalidChar50_clbk PatriarchElectProcessorPushDQSCheckInvalidChar50_user(nullptr);
        
        Info::PatriarchElectProcessorRequest_Refund52_ptr PatriarchElectProcessorRequest_Refund52_next(nullptr);
        Info::PatriarchElectProcessorRequest_Refund52_clbk PatriarchElectProcessorRequest_Refund52_user(nullptr);
        
        Info::PatriarchElectProcessorSendMsg_ConnectNewUser54_ptr PatriarchElectProcessorSendMsg_ConnectNewUser54_next(nullptr);
        Info::PatriarchElectProcessorSendMsg_ConnectNewUser54_clbk PatriarchElectProcessorSendMsg_ConnectNewUser54_user(nullptr);
        
        Info::PatriarchElectProcessorSendMsg_ResultCode56_ptr PatriarchElectProcessorSendMsg_ResultCode56_next(nullptr);
        Info::PatriarchElectProcessorSendMsg_ResultCode56_clbk PatriarchElectProcessorSendMsg_ResultCode56_user(nullptr);
        
        Info::PatriarchElectProcessorSetCurrPatriarchElectSerial58_ptr PatriarchElectProcessorSetCurrPatriarchElectSerial58_next(nullptr);
        Info::PatriarchElectProcessorSetCurrPatriarchElectSerial58_clbk PatriarchElectProcessorSetCurrPatriarchElectSerial58_user(nullptr);
        
        Info::PatriarchElectProcessorSetTimeCheck60_ptr PatriarchElectProcessorSetTimeCheck60_next(nullptr);
        Info::PatriarchElectProcessorSetTimeCheck60_clbk PatriarchElectProcessorSetTimeCheck60_user(nullptr);
        
        Info::PatriarchElectProcessorTimeCheck62_ptr PatriarchElectProcessorTimeCheck62_next(nullptr);
        Info::PatriarchElectProcessorTimeCheck62_clbk PatriarchElectProcessorTimeCheck62_user(nullptr);
        
        Info::PatriarchElectProcessorUpdate_Elect64_ptr PatriarchElectProcessorUpdate_Elect64_next(nullptr);
        Info::PatriarchElectProcessorUpdate_Elect64_clbk PatriarchElectProcessorUpdate_Elect64_user(nullptr);
        
        
        Info::PatriarchElectProcessordtor_PatriarchElectProcessor68_ptr PatriarchElectProcessordtor_PatriarchElectProcessor68_next(nullptr);
        Info::PatriarchElectProcessordtor_PatriarchElectProcessor68_clbk PatriarchElectProcessordtor_PatriarchElectProcessor68_user(nullptr);
        
        bool PatriarchElectProcessorCheatClearPatriarch2_wrapper(struct PatriarchElectProcessor* _this)
        {
           return PatriarchElectProcessorCheatClearPatriarch2_user(_this, PatriarchElectProcessorCheatClearPatriarch2_next);
        };
        bool PatriarchElectProcessorCheatSetPatriarch4_wrapper(struct PatriarchElectProcessor* _this, struct CPlayer* pOne, _candidate_info::ClassType eClass)
        {
           return PatriarchElectProcessorCheatSetPatriarch4_user(_this, pOne, eClass, PatriarchElectProcessorCheatSetPatriarch4_next);
        };
        void PatriarchElectProcessorCompleteCheckInvalidChar6_wrapper(struct PatriarchElectProcessor* _this, char byProc)
        {
           PatriarchElectProcessorCompleteCheckInvalidChar6_user(_this, byProc, PatriarchElectProcessorCompleteCheckInvalidChar6_next);
        };
        void PatriarchElectProcessorCompleteInsertElect8_wrapper(struct PatriarchElectProcessor* _this)
        {
           PatriarchElectProcessorCompleteInsertElect8_user(_this, PatriarchElectProcessorCompleteInsertElect8_next);
        };
        void PatriarchElectProcessorCompleteInsertPatriarch10_wrapper(struct PatriarchElectProcessor* _this, struct _DB_QRY_SYN_DATA* pData)
        {
           PatriarchElectProcessorCompleteInsertPatriarch10_user(_this, pData, PatriarchElectProcessorCompleteInsertPatriarch10_next);
        };
        void PatriarchElectProcessorCompleteItemChargeRefund12_wrapper(struct PatriarchElectProcessor* _this, struct _DB_QRY_SYN_DATA* pData)
        {
           PatriarchElectProcessorCompleteItemChargeRefund12_user(_this, pData, PatriarchElectProcessorCompleteItemChargeRefund12_next);
        };
        void PatriarchElectProcessorCompleteRequestRefund14_wrapper(struct PatriarchElectProcessor* _this, struct _DB_QRY_SYN_DATA* pData)
        {
           PatriarchElectProcessorCompleteRequestRefund14_user(_this, pData, PatriarchElectProcessorCompleteRequestRefund14_next);
        };
        void PatriarchElectProcessorCompleteSelectElect16_wrapper(struct PatriarchElectProcessor* _this)
        {
           PatriarchElectProcessorCompleteSelectElect16_user(_this, PatriarchElectProcessorCompleteSelectElect16_next);
        };
        void PatriarchElectProcessorDestroy18_wrapper(struct PatriarchElectProcessor* _this)
        {
           PatriarchElectProcessorDestroy18_user(_this, PatriarchElectProcessorDestroy18_next);
        };
        bool PatriarchElectProcessorDoit20_wrapper(struct PatriarchElectProcessor* _this, Cmd eCmd, struct CPlayer* pOne, char* pdata)
        {
           return PatriarchElectProcessorDoit20_user(_this, eCmd, pOne, pdata, PatriarchElectProcessorDoit20_next);
        };
        bool PatriarchElectProcessorForceChangeProcessor22_wrapper(struct PatriarchElectProcessor* _this, ElectProcessor::ProcessorType eProc)
        {
           return PatriarchElectProcessorForceChangeProcessor22_user(_this, eProc, PatriarchElectProcessorForceChangeProcessor22_next);
        };
        unsigned int PatriarchElectProcessorGetCurrPatriarchElectSerial24_wrapper(struct PatriarchElectProcessor* _this)
        {
           return PatriarchElectProcessorGetCurrPatriarchElectSerial24_user(_this, PatriarchElectProcessorGetCurrPatriarchElectSerial24_next);
        };
        unsigned int PatriarchElectProcessorGetElectSerial26_wrapper(struct PatriarchElectProcessor* _this)
        {
           return PatriarchElectProcessorGetElectSerial26_user(_this, PatriarchElectProcessorGetElectSerial26_next);
        };
        ElectProcessor::ProcessorType PatriarchElectProcessorGetProcessorType28_wrapper(struct PatriarchElectProcessor* _this)
        {
           return PatriarchElectProcessorGetProcessorType28_user(_this, PatriarchElectProcessorGetProcessorType28_next);
        };
        bool PatriarchElectProcessorGetTimeCheck30_wrapper(struct PatriarchElectProcessor* _this)
        {
           return PatriarchElectProcessorGetTimeCheck30_user(_this, PatriarchElectProcessorGetTimeCheck30_next);
        };
        bool PatriarchElectProcessorInitProcess32_wrapper(struct PatriarchElectProcessor* _this)
        {
           return PatriarchElectProcessorInitProcess32_user(_this, PatriarchElectProcessorInitProcess32_next);
        };
        bool PatriarchElectProcessorInitialize34_wrapper(struct PatriarchElectProcessor* _this)
        {
           return PatriarchElectProcessorInitialize34_user(_this, PatriarchElectProcessorInitialize34_next);
        };
        int PatriarchElectProcessorInsert_Elect36_wrapper(struct PatriarchElectProcessor* _this)
        {
           return PatriarchElectProcessorInsert_Elect36_user(_this, PatriarchElectProcessorInsert_Elect36_next);
        };
        int PatriarchElectProcessorInsert_PatrirchItemChargeRefund38_wrapper(struct PatriarchElectProcessor* _this, char* pData)
        {
           return PatriarchElectProcessorInsert_PatrirchItemChargeRefund38_user(_this, pData, PatriarchElectProcessorInsert_PatrirchItemChargeRefund38_next);
        };
        struct PatriarchElectProcessor* PatriarchElectProcessorInstance40_wrapper()
        {
           return PatriarchElectProcessorInstance40_user(PatriarchElectProcessorInstance40_next);
        };
        bool PatriarchElectProcessorLoadDatabae42_wrapper(struct PatriarchElectProcessor* _this)
        {
           return PatriarchElectProcessorLoadDatabae42_user(_this, PatriarchElectProcessorLoadDatabae42_next);
        };
        bool PatriarchElectProcessorLoadElectState44_wrapper(struct PatriarchElectProcessor* _this)
        {
           return PatriarchElectProcessorLoadElectState44_user(_this, PatriarchElectProcessorLoadElectState44_next);
        };
        void PatriarchElectProcessorLoop46_wrapper(struct PatriarchElectProcessor* _this)
        {
           PatriarchElectProcessorLoop46_user(_this, PatriarchElectProcessorLoop46_next);
        };
        
        void PatriarchElectProcessorctor_PatriarchElectProcessor48_wrapper(struct PatriarchElectProcessor* _this)
        {
           PatriarchElectProcessorctor_PatriarchElectProcessor48_user(_this, PatriarchElectProcessorctor_PatriarchElectProcessor48_next);
        };
        void PatriarchElectProcessorPushDQSCheckInvalidChar50_wrapper(struct PatriarchElectProcessor* _this)
        {
           PatriarchElectProcessorPushDQSCheckInvalidChar50_user(_this, PatriarchElectProcessorPushDQSCheckInvalidChar50_next);
        };
        int PatriarchElectProcessorRequest_Refund52_wrapper(struct PatriarchElectProcessor* _this, char* pData)
        {
           return PatriarchElectProcessorRequest_Refund52_user(_this, pData, PatriarchElectProcessorRequest_Refund52_next);
        };
        void PatriarchElectProcessorSendMsg_ConnectNewUser54_wrapper(struct PatriarchElectProcessor* _this, struct CPlayer* pOne)
        {
           PatriarchElectProcessorSendMsg_ConnectNewUser54_user(_this, pOne, PatriarchElectProcessorSendMsg_ConnectNewUser54_next);
        };
        void PatriarchElectProcessorSendMsg_ResultCode56_wrapper(struct PatriarchElectProcessor* _this, unsigned int n, char byCode)
        {
           PatriarchElectProcessorSendMsg_ResultCode56_user(_this, n, byCode, PatriarchElectProcessorSendMsg_ResultCode56_next);
        };
        void PatriarchElectProcessorSetCurrPatriarchElectSerial58_wrapper(struct PatriarchElectProcessor* _this, unsigned int dwSerial)
        {
           PatriarchElectProcessorSetCurrPatriarchElectSerial58_user(_this, dwSerial, PatriarchElectProcessorSetCurrPatriarchElectSerial58_next);
        };
        void PatriarchElectProcessorSetTimeCheck60_wrapper(struct PatriarchElectProcessor* _this, bool bFlag)
        {
           PatriarchElectProcessorSetTimeCheck60_user(_this, bFlag, PatriarchElectProcessorSetTimeCheck60_next);
        };
        void PatriarchElectProcessorTimeCheck62_wrapper(struct PatriarchElectProcessor* _this, uint16_t wDayOfWeek, uint16_t wHour)
        {
           PatriarchElectProcessorTimeCheck62_user(_this, wDayOfWeek, wHour, PatriarchElectProcessorTimeCheck62_next);
        };
        int PatriarchElectProcessorUpdate_Elect64_wrapper(struct PatriarchElectProcessor* _this)
        {
           return PatriarchElectProcessorUpdate_Elect64_user(_this, PatriarchElectProcessorUpdate_Elect64_next);
        };
        
        void PatriarchElectProcessordtor_PatriarchElectProcessor68_wrapper(struct PatriarchElectProcessor* _this)
        {
           PatriarchElectProcessordtor_PatriarchElectProcessor68_user(_this, PatriarchElectProcessordtor_PatriarchElectProcessor68_next);
        };
        
        ::std::array<hook_record, 33> PatriarchElectProcessor_functions = 
        {
            _hook_record {
                (LPVOID)0x1402bc120L,
                (LPVOID *)&PatriarchElectProcessorCheatClearPatriarch2_user,
                (LPVOID *)&PatriarchElectProcessorCheatClearPatriarch2_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorCheatClearPatriarch2_wrapper),
                (LPVOID)cast_pointer_function((bool(PatriarchElectProcessor::*)())&PatriarchElectProcessor::CheatClearPatriarch)
            },
            _hook_record {
                (LPVOID)0x1402bc3c0L,
                (LPVOID *)&PatriarchElectProcessorCheatSetPatriarch4_user,
                (LPVOID *)&PatriarchElectProcessorCheatSetPatriarch4_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorCheatSetPatriarch4_wrapper),
                (LPVOID)cast_pointer_function((bool(PatriarchElectProcessor::*)(struct CPlayer*, _candidate_info::ClassType))&PatriarchElectProcessor::CheatSetPatriarch)
            },
            _hook_record {
                (LPVOID)0x1402bc7d0L,
                (LPVOID *)&PatriarchElectProcessorCompleteCheckInvalidChar6_user,
                (LPVOID *)&PatriarchElectProcessorCompleteCheckInvalidChar6_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorCompleteCheckInvalidChar6_wrapper),
                (LPVOID)cast_pointer_function((void(PatriarchElectProcessor::*)(char))&PatriarchElectProcessor::CompleteCheckInvalidChar)
            },
            _hook_record {
                (LPVOID)0x1402bb800L,
                (LPVOID *)&PatriarchElectProcessorCompleteInsertElect8_user,
                (LPVOID *)&PatriarchElectProcessorCompleteInsertElect8_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorCompleteInsertElect8_wrapper),
                (LPVOID)cast_pointer_function((void(PatriarchElectProcessor::*)())&PatriarchElectProcessor::CompleteInsertElect)
            },
            _hook_record {
                (LPVOID)0x1402bbc40L,
                (LPVOID *)&PatriarchElectProcessorCompleteInsertPatriarch10_user,
                (LPVOID *)&PatriarchElectProcessorCompleteInsertPatriarch10_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorCompleteInsertPatriarch10_wrapper),
                (LPVOID)cast_pointer_function((void(PatriarchElectProcessor::*)(struct _DB_QRY_SYN_DATA*))&PatriarchElectProcessor::CompleteInsertPatriarch)
            },
            _hook_record {
                (LPVOID)0x1402bbba0L,
                (LPVOID *)&PatriarchElectProcessorCompleteItemChargeRefund12_user,
                (LPVOID *)&PatriarchElectProcessorCompleteItemChargeRefund12_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorCompleteItemChargeRefund12_wrapper),
                (LPVOID)cast_pointer_function((void(PatriarchElectProcessor::*)(struct _DB_QRY_SYN_DATA*))&PatriarchElectProcessor::CompleteItemChargeRefund)
            },
            _hook_record {
                (LPVOID)0x1402bb9a0L,
                (LPVOID *)&PatriarchElectProcessorCompleteRequestRefund14_user,
                (LPVOID *)&PatriarchElectProcessorCompleteRequestRefund14_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorCompleteRequestRefund14_wrapper),
                (LPVOID)cast_pointer_function((void(PatriarchElectProcessor::*)(struct _DB_QRY_SYN_DATA*))&PatriarchElectProcessor::CompleteRequestRefund)
            },
            _hook_record {
                (LPVOID)0x1402bb860L,
                (LPVOID *)&PatriarchElectProcessorCompleteSelectElect16_user,
                (LPVOID *)&PatriarchElectProcessorCompleteSelectElect16_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorCompleteSelectElect16_wrapper),
                (LPVOID)cast_pointer_function((void(PatriarchElectProcessor::*)())&PatriarchElectProcessor::CompleteSelectElect)
            },
            _hook_record {
                (LPVOID)0x1402ba5a0L,
                (LPVOID *)&PatriarchElectProcessorDestroy18_user,
                (LPVOID *)&PatriarchElectProcessorDestroy18_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorDestroy18_wrapper),
                (LPVOID)cast_pointer_function((void(PatriarchElectProcessor::*)())&PatriarchElectProcessor::Destroy)
            },
            _hook_record {
                (LPVOID)0x1402badf0L,
                (LPVOID *)&PatriarchElectProcessorDoit20_user,
                (LPVOID *)&PatriarchElectProcessorDoit20_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorDoit20_wrapper),
                (LPVOID)cast_pointer_function((bool(PatriarchElectProcessor::*)(Cmd, struct CPlayer*, char*))&PatriarchElectProcessor::Doit)
            },
            _hook_record {
                (LPVOID)0x1402ba830L,
                (LPVOID *)&PatriarchElectProcessorForceChangeProcessor22_user,
                (LPVOID *)&PatriarchElectProcessorForceChangeProcessor22_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorForceChangeProcessor22_wrapper),
                (LPVOID)cast_pointer_function((bool(PatriarchElectProcessor::*)(ElectProcessor::ProcessorType))&PatriarchElectProcessor::ForceChangeProcessor)
            },
            _hook_record {
                (LPVOID)0x14007e0e0L,
                (LPVOID *)&PatriarchElectProcessorGetCurrPatriarchElectSerial24_user,
                (LPVOID *)&PatriarchElectProcessorGetCurrPatriarchElectSerial24_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorGetCurrPatriarchElectSerial24_wrapper),
                (LPVOID)cast_pointer_function((unsigned int(PatriarchElectProcessor::*)())&PatriarchElectProcessor::GetCurrPatriarchElectSerial)
            },
            _hook_record {
                (LPVOID)0x140208270L,
                (LPVOID *)&PatriarchElectProcessorGetElectSerial26_user,
                (LPVOID *)&PatriarchElectProcessorGetElectSerial26_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorGetElectSerial26_wrapper),
                (LPVOID)cast_pointer_function((unsigned int(PatriarchElectProcessor::*)())&PatriarchElectProcessor::GetElectSerial)
            },
            _hook_record {
                (LPVOID)0x140208290L,
                (LPVOID *)&PatriarchElectProcessorGetProcessorType28_user,
                (LPVOID *)&PatriarchElectProcessorGetProcessorType28_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorGetProcessorType28_wrapper),
                (LPVOID)cast_pointer_function((ElectProcessor::ProcessorType(PatriarchElectProcessor::*)())&PatriarchElectProcessor::GetProcessorType)
            },
            _hook_record {
                (LPVOID)0x1402c0510L,
                (LPVOID *)&PatriarchElectProcessorGetTimeCheck30_user,
                (LPVOID *)&PatriarchElectProcessorGetTimeCheck30_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorGetTimeCheck30_wrapper),
                (LPVOID)cast_pointer_function((bool(PatriarchElectProcessor::*)())&PatriarchElectProcessor::GetTimeCheck)
            },
            _hook_record {
                (LPVOID)0x1402ba630L,
                (LPVOID *)&PatriarchElectProcessorInitProcess32_user,
                (LPVOID *)&PatriarchElectProcessorInitProcess32_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorInitProcess32_wrapper),
                (LPVOID)cast_pointer_function((bool(PatriarchElectProcessor::*)())&PatriarchElectProcessor::InitProcess)
            },
            _hook_record {
                (LPVOID)0x1402b9f10L,
                (LPVOID *)&PatriarchElectProcessorInitialize34_user,
                (LPVOID *)&PatriarchElectProcessorInitialize34_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorInitialize34_wrapper),
                (LPVOID)cast_pointer_function((bool(PatriarchElectProcessor::*)())&PatriarchElectProcessor::Initialize)
            },
            _hook_record {
                (LPVOID)0x1402bb430L,
                (LPVOID *)&PatriarchElectProcessorInsert_Elect36_user,
                (LPVOID *)&PatriarchElectProcessorInsert_Elect36_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorInsert_Elect36_wrapper),
                (LPVOID)cast_pointer_function((int(PatriarchElectProcessor::*)())&PatriarchElectProcessor::Insert_Elect)
            },
            _hook_record {
                (LPVOID)0x1402bc040L,
                (LPVOID *)&PatriarchElectProcessorInsert_PatrirchItemChargeRefund38_user,
                (LPVOID *)&PatriarchElectProcessorInsert_PatrirchItemChargeRefund38_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorInsert_PatrirchItemChargeRefund38_wrapper),
                (LPVOID)cast_pointer_function((int(PatriarchElectProcessor::*)(char*))&PatriarchElectProcessor::Insert_PatrirchItemChargeRefund)
            },
            _hook_record {
                (LPVOID)0x1402ba4d0L,
                (LPVOID *)&PatriarchElectProcessorInstance40_user,
                (LPVOID *)&PatriarchElectProcessorInstance40_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorInstance40_wrapper),
                (LPVOID)cast_pointer_function((struct PatriarchElectProcessor*(*)())&PatriarchElectProcessor::Instance)
            },
            _hook_record {
                (LPVOID)0x1402bb290L,
                (LPVOID *)&PatriarchElectProcessorLoadDatabae42_user,
                (LPVOID *)&PatriarchElectProcessorLoadDatabae42_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorLoadDatabae42_wrapper),
                (LPVOID)cast_pointer_function((bool(PatriarchElectProcessor::*)())&PatriarchElectProcessor::LoadDatabae)
            },
            _hook_record {
                (LPVOID)0x1402bb510L,
                (LPVOID *)&PatriarchElectProcessorLoadElectState44_user,
                (LPVOID *)&PatriarchElectProcessorLoadElectState44_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorLoadElectState44_wrapper),
                (LPVOID)cast_pointer_function((bool(PatriarchElectProcessor::*)())&PatriarchElectProcessor::LoadElectState)
            },
            _hook_record {
                (LPVOID)0x1402ba770L,
                (LPVOID *)&PatriarchElectProcessorLoop46_user,
                (LPVOID *)&PatriarchElectProcessorLoop46_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorLoop46_wrapper),
                (LPVOID)cast_pointer_function((void(PatriarchElectProcessor::*)())&PatriarchElectProcessor::Loop)
            },
            _hook_record {
                (LPVOID)0x1402b9cc0L,
                (LPVOID *)&PatriarchElectProcessorctor_PatriarchElectProcessor48_user,
                (LPVOID *)&PatriarchElectProcessorctor_PatriarchElectProcessor48_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorctor_PatriarchElectProcessor48_wrapper),
                (LPVOID)cast_pointer_function((void(PatriarchElectProcessor::*)())&PatriarchElectProcessor::ctor_PatriarchElectProcessor)
            },
            _hook_record {
                (LPVOID)0x1402bc870L,
                (LPVOID *)&PatriarchElectProcessorPushDQSCheckInvalidChar50_user,
                (LPVOID *)&PatriarchElectProcessorPushDQSCheckInvalidChar50_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorPushDQSCheckInvalidChar50_wrapper),
                (LPVOID)cast_pointer_function((void(PatriarchElectProcessor::*)())&PatriarchElectProcessor::PushDQSCheckInvalidChar)
            },
            _hook_record {
                (LPVOID)0x1402bb8b0L,
                (LPVOID *)&PatriarchElectProcessorRequest_Refund52_user,
                (LPVOID *)&PatriarchElectProcessorRequest_Refund52_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorRequest_Refund52_wrapper),
                (LPVOID)cast_pointer_function((int(PatriarchElectProcessor::*)(char*))&PatriarchElectProcessor::Request_Refund)
            },
            _hook_record {
                (LPVOID)0x1402bb050L,
                (LPVOID *)&PatriarchElectProcessorSendMsg_ConnectNewUser54_user,
                (LPVOID *)&PatriarchElectProcessorSendMsg_ConnectNewUser54_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorSendMsg_ConnectNewUser54_wrapper),
                (LPVOID)cast_pointer_function((void(PatriarchElectProcessor::*)(struct CPlayer*))&PatriarchElectProcessor::SendMsg_ConnectNewUser)
            },
            _hook_record {
                (LPVOID)0x1402bafb0L,
                (LPVOID *)&PatriarchElectProcessorSendMsg_ResultCode56_user,
                (LPVOID *)&PatriarchElectProcessorSendMsg_ResultCode56_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorSendMsg_ResultCode56_wrapper),
                (LPVOID)cast_pointer_function((void(PatriarchElectProcessor::*)(unsigned int, char))&PatriarchElectProcessor::SendMsg_ResultCode)
            },
            _hook_record {
                (LPVOID)0x1404da440L,
                (LPVOID *)&PatriarchElectProcessorSetCurrPatriarchElectSerial58_user,
                (LPVOID *)&PatriarchElectProcessorSetCurrPatriarchElectSerial58_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorSetCurrPatriarchElectSerial58_wrapper),
                (LPVOID)cast_pointer_function((void(PatriarchElectProcessor::*)(unsigned int))&PatriarchElectProcessor::SetCurrPatriarchElectSerial)
            },
            _hook_record {
                (LPVOID)0x14029d6d0L,
                (LPVOID *)&PatriarchElectProcessorSetTimeCheck60_user,
                (LPVOID *)&PatriarchElectProcessorSetTimeCheck60_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorSetTimeCheck60_wrapper),
                (LPVOID)cast_pointer_function((void(PatriarchElectProcessor::*)(bool))&PatriarchElectProcessor::SetTimeCheck)
            },
            _hook_record {
                (LPVOID)0x1402baab0L,
                (LPVOID *)&PatriarchElectProcessorTimeCheck62_user,
                (LPVOID *)&PatriarchElectProcessorTimeCheck62_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorTimeCheck62_wrapper),
                (LPVOID)cast_pointer_function((void(PatriarchElectProcessor::*)(uint16_t, uint16_t))&PatriarchElectProcessor::TimeCheck)
            },
            _hook_record {
                (LPVOID)0x1402bb690L,
                (LPVOID *)&PatriarchElectProcessorUpdate_Elect64_user,
                (LPVOID *)&PatriarchElectProcessorUpdate_Elect64_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessorUpdate_Elect64_wrapper),
                (LPVOID)cast_pointer_function((int(PatriarchElectProcessor::*)())&PatriarchElectProcessor::Update_Elect)
            },
            _hook_record {
                (LPVOID)0x1402b9df0L,
                (LPVOID *)&PatriarchElectProcessordtor_PatriarchElectProcessor68_user,
                (LPVOID *)&PatriarchElectProcessordtor_PatriarchElectProcessor68_next,
                (LPVOID)cast_pointer_function(PatriarchElectProcessordtor_PatriarchElectProcessor68_wrapper),
                (LPVOID)cast_pointer_function((void(PatriarchElectProcessor::*)())&PatriarchElectProcessor::dtor_PatriarchElectProcessor)
            },
        };
    }; // end namespace Detail
END_ATF_NAMESPACE
