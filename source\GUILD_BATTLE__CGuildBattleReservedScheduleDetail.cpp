#include <GUILD_BATTLE__CGuildBattleReservedScheduleDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Detail
        {
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleAdd2_ptr GUILD_BATTLE__CGuildBattleReservedScheduleAdd2_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleAdd2_clbk GUILD_BATTLE__CGuildBattleReservedScheduleAdd2_user(nullptr);
            
            
            Info::GUILD_BATTLE__CGuildBattleReservedSchedulector_CGuildBattleReservedSchedule4_ptr GUILD_BATTLE__CGuildBattleReservedSchedulector_CGuildBattleReservedSchedule4_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedSchedulector_CGuildBattleReservedSchedule4_clbk GUILD_BATTLE__CGuildBattleReservedSchedulector_CGuildBattleReservedSchedule4_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleCheckNextEvent6_ptr GUILD_BATTLE__CGuildBattleReservedScheduleCheckNextEvent6_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleCheckNextEvent6_clbk GUILD_BATTLE__CGuildBattleReservedScheduleCheckNextEvent6_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleCleanUpDanglingReservedSchedule8_ptr GUILD_BATTLE__CGuildBattleReservedScheduleCleanUpDanglingReservedSchedule8_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleCleanUpDanglingReservedSchedule8_clbk GUILD_BATTLE__CGuildBattleReservedScheduleCleanUpDanglingReservedSchedule8_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleClear10_ptr GUILD_BATTLE__CGuildBattleReservedScheduleClear10_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleClear10_clbk GUILD_BATTLE__CGuildBattleReservedScheduleClear10_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleClear12_ptr GUILD_BATTLE__CGuildBattleReservedScheduleClear12_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleClear12_clbk GUILD_BATTLE__CGuildBattleReservedScheduleClear12_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleClearElapsedSchedule14_ptr GUILD_BATTLE__CGuildBattleReservedScheduleClearElapsedSchedule14_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleClearElapsedSchedule14_clbk GUILD_BATTLE__CGuildBattleReservedScheduleClearElapsedSchedule14_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleCopyUseTimeField16_ptr GUILD_BATTLE__CGuildBattleReservedScheduleCopyUseTimeField16_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleCopyUseTimeField16_clbk GUILD_BATTLE__CGuildBattleReservedScheduleCopyUseTimeField16_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleFlip18_ptr GUILD_BATTLE__CGuildBattleReservedScheduleFlip18_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleFlip18_clbk GUILD_BATTLE__CGuildBattleReservedScheduleFlip18_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleGetCurScheduleID20_ptr GUILD_BATTLE__CGuildBattleReservedScheduleGetCurScheduleID20_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleGetCurScheduleID20_clbk GUILD_BATTLE__CGuildBattleReservedScheduleGetCurScheduleID20_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleGetID22_ptr GUILD_BATTLE__CGuildBattleReservedScheduleGetID22_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleGetID22_clbk GUILD_BATTLE__CGuildBattleReservedScheduleGetID22_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleIsDone24_ptr GUILD_BATTLE__CGuildBattleReservedScheduleIsDone24_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleIsDone24_clbk GUILD_BATTLE__CGuildBattleReservedScheduleIsDone24_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleIsEmptyTime26_ptr GUILD_BATTLE__CGuildBattleReservedScheduleIsEmptyTime26_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleIsEmptyTime26_clbk GUILD_BATTLE__CGuildBattleReservedScheduleIsEmptyTime26_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleLoad28_ptr GUILD_BATTLE__CGuildBattleReservedScheduleLoad28_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleLoad28_clbk GUILD_BATTLE__CGuildBattleReservedScheduleLoad28_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleLoop30_ptr GUILD_BATTLE__CGuildBattleReservedScheduleLoop30_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleLoop30_clbk GUILD_BATTLE__CGuildBattleReservedScheduleLoop30_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleNext32_ptr GUILD_BATTLE__CGuildBattleReservedScheduleNext32_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleNext32_clbk GUILD_BATTLE__CGuildBattleReservedScheduleNext32_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseField34_ptr GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseField34_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseField34_clbk GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseField34_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseFlag36_ptr GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseFlag36_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseFlag36_clbk GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseFlag36_user(nullptr);
            
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduledtor_CGuildBattleReservedSchedule42_ptr GUILD_BATTLE__CGuildBattleReservedScheduledtor_CGuildBattleReservedSchedule42_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduledtor_CGuildBattleReservedSchedule42_clbk GUILD_BATTLE__CGuildBattleReservedScheduledtor_CGuildBattleReservedSchedule42_user(nullptr);
            
            char GUILD_BATTLE__CGuildBattleReservedScheduleAdd2_wrapper(struct GUILD_BATTLE::CGuildBattleReservedSchedule* _this, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt, struct GUILD_BATTLE::CGuildBattleSchedule** ppkSchedule)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleAdd2_user(_this, dwStartTimeInx, dwElapseTimeCnt, ppkSchedule, GUILD_BATTLE__CGuildBattleReservedScheduleAdd2_next);
            };
            
            void GUILD_BATTLE__CGuildBattleReservedSchedulector_CGuildBattleReservedSchedule4_wrapper(struct GUILD_BATTLE::CGuildBattleReservedSchedule* _this, unsigned int uiScheduleListID)
            {
               GUILD_BATTLE__CGuildBattleReservedSchedulector_CGuildBattleReservedSchedule4_user(_this, uiScheduleListID, GUILD_BATTLE__CGuildBattleReservedSchedulector_CGuildBattleReservedSchedule4_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleCheckNextEvent6_wrapper(struct GUILD_BATTLE::CGuildBattleReservedSchedule* _this, int iRet)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleCheckNextEvent6_user(_this, iRet, GUILD_BATTLE__CGuildBattleReservedScheduleCheckNextEvent6_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleCleanUpDanglingReservedSchedule8_wrapper(struct GUILD_BATTLE::CGuildBattleReservedSchedule* _this)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleCleanUpDanglingReservedSchedule8_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleCleanUpDanglingReservedSchedule8_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleClear10_wrapper(struct GUILD_BATTLE::CGuildBattleReservedSchedule* _this, unsigned int dwID)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleClear10_user(_this, dwID, GUILD_BATTLE__CGuildBattleReservedScheduleClear10_next);
            };
            void GUILD_BATTLE__CGuildBattleReservedScheduleClear12_wrapper(struct GUILD_BATTLE::CGuildBattleReservedSchedule* _this)
            {
               GUILD_BATTLE__CGuildBattleReservedScheduleClear12_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleClear12_next);
            };
            void GUILD_BATTLE__CGuildBattleReservedScheduleClearElapsedSchedule14_wrapper(struct GUILD_BATTLE::CGuildBattleReservedSchedule* _this)
            {
               GUILD_BATTLE__CGuildBattleReservedScheduleClearElapsedSchedule14_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleClearElapsedSchedule14_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleCopyUseTimeField16_wrapper(struct GUILD_BATTLE::CGuildBattleReservedSchedule* _this, bool* pbField)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleCopyUseTimeField16_user(_this, pbField, GUILD_BATTLE__CGuildBattleReservedScheduleCopyUseTimeField16_next);
            };
            void GUILD_BATTLE__CGuildBattleReservedScheduleFlip18_wrapper(struct GUILD_BATTLE::CGuildBattleReservedSchedule* _this)
            {
               GUILD_BATTLE__CGuildBattleReservedScheduleFlip18_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleFlip18_next);
            };
            unsigned int GUILD_BATTLE__CGuildBattleReservedScheduleGetCurScheduleID20_wrapper(struct GUILD_BATTLE::CGuildBattleReservedSchedule* _this)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleGetCurScheduleID20_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleGetCurScheduleID20_next);
            };
            unsigned int GUILD_BATTLE__CGuildBattleReservedScheduleGetID22_wrapper(struct GUILD_BATTLE::CGuildBattleReservedSchedule* _this)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleGetID22_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleGetID22_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleIsDone24_wrapper(struct GUILD_BATTLE::CGuildBattleReservedSchedule* _this)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleIsDone24_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleIsDone24_next);
            };
            char GUILD_BATTLE__CGuildBattleReservedScheduleIsEmptyTime26_wrapper(struct GUILD_BATTLE::CGuildBattleReservedSchedule* _this, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleIsEmptyTime26_user(_this, dwStartTimeInx, dwElapseTimeCnt, GUILD_BATTLE__CGuildBattleReservedScheduleIsEmptyTime26_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleLoad28_wrapper(struct GUILD_BATTLE::CGuildBattleReservedSchedule* _this, bool bToday, struct _worlddb_guild_battle_schedule_list* pkInfo)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleLoad28_user(_this, bToday, pkInfo, GUILD_BATTLE__CGuildBattleReservedScheduleLoad28_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleLoop30_wrapper(struct GUILD_BATTLE::CGuildBattleReservedSchedule* _this)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleLoop30_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleLoop30_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleNext32_wrapper(struct GUILD_BATTLE::CGuildBattleReservedSchedule* _this)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleNext32_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleNext32_next);
            };
            void GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseField34_wrapper(struct GUILD_BATTLE::CGuildBattleReservedSchedule* _this, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt)
            {
               GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseField34_user(_this, dwStartTimeInx, dwElapseTimeCnt, GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseField34_next);
            };
            struct GUILD_BATTLE::CGuildBattleSchedule* GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseFlag36_wrapper(struct GUILD_BATTLE::CGuildBattleReservedSchedule* _this, unsigned int dwID)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseFlag36_user(_this, dwID, GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseFlag36_next);
            };
            
            void GUILD_BATTLE__CGuildBattleReservedScheduledtor_CGuildBattleReservedSchedule42_wrapper(struct GUILD_BATTLE::CGuildBattleReservedSchedule* _this)
            {
               GUILD_BATTLE__CGuildBattleReservedScheduledtor_CGuildBattleReservedSchedule42_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduledtor_CGuildBattleReservedSchedule42_next);
            };
            
            ::std::array<hook_record, 19> CGuildBattleReservedSchedule_functions = 
            {
                _hook_record {
                    (LPVOID)0x1403dac40L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleAdd2_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleAdd2_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleAdd2_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CGuildBattleReservedSchedule::*)(unsigned int, unsigned int, struct GUILD_BATTLE::CGuildBattleSchedule**))&GUILD_BATTLE::CGuildBattleReservedSchedule::Add)
                },
                _hook_record {
                    (LPVOID)0x1403dab60L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedSchedulector_CGuildBattleReservedSchedule4_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedSchedulector_CGuildBattleReservedSchedule4_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedSchedulector_CGuildBattleReservedSchedule4_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleReservedSchedule::*)(unsigned int))&GUILD_BATTLE::CGuildBattleReservedSchedule::ctor_CGuildBattleReservedSchedule)
                },
                _hook_record {
                    (LPVOID)0x1403db750L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleCheckNextEvent6_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleCheckNextEvent6_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleCheckNextEvent6_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedSchedule::*)(int))&GUILD_BATTLE::CGuildBattleReservedSchedule::CheckNextEvent)
                },
                _hook_record {
                    (LPVOID)0x1403db570L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleCleanUpDanglingReservedSchedule8_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleCleanUpDanglingReservedSchedule8_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleCleanUpDanglingReservedSchedule8_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedSchedule::*)())&GUILD_BATTLE::CGuildBattleReservedSchedule::CleanUpDanglingReservedSchedule)
                },
                _hook_record {
                    (LPVOID)0x1403db420L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleClear10_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleClear10_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleClear10_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedSchedule::*)(unsigned int))&GUILD_BATTLE::CGuildBattleReservedSchedule::Clear)
                },
                _hook_record {
                    (LPVOID)0x1403dae20L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleClear12_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleClear12_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleClear12_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleReservedSchedule::*)())&GUILD_BATTLE::CGuildBattleReservedSchedule::Clear)
                },
                _hook_record {
                    (LPVOID)0x1403db950L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleClearElapsedSchedule14_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleClearElapsedSchedule14_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleClearElapsedSchedule14_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleReservedSchedule::*)())&GUILD_BATTLE::CGuildBattleReservedSchedule::ClearElapsedSchedule)
                },
                _hook_record {
                    (LPVOID)0x1403dad20L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleCopyUseTimeField16_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleCopyUseTimeField16_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleCopyUseTimeField16_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedSchedule::*)(bool*))&GUILD_BATTLE::CGuildBattleReservedSchedule::CopyUseTimeField)
                },
                _hook_record {
                    (LPVOID)0x1403db4b0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleFlip18_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleFlip18_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleFlip18_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleReservedSchedule::*)())&GUILD_BATTLE::CGuildBattleReservedSchedule::Flip)
                },
                _hook_record {
                    (LPVOID)0x1403db640L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleGetCurScheduleID20_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleGetCurScheduleID20_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleGetCurScheduleID20_wrapper),
                    (LPVOID)cast_pointer_function((unsigned int(GUILD_BATTLE::CGuildBattleReservedSchedule::*)())&GUILD_BATTLE::CGuildBattleReservedSchedule::GetCurScheduleID)
                },
                _hook_record {
                    (LPVOID)0x1403dec40L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleGetID22_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleGetID22_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleGetID22_wrapper),
                    (LPVOID)cast_pointer_function((unsigned int(GUILD_BATTLE::CGuildBattleReservedSchedule::*)())&GUILD_BATTLE::CGuildBattleReservedSchedule::GetID)
                },
                _hook_record {
                    (LPVOID)0x1403dec60L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleIsDone24_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleIsDone24_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleIsDone24_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedSchedule::*)())&GUILD_BATTLE::CGuildBattleReservedSchedule::IsDone)
                },
                _hook_record {
                    (LPVOID)0x1403dabc0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleIsEmptyTime26_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleIsEmptyTime26_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleIsEmptyTime26_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CGuildBattleReservedSchedule::*)(unsigned int, unsigned int))&GUILD_BATTLE::CGuildBattleReservedSchedule::IsEmptyTime)
                },
                _hook_record {
                    (LPVOID)0x1403daea0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleLoad28_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleLoad28_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleLoad28_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedSchedule::*)(bool, struct _worlddb_guild_battle_schedule_list*))&GUILD_BATTLE::CGuildBattleReservedSchedule::Load)
                },
                _hook_record {
                    (LPVOID)0x1403dad80L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleLoop30_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleLoop30_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleLoop30_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedSchedule::*)())&GUILD_BATTLE::CGuildBattleReservedSchedule::Loop)
                },
                _hook_record {
                    (LPVOID)0x1403db7f0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleNext32_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleNext32_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleNext32_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedSchedule::*)())&GUILD_BATTLE::CGuildBattleReservedSchedule::Next)
                },
                _hook_record {
                    (LPVOID)0x1403db8d0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseField34_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseField34_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseField34_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleReservedSchedule::*)(unsigned int, unsigned int))&GUILD_BATTLE::CGuildBattleReservedSchedule::UpdateUseField)
                },
                _hook_record {
                    (LPVOID)0x1403db4f0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseFlag36_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseFlag36_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleUpdateUseFlag36_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CGuildBattleSchedule*(GUILD_BATTLE::CGuildBattleReservedSchedule::*)(unsigned int))&GUILD_BATTLE::CGuildBattleReservedSchedule::UpdateUseFlag)
                },
                _hook_record {
                    (LPVOID)0x1403dabb0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduledtor_CGuildBattleReservedSchedule42_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduledtor_CGuildBattleReservedSchedule42_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduledtor_CGuildBattleReservedSchedule42_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleReservedSchedule::*)())&GUILD_BATTLE::CGuildBattleReservedSchedule::dtor_CGuildBattleReservedSchedule)
                },
            };
        }; // end namespace Detail
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE
