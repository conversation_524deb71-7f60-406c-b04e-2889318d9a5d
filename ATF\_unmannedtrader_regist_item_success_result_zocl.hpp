// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _unmannedtrader_regist_item_success_result_zocl
    {
        unsigned __int16 wItemSerial;
        unsigned int dwPrice;
        unsigned int dwRegedSerial;
        unsigned int dwListIndex;
        unsigned int dwTax;
        unsigned int dwLeftDalant;
        unsigned __int16 wSepaSerial;
        char bySepaAmount;
    public:
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_unmannedtrader_regist_item_success_result_zocl, 25>(), "_unmannedtrader_regist_item_success_result_zocl");
END_ATF_NAMESPACE
