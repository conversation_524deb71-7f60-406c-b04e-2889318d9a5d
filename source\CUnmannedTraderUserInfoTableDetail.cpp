#include <CUnmannedTraderUserInfoTableDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace Detail
    {
        Info::CUnmannedTraderUserInfoTableBuy2_ptr CUnmannedTraderUserInfoTableBuy2_next(nullptr);
        Info::CUnmannedTraderUserInfoTableBuy2_clbk CUnmannedTraderUserInfoTableBuy2_user(nullptr);
        
        
        Info::CUnmannedTraderUserInfoTablector_CUnmannedTraderUserInfoTable4_ptr CUnmannedTraderUserInfoTablector_CUnmannedTraderUserInfoTable4_next(nullptr);
        Info::CUnmannedTraderUserInfoTablector_CUnmannedTraderUserInfoTable4_clbk CUnmannedTraderUserInfoTablector_CUnmannedTraderUserInfoTable4_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableCancelRegist6_ptr CUnmannedTraderUserInfoTableCancelRegist6_next(nullptr);
        Info::CUnmannedTraderUserInfoTableCancelRegist6_clbk CUnmannedTraderUserInfoTableCancelRegist6_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableCheatCancelRegist8_ptr CUnmannedTraderUserInfoTableCheatCancelRegist8_next(nullptr);
        Info::CUnmannedTraderUserInfoTableCheatCancelRegist8_clbk CUnmannedTraderUserInfoTableCheatCancelRegist8_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableCheckwIndexAndType10_ptr CUnmannedTraderUserInfoTableCheckwIndexAndType10_next(nullptr);
        Info::CUnmannedTraderUserInfoTableCheckwIndexAndType10_clbk CUnmannedTraderUserInfoTableCheckwIndexAndType10_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableClearLogLogOutState12_ptr CUnmannedTraderUserInfoTableClearLogLogOutState12_next(nullptr);
        Info::CUnmannedTraderUserInfoTableClearLogLogOutState12_clbk CUnmannedTraderUserInfoTableClearLogLogOutState12_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableClearRequest14_ptr CUnmannedTraderUserInfoTableClearRequest14_next(nullptr);
        Info::CUnmannedTraderUserInfoTableClearRequest14_clbk CUnmannedTraderUserInfoTableClearRequest14_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableCompleteBuy16_ptr CUnmannedTraderUserInfoTableCompleteBuy16_next(nullptr);
        Info::CUnmannedTraderUserInfoTableCompleteBuy16_clbk CUnmannedTraderUserInfoTableCompleteBuy16_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableCompleteCancelRegist18_ptr CUnmannedTraderUserInfoTableCompleteCancelRegist18_next(nullptr);
        Info::CUnmannedTraderUserInfoTableCompleteCancelRegist18_clbk CUnmannedTraderUserInfoTableCompleteCancelRegist18_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableCompleteCreate20_ptr CUnmannedTraderUserInfoTableCompleteCreate20_next(nullptr);
        Info::CUnmannedTraderUserInfoTableCompleteCreate20_clbk CUnmannedTraderUserInfoTableCompleteCreate20_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableCompleteReRegist22_ptr CUnmannedTraderUserInfoTableCompleteReRegist22_next(nullptr);
        Info::CUnmannedTraderUserInfoTableCompleteReRegist22_clbk CUnmannedTraderUserInfoTableCompleteReRegist22_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableCompleteReRegistRollBack24_ptr CUnmannedTraderUserInfoTableCompleteReRegistRollBack24_next(nullptr);
        Info::CUnmannedTraderUserInfoTableCompleteReRegistRollBack24_clbk CUnmannedTraderUserInfoTableCompleteReRegistRollBack24_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableCompleteRegist26_ptr CUnmannedTraderUserInfoTableCompleteRegist26_next(nullptr);
        Info::CUnmannedTraderUserInfoTableCompleteRegist26_clbk CUnmannedTraderUserInfoTableCompleteRegist26_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableCompleteReprice28_ptr CUnmannedTraderUserInfoTableCompleteReprice28_next(nullptr);
        Info::CUnmannedTraderUserInfoTableCompleteReprice28_clbk CUnmannedTraderUserInfoTableCompleteReprice28_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableCompleteSearch30_ptr CUnmannedTraderUserInfoTableCompleteSearch30_next(nullptr);
        Info::CUnmannedTraderUserInfoTableCompleteSearch30_clbk CUnmannedTraderUserInfoTableCompleteSearch30_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableCompleteTimeOutClear32_ptr CUnmannedTraderUserInfoTableCompleteTimeOutClear32_next(nullptr);
        Info::CUnmannedTraderUserInfoTableCompleteTimeOutClear32_clbk CUnmannedTraderUserInfoTableCompleteTimeOutClear32_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableCompleteUpdateCheatRegistTime34_ptr CUnmannedTraderUserInfoTableCompleteUpdateCheatRegistTime34_next(nullptr);
        Info::CUnmannedTraderUserInfoTableCompleteUpdateCheatRegistTime34_clbk CUnmannedTraderUserInfoTableCompleteUpdateCheatRegistTime34_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableCompleteUpdateState36_ptr CUnmannedTraderUserInfoTableCompleteUpdateState36_next(nullptr);
        Info::CUnmannedTraderUserInfoTableCompleteUpdateState36_clbk CUnmannedTraderUserInfoTableCompleteUpdateState36_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableDestroy38_ptr CUnmannedTraderUserInfoTableDestroy38_next(nullptr);
        Info::CUnmannedTraderUserInfoTableDestroy38_clbk CUnmannedTraderUserInfoTableDestroy38_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableFind40_ptr CUnmannedTraderUserInfoTableFind40_next(nullptr);
        Info::CUnmannedTraderUserInfoTableFind40_clbk CUnmannedTraderUserInfoTableFind40_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableFindByIndex42_ptr CUnmannedTraderUserInfoTableFindByIndex42_next(nullptr);
        Info::CUnmannedTraderUserInfoTableFindByIndex42_clbk CUnmannedTraderUserInfoTableFindByIndex42_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableFindUser44_ptr CUnmannedTraderUserInfoTableFindUser44_next(nullptr);
        Info::CUnmannedTraderUserInfoTableFindUser44_clbk CUnmannedTraderUserInfoTableFindUser44_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableGetCloseItemForPassTimeUpdateInfo46_ptr CUnmannedTraderUserInfoTableGetCloseItemForPassTimeUpdateInfo46_next(nullptr);
        Info::CUnmannedTraderUserInfoTableGetCloseItemForPassTimeUpdateInfo46_clbk CUnmannedTraderUserInfoTableGetCloseItemForPassTimeUpdateInfo46_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableGetMaxRegistCnt48_ptr CUnmannedTraderUserInfoTableGetMaxRegistCnt48_next(nullptr);
        Info::CUnmannedTraderUserInfoTableGetMaxRegistCnt48_clbk CUnmannedTraderUserInfoTableGetMaxRegistCnt48_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableGetRegItemInfo50_ptr CUnmannedTraderUserInfoTableGetRegItemInfo50_next(nullptr);
        Info::CUnmannedTraderUserInfoTableGetRegItemInfo50_clbk CUnmannedTraderUserInfoTableGetRegItemInfo50_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableInit52_ptr CUnmannedTraderUserInfoTableInit52_next(nullptr);
        Info::CUnmannedTraderUserInfoTableInit52_clbk CUnmannedTraderUserInfoTableInit52_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableInstance54_ptr CUnmannedTraderUserInfoTableInstance54_next(nullptr);
        Info::CUnmannedTraderUserInfoTableInstance54_clbk CUnmannedTraderUserInfoTableInstance54_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableLoad56_ptr CUnmannedTraderUserInfoTableLoad56_next(nullptr);
        Info::CUnmannedTraderUserInfoTableLoad56_clbk CUnmannedTraderUserInfoTableLoad56_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableLog58_ptr CUnmannedTraderUserInfoTableLog58_next(nullptr);
        Info::CUnmannedTraderUserInfoTableLog58_clbk CUnmannedTraderUserInfoTableLog58_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableLogOut60_ptr CUnmannedTraderUserInfoTableLogOut60_next(nullptr);
        Info::CUnmannedTraderUserInfoTableLogOut60_clbk CUnmannedTraderUserInfoTableLogOut60_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableModifyPrice62_ptr CUnmannedTraderUserInfoTableModifyPrice62_next(nullptr);
        Info::CUnmannedTraderUserInfoTableModifyPrice62_clbk CUnmannedTraderUserInfoTableModifyPrice62_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTablePushUpdateBuyRollBack64_ptr CUnmannedTraderUserInfoTablePushUpdateBuyRollBack64_next(nullptr);
        Info::CUnmannedTraderUserInfoTablePushUpdateBuyRollBack64_clbk CUnmannedTraderUserInfoTablePushUpdateBuyRollBack64_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableReRegist66_ptr CUnmannedTraderUserInfoTableReRegist66_next(nullptr);
        Info::CUnmannedTraderUserInfoTableReRegist66_clbk CUnmannedTraderUserInfoTableReRegist66_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableRegist68_ptr CUnmannedTraderUserInfoTableRegist68_next(nullptr);
        Info::CUnmannedTraderUserInfoTableRegist68_clbk CUnmannedTraderUserInfoTableRegist68_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableSearch70_ptr CUnmannedTraderUserInfoTableSearch70_next(nullptr);
        Info::CUnmannedTraderUserInfoTableSearch70_clbk CUnmannedTraderUserInfoTableSearch70_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableServiceLog72_ptr CUnmannedTraderUserInfoTableServiceLog72_next(nullptr);
        Info::CUnmannedTraderUserInfoTableServiceLog72_clbk CUnmannedTraderUserInfoTableServiceLog72_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableSetLogger74_ptr CUnmannedTraderUserInfoTableSetLogger74_next(nullptr);
        Info::CUnmannedTraderUserInfoTableSetLogger74_clbk CUnmannedTraderUserInfoTableSetLogger74_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableSubCompleteBuyFindBuyer76_ptr CUnmannedTraderUserInfoTableSubCompleteBuyFindBuyer76_next(nullptr);
        Info::CUnmannedTraderUserInfoTableSubCompleteBuyFindBuyer76_clbk CUnmannedTraderUserInfoTableSubCompleteBuyFindBuyer76_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableSubCompleteBuyIncreaseVesion78_ptr CUnmannedTraderUserInfoTableSubCompleteBuyIncreaseVesion78_next(nullptr);
        Info::CUnmannedTraderUserInfoTableSubCompleteBuyIncreaseVesion78_clbk CUnmannedTraderUserInfoTableSubCompleteBuyIncreaseVesion78_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableSubCompleteBuyProcBuy80_ptr CUnmannedTraderUserInfoTableSubCompleteBuyProcBuy80_next(nullptr);
        Info::CUnmannedTraderUserInfoTableSubCompleteBuyProcBuy80_clbk CUnmannedTraderUserInfoTableSubCompleteBuyProcBuy80_user(nullptr);
        
        Info::CUnmannedTraderUserInfoTableSubCompleteBuyProcBuyResult82_ptr CUnmannedTraderUserInfoTableSubCompleteBuyProcBuyResult82_next(nullptr);
        Info::CUnmannedTraderUserInfoTableSubCompleteBuyProcBuyResult82_clbk CUnmannedTraderUserInfoTableSubCompleteBuyProcBuyResult82_user(nullptr);
        
        
        Info::CUnmannedTraderUserInfoTabledtor_CUnmannedTraderUserInfoTable86_ptr CUnmannedTraderUserInfoTabledtor_CUnmannedTraderUserInfoTable86_next(nullptr);
        Info::CUnmannedTraderUserInfoTabledtor_CUnmannedTraderUserInfoTable86_clbk CUnmannedTraderUserInfoTabledtor_CUnmannedTraderUserInfoTable86_user(nullptr);
        
        void CUnmannedTraderUserInfoTableBuy2_wrapper(struct CUnmannedTraderUserInfoTable* _this, uint16_t wInx, char byType, struct _unmannedtrader_buy_item_request_clzo* pRequest)
        {
           CUnmannedTraderUserInfoTableBuy2_user(_this, wInx, byType, pRequest, CUnmannedTraderUserInfoTableBuy2_next);
        };
        
        void CUnmannedTraderUserInfoTablector_CUnmannedTraderUserInfoTable4_wrapper(struct CUnmannedTraderUserInfoTable* _this)
        {
           CUnmannedTraderUserInfoTablector_CUnmannedTraderUserInfoTable4_user(_this, CUnmannedTraderUserInfoTablector_CUnmannedTraderUserInfoTable4_next);
        };
        void CUnmannedTraderUserInfoTableCancelRegist6_wrapper(struct CUnmannedTraderUserInfoTable* _this, uint16_t wInx, char byType, struct _a_trade_clear_item_request_clzo* pRequest)
        {
           CUnmannedTraderUserInfoTableCancelRegist6_user(_this, wInx, byType, pRequest, CUnmannedTraderUserInfoTableCancelRegist6_next);
        };
        bool CUnmannedTraderUserInfoTableCheatCancelRegist8_wrapper(struct CUnmannedTraderUserInfoTable* _this, uint16_t wInx, unsigned int dwOwnerSerial, char byNth)
        {
           return CUnmannedTraderUserInfoTableCheatCancelRegist8_user(_this, wInx, dwOwnerSerial, byNth, CUnmannedTraderUserInfoTableCheatCancelRegist8_next);
        };
        bool CUnmannedTraderUserInfoTableCheckwIndexAndType10_wrapper(struct CUnmannedTraderUserInfoTable* _this, uint16_t wInx, char byType, char* szCallFuncName)
        {
           return CUnmannedTraderUserInfoTableCheckwIndexAndType10_user(_this, wInx, byType, szCallFuncName, CUnmannedTraderUserInfoTableCheckwIndexAndType10_next);
        };
        void CUnmannedTraderUserInfoTableClearLogLogOutState12_wrapper(struct CUnmannedTraderUserInfoTable* _this, char* szType, unsigned int dwOwner, char* szAccount, char* wszName, unsigned int dwRegistSerial, unsigned int dwK, unsigned int dwD, unsigned int dwU)
        {
           CUnmannedTraderUserInfoTableClearLogLogOutState12_user(_this, szType, dwOwner, szAccount, wszName, dwRegistSerial, dwK, dwD, dwU, CUnmannedTraderUserInfoTableClearLogLogOutState12_next);
        };
        void CUnmannedTraderUserInfoTableClearRequest14_wrapper(struct CUnmannedTraderUserInfoTable* _this, uint16_t wInx, unsigned int dwOwner)
        {
           CUnmannedTraderUserInfoTableClearRequest14_user(_this, wInx, dwOwner, CUnmannedTraderUserInfoTableClearRequest14_next);
        };
        void CUnmannedTraderUserInfoTableCompleteBuy16_wrapper(struct CUnmannedTraderUserInfoTable* _this, char byRet, char* pLoadData, struct CUnmannedTraderTradeInfo* pkTaradInfo)
        {
           CUnmannedTraderUserInfoTableCompleteBuy16_user(_this, byRet, pLoadData, pkTaradInfo, CUnmannedTraderUserInfoTableCompleteBuy16_next);
        };
        void CUnmannedTraderUserInfoTableCompleteCancelRegist18_wrapper(struct CUnmannedTraderUserInfoTable* _this, char byRet, char* pLoadData)
        {
           CUnmannedTraderUserInfoTableCompleteCancelRegist18_user(_this, byRet, pLoadData, CUnmannedTraderUserInfoTableCompleteCancelRegist18_next);
        };
        void CUnmannedTraderUserInfoTableCompleteCreate20_wrapper(struct CUnmannedTraderUserInfoTable* _this, uint16_t wInx)
        {
           CUnmannedTraderUserInfoTableCompleteCreate20_user(_this, wInx, CUnmannedTraderUserInfoTableCompleteCreate20_next);
        };
        void CUnmannedTraderUserInfoTableCompleteReRegist22_wrapper(struct CUnmannedTraderUserInfoTable* _this, char* pLoadData)
        {
           CUnmannedTraderUserInfoTableCompleteReRegist22_user(_this, pLoadData, CUnmannedTraderUserInfoTableCompleteReRegist22_next);
        };
        void CUnmannedTraderUserInfoTableCompleteReRegistRollBack24_wrapper(struct CUnmannedTraderUserInfoTable* _this, uint16_t wInx, unsigned int dwOwnerSerial, char* pData)
        {
           CUnmannedTraderUserInfoTableCompleteReRegistRollBack24_user(_this, wInx, dwOwnerSerial, pData, CUnmannedTraderUserInfoTableCompleteReRegistRollBack24_next);
        };
        void CUnmannedTraderUserInfoTableCompleteRegist26_wrapper(struct CUnmannedTraderUserInfoTable* _this, char byRet, char* pLoadData)
        {
           CUnmannedTraderUserInfoTableCompleteRegist26_user(_this, byRet, pLoadData, CUnmannedTraderUserInfoTableCompleteRegist26_next);
        };
        void CUnmannedTraderUserInfoTableCompleteReprice28_wrapper(struct CUnmannedTraderUserInfoTable* _this, char byRet, char* pLoadData)
        {
           CUnmannedTraderUserInfoTableCompleteReprice28_user(_this, byRet, pLoadData, CUnmannedTraderUserInfoTableCompleteReprice28_next);
        };
        void CUnmannedTraderUserInfoTableCompleteSearch30_wrapper(struct CUnmannedTraderUserInfoTable* _this, char byDBRet, char byProcRet, char* pLoadData)
        {
           CUnmannedTraderUserInfoTableCompleteSearch30_user(_this, byDBRet, byProcRet, pLoadData, CUnmannedTraderUserInfoTableCompleteSearch30_next);
        };
        void CUnmannedTraderUserInfoTableCompleteTimeOutClear32_wrapper(struct CUnmannedTraderUserInfoTable* _this, char* pLoadData)
        {
           CUnmannedTraderUserInfoTableCompleteTimeOutClear32_user(_this, pLoadData, CUnmannedTraderUserInfoTableCompleteTimeOutClear32_next);
        };
        void CUnmannedTraderUserInfoTableCompleteUpdateCheatRegistTime34_wrapper(struct CUnmannedTraderUserInfoTable* _this, char* pLoadData)
        {
           CUnmannedTraderUserInfoTableCompleteUpdateCheatRegistTime34_user(_this, pLoadData, CUnmannedTraderUserInfoTableCompleteUpdateCheatRegistTime34_next);
        };
        bool CUnmannedTraderUserInfoTableCompleteUpdateState36_wrapper(struct CUnmannedTraderUserInfoTable* _this, unsigned int dwOwnerSerial, unsigned int dwRegistSerial, char byState)
        {
           return CUnmannedTraderUserInfoTableCompleteUpdateState36_user(_this, dwOwnerSerial, dwRegistSerial, byState, CUnmannedTraderUserInfoTableCompleteUpdateState36_next);
        };
        void CUnmannedTraderUserInfoTableDestroy38_wrapper()
        {
           CUnmannedTraderUserInfoTableDestroy38_user(CUnmannedTraderUserInfoTableDestroy38_next);
        };
        struct CUnmannedTraderUserInfo* CUnmannedTraderUserInfoTableFind40_wrapper(struct CUnmannedTraderUserInfoTable* _this, unsigned int dwSerial)
        {
           return CUnmannedTraderUserInfoTableFind40_user(_this, dwSerial, CUnmannedTraderUserInfoTableFind40_next);
        };
        struct CUnmannedTraderUserInfo* CUnmannedTraderUserInfoTableFindByIndex42_wrapper(struct CUnmannedTraderUserInfoTable* _this, uint16_t wInx)
        {
           return CUnmannedTraderUserInfoTableFindByIndex42_user(_this, wInx, CUnmannedTraderUserInfoTableFindByIndex42_next);
        };
        struct CUnmannedTraderUserInfo* CUnmannedTraderUserInfoTableFindUser44_wrapper(struct CUnmannedTraderUserInfoTable* _this, uint16_t wInx, unsigned int dwSerial)
        {
           return CUnmannedTraderUserInfoTableFindUser44_user(_this, wInx, dwSerial, CUnmannedTraderUserInfoTableFindUser44_next);
        };
        CUnmannedTraderItemState::STATE CUnmannedTraderUserInfoTableGetCloseItemForPassTimeUpdateInfo46_wrapper(struct CUnmannedTraderUserInfoTable* _this, unsigned int dwOwnerSerial, unsigned int dwRegistSerial, struct CPlayer** pkOwner)
        {
           return CUnmannedTraderUserInfoTableGetCloseItemForPassTimeUpdateInfo46_user(_this, dwOwnerSerial, dwRegistSerial, pkOwner, CUnmannedTraderUserInfoTableGetCloseItemForPassTimeUpdateInfo46_next);
        };
        char CUnmannedTraderUserInfoTableGetMaxRegistCnt48_wrapper(struct CUnmannedTraderUserInfoTable* _this, uint16_t wInx, unsigned int dwSerial)
        {
           return CUnmannedTraderUserInfoTableGetMaxRegistCnt48_user(_this, wInx, dwSerial, CUnmannedTraderUserInfoTableGetMaxRegistCnt48_next);
        };
        struct CUnmannedTraderRegistItemInfo* CUnmannedTraderUserInfoTableGetRegItemInfo50_wrapper(struct CUnmannedTraderUserInfoTable* _this, uint16_t wInx, unsigned int dwSerial)
        {
           return CUnmannedTraderUserInfoTableGetRegItemInfo50_user(_this, wInx, dwSerial, CUnmannedTraderUserInfoTableGetRegItemInfo50_next);
        };
        bool CUnmannedTraderUserInfoTableInit52_wrapper(struct CUnmannedTraderUserInfoTable* _this)
        {
           return CUnmannedTraderUserInfoTableInit52_user(_this, CUnmannedTraderUserInfoTableInit52_next);
        };
        struct CUnmannedTraderUserInfoTable* CUnmannedTraderUserInfoTableInstance54_wrapper()
        {
           return CUnmannedTraderUserInfoTableInstance54_user(CUnmannedTraderUserInfoTableInstance54_next);
        };
        bool CUnmannedTraderUserInfoTableLoad56_wrapper(struct CUnmannedTraderUserInfoTable* _this, char byType, uint16_t wInx, unsigned int dwSerial, struct _TRADE_DB_BASE* kInfo)
        {
           return CUnmannedTraderUserInfoTableLoad56_user(_this, byType, wInx, dwSerial, kInfo, CUnmannedTraderUserInfoTableLoad56_next);
        };
        void CUnmannedTraderUserInfoTableLog58_wrapper(struct CUnmannedTraderUserInfoTable* _this, char* fmt)
        {
           CUnmannedTraderUserInfoTableLog58_user(_this, fmt, CUnmannedTraderUserInfoTableLog58_next);
        };
        void CUnmannedTraderUserInfoTableLogOut60_wrapper(struct CUnmannedTraderUserInfoTable* _this, uint16_t wInx, unsigned int dwSerial)
        {
           CUnmannedTraderUserInfoTableLogOut60_user(_this, wInx, dwSerial, CUnmannedTraderUserInfoTableLogOut60_next);
        };
        void CUnmannedTraderUserInfoTableModifyPrice62_wrapper(struct CUnmannedTraderUserInfoTable* _this, uint16_t wInx, char byType, struct _a_trade_adjust_price_request_clzo* pRequest)
        {
           CUnmannedTraderUserInfoTableModifyPrice62_user(_this, wInx, byType, pRequest, CUnmannedTraderUserInfoTableModifyPrice62_next);
        };
        void CUnmannedTraderUserInfoTablePushUpdateBuyRollBack64_wrapper(struct CUnmannedTraderUserInfoTable* _this, struct _qry_case_unmandtrader_buy_update_wait* pkQuery)
        {
           CUnmannedTraderUserInfoTablePushUpdateBuyRollBack64_user(_this, pkQuery, CUnmannedTraderUserInfoTablePushUpdateBuyRollBack64_next);
        };
        void CUnmannedTraderUserInfoTableReRegist66_wrapper(struct CUnmannedTraderUserInfoTable* _this, uint16_t wInx, char byType, struct _unmannedtrader_re_regist_request_clzo* pRequest)
        {
           CUnmannedTraderUserInfoTableReRegist66_user(_this, wInx, byType, pRequest, CUnmannedTraderUserInfoTableReRegist66_next);
        };
        void CUnmannedTraderUserInfoTableRegist68_wrapper(struct CUnmannedTraderUserInfoTable* _this, uint16_t wInx, char byType, struct _a_trade_reg_item_request_clzo* pRequest)
        {
           CUnmannedTraderUserInfoTableRegist68_user(_this, wInx, byType, pRequest, CUnmannedTraderUserInfoTableRegist68_next);
        };
        void CUnmannedTraderUserInfoTableSearch70_wrapper(struct CUnmannedTraderUserInfoTable* _this, uint16_t wInx, char byType, struct _unmannedtrader_search_list_request_clzo* pRequest)
        {
           CUnmannedTraderUserInfoTableSearch70_user(_this, wInx, byType, pRequest, CUnmannedTraderUserInfoTableSearch70_next);
        };
        void CUnmannedTraderUserInfoTableServiceLog72_wrapper(struct CUnmannedTraderUserInfoTable* _this, char* fmt)
        {
           CUnmannedTraderUserInfoTableServiceLog72_user(_this, fmt, CUnmannedTraderUserInfoTableServiceLog72_next);
        };
        void CUnmannedTraderUserInfoTableSetLogger74_wrapper(struct CUnmannedTraderUserInfoTable* _this, struct CLogFile* pkLogger, struct CLogFile* pkSeviceLogger)
        {
           CUnmannedTraderUserInfoTableSetLogger74_user(_this, pkLogger, pkSeviceLogger, CUnmannedTraderUserInfoTableSetLogger74_next);
        };
        bool CUnmannedTraderUserInfoTableSubCompleteBuyFindBuyer76_wrapper(struct CUnmannedTraderUserInfoTable* _this, struct _qry_case_unmandtrader_buy_update_wait* pkQuery, struct CUnmannedTraderUserInfo** ppkBuyUser, struct CPlayer** ppkBuyPlayer)
        {
           return CUnmannedTraderUserInfoTableSubCompleteBuyFindBuyer76_user(_this, pkQuery, ppkBuyUser, ppkBuyPlayer, CUnmannedTraderUserInfoTableSubCompleteBuyFindBuyer76_next);
        };
        void CUnmannedTraderUserInfoTableSubCompleteBuyIncreaseVesion78_wrapper(struct CUnmannedTraderUserInfoTable* _this, char byDivision, char byClass)
        {
           CUnmannedTraderUserInfoTableSubCompleteBuyIncreaseVesion78_user(_this, byDivision, byClass, CUnmannedTraderUserInfoTableSubCompleteBuyIncreaseVesion78_next);
        };
        bool CUnmannedTraderUserInfoTableSubCompleteBuyProcBuy80_wrapper(struct CUnmannedTraderUserInfoTable* _this, struct CPlayer* pkBuyPlayer, struct CUnmannedTraderUserInfo* pkBuyUser, int64_t tResultTime, struct _qry_case_unmandtrader_buy_update_wait::__list* pkQueryList, struct _unmannedtrader_buy_item_result_zocl::__list* pSendResultList, struct _qry_case_unmandtrader_buy_update_complete::__list* pUpdateCompleteList, char* byCompleteUpdateNum, unsigned int* pdwPayDalant, struct CUnmannedTraderTradeInfo* pkTaradInfo)
        {
           return CUnmannedTraderUserInfoTableSubCompleteBuyProcBuy80_user(_this, pkBuyPlayer, pkBuyUser, tResultTime, pkQueryList, pSendResultList, pUpdateCompleteList, byCompleteUpdateNum, pdwPayDalant, pkTaradInfo, CUnmannedTraderUserInfoTableSubCompleteBuyProcBuy80_next);
        };
        bool CUnmannedTraderUserInfoTableSubCompleteBuyProcBuyResult82_wrapper(struct CUnmannedTraderUserInfoTable* _this, char byRet, struct _qry_case_unmandtrader_buy_update_complete::__list* pUpdateCompleteList, char* byCompleteUpdateNum)
        {
           return CUnmannedTraderUserInfoTableSubCompleteBuyProcBuyResult82_user(_this, byRet, pUpdateCompleteList, byCompleteUpdateNum, CUnmannedTraderUserInfoTableSubCompleteBuyProcBuyResult82_next);
        };
        
        void CUnmannedTraderUserInfoTabledtor_CUnmannedTraderUserInfoTable86_wrapper(struct CUnmannedTraderUserInfoTable* _this)
        {
           CUnmannedTraderUserInfoTabledtor_CUnmannedTraderUserInfoTable86_user(_this, CUnmannedTraderUserInfoTabledtor_CUnmannedTraderUserInfoTable86_next);
        };
        
        ::std::array<hook_record, 42> CUnmannedTraderUserInfoTable_functions = 
        {
            _hook_record {
                (LPVOID)0x1401d4980L,
                (LPVOID *)&CUnmannedTraderUserInfoTableBuy2_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableBuy2_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableBuy2_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(uint16_t, char, struct _unmannedtrader_buy_item_request_clzo*))&CUnmannedTraderUserInfoTable::Buy)
            },
            _hook_record {
                (LPVOID)0x140366b70L,
                (LPVOID *)&CUnmannedTraderUserInfoTablector_CUnmannedTraderUserInfoTable4_user,
                (LPVOID *)&CUnmannedTraderUserInfoTablector_CUnmannedTraderUserInfoTable4_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTablector_CUnmannedTraderUserInfoTable4_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)())&CUnmannedTraderUserInfoTable::ctor_CUnmannedTraderUserInfoTable)
            },
            _hook_record {
                (LPVOID)0x1401d4870L,
                (LPVOID *)&CUnmannedTraderUserInfoTableCancelRegist6_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableCancelRegist6_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableCancelRegist6_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(uint16_t, char, struct _a_trade_clear_item_request_clzo*))&CUnmannedTraderUserInfoTable::CancelRegist)
            },
            _hook_record {
                (LPVOID)0x140363a00L,
                (LPVOID *)&CUnmannedTraderUserInfoTableCheatCancelRegist8_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableCheatCancelRegist8_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableCheatCancelRegist8_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfoTable::*)(uint16_t, unsigned int, char))&CUnmannedTraderUserInfoTable::CheatCancelRegist)
            },
            _hook_record {
                (LPVOID)0x1401d4660L,
                (LPVOID *)&CUnmannedTraderUserInfoTableCheckwIndexAndType10_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableCheckwIndexAndType10_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableCheckwIndexAndType10_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfoTable::*)(uint16_t, char, char*))&CUnmannedTraderUserInfoTable::CheckwIndexAndType)
            },
            _hook_record {
                (LPVOID)0x140365cc0L,
                (LPVOID *)&CUnmannedTraderUserInfoTableClearLogLogOutState12_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableClearLogLogOutState12_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableClearLogLogOutState12_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(char*, unsigned int, char*, char*, unsigned int, unsigned int, unsigned int, unsigned int))&CUnmannedTraderUserInfoTable::ClearLogLogOutState)
            },
            _hook_record {
                (LPVOID)0x140364970L,
                (LPVOID *)&CUnmannedTraderUserInfoTableClearRequest14_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableClearRequest14_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableClearRequest14_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(uint16_t, unsigned int))&CUnmannedTraderUserInfoTable::ClearRequest)
            },
            _hook_record {
                (LPVOID)0x140363f80L,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteBuy16_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteBuy16_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableCompleteBuy16_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(char, char*, struct CUnmannedTraderTradeInfo*))&CUnmannedTraderUserInfoTable::CompleteBuy)
            },
            _hook_record {
                (LPVOID)0x140363e40L,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteCancelRegist18_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteCancelRegist18_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableCompleteCancelRegist18_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(char, char*))&CUnmannedTraderUserInfoTable::CompleteCancelRegist)
            },
            _hook_record {
                (LPVOID)0x140363b30L,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteCreate20_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteCreate20_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableCompleteCreate20_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(uint16_t))&CUnmannedTraderUserInfoTable::CompleteCreate)
            },
            _hook_record {
                (LPVOID)0x140364520L,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteReRegist22_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteReRegist22_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableCompleteReRegist22_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(char*))&CUnmannedTraderUserInfoTable::CompleteReRegist)
            },
            _hook_record {
                (LPVOID)0x1403648a0L,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteReRegistRollBack24_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteReRegistRollBack24_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableCompleteReRegistRollBack24_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(uint16_t, unsigned int, char*))&CUnmannedTraderUserInfoTable::CompleteReRegistRollBack)
            },
            _hook_record {
                (LPVOID)0x140363bc0L,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteRegist26_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteRegist26_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableCompleteRegist26_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(char, char*))&CUnmannedTraderUserInfoTable::CompleteRegist)
            },
            _hook_record {
                (LPVOID)0x140363d70L,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteReprice28_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteReprice28_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableCompleteReprice28_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(char, char*))&CUnmannedTraderUserInfoTable::CompleteReprice)
            },
            _hook_record {
                (LPVOID)0x1403642f0L,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteSearch30_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteSearch30_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableCompleteSearch30_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(char, char, char*))&CUnmannedTraderUserInfoTable::CompleteSearch)
            },
            _hook_record {
                (LPVOID)0x140364400L,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteTimeOutClear32_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteTimeOutClear32_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableCompleteTimeOutClear32_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(char*))&CUnmannedTraderUserInfoTable::CompleteTimeOutClear)
            },
            _hook_record {
                (LPVOID)0x1403647f0L,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteUpdateCheatRegistTime34_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteUpdateCheatRegistTime34_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableCompleteUpdateCheatRegistTime34_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(char*))&CUnmannedTraderUserInfoTable::CompleteUpdateCheatRegistTime)
            },
            _hook_record {
                (LPVOID)0x140363ab0L,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteUpdateState36_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableCompleteUpdateState36_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableCompleteUpdateState36_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfoTable::*)(unsigned int, unsigned int, char))&CUnmannedTraderUserInfoTable::CompleteUpdateState)
            },
            _hook_record {
                (LPVOID)0x140363780L,
                (LPVOID *)&CUnmannedTraderUserInfoTableDestroy38_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableDestroy38_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableDestroy38_wrapper),
                (LPVOID)cast_pointer_function((void(*)())&CUnmannedTraderUserInfoTable::Destroy)
            },
            _hook_record {
                (LPVOID)0x140365a90L,
                (LPVOID *)&CUnmannedTraderUserInfoTableFind40_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableFind40_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableFind40_wrapper),
                (LPVOID)cast_pointer_function((struct CUnmannedTraderUserInfo*(CUnmannedTraderUserInfoTable::*)(unsigned int))&CUnmannedTraderUserInfoTable::Find)
            },
            _hook_record {
                (LPVOID)0x140366db0L,
                (LPVOID *)&CUnmannedTraderUserInfoTableFindByIndex42_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableFindByIndex42_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableFindByIndex42_wrapper),
                (LPVOID)cast_pointer_function((struct CUnmannedTraderUserInfo*(CUnmannedTraderUserInfoTable::*)(uint16_t))&CUnmannedTraderUserInfoTable::FindByIndex)
            },
            _hook_record {
                (LPVOID)0x140366ca0L,
                (LPVOID *)&CUnmannedTraderUserInfoTableFindUser44_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableFindUser44_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableFindUser44_wrapper),
                (LPVOID)cast_pointer_function((struct CUnmannedTraderUserInfo*(CUnmannedTraderUserInfoTable::*)(uint16_t, unsigned int))&CUnmannedTraderUserInfoTable::FindUser)
            },
            _hook_record {
                (LPVOID)0x1403979f0L,
                (LPVOID *)&CUnmannedTraderUserInfoTableGetCloseItemForPassTimeUpdateInfo46_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableGetCloseItemForPassTimeUpdateInfo46_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableGetCloseItemForPassTimeUpdateInfo46_wrapper),
                (LPVOID)cast_pointer_function((CUnmannedTraderItemState::STATE(CUnmannedTraderUserInfoTable::*)(unsigned int, unsigned int, struct CPlayer**))&CUnmannedTraderUserInfoTable::GetCloseItemForPassTimeUpdateInfo)
            },
            _hook_record {
                (LPVOID)0x140365830L,
                (LPVOID *)&CUnmannedTraderUserInfoTableGetMaxRegistCnt48_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableGetMaxRegistCnt48_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableGetMaxRegistCnt48_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderUserInfoTable::*)(uint16_t, unsigned int))&CUnmannedTraderUserInfoTable::GetMaxRegistCnt)
            },
            _hook_record {
                (LPVOID)0x140365910L,
                (LPVOID *)&CUnmannedTraderUserInfoTableGetRegItemInfo50_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableGetRegItemInfo50_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableGetRegItemInfo50_wrapper),
                (LPVOID)cast_pointer_function((struct CUnmannedTraderRegistItemInfo*(CUnmannedTraderUserInfoTable::*)(uint16_t, unsigned int))&CUnmannedTraderUserInfoTable::GetRegItemInfo)
            },
            _hook_record {
                (LPVOID)0x140363800L,
                (LPVOID *)&CUnmannedTraderUserInfoTableInit52_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableInit52_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableInit52_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfoTable::*)())&CUnmannedTraderUserInfoTable::Init)
            },
            _hook_record {
                (LPVOID)0x1403636c0L,
                (LPVOID *)&CUnmannedTraderUserInfoTableInstance54_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableInstance54_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableInstance54_wrapper),
                (LPVOID)cast_pointer_function((struct CUnmannedTraderUserInfoTable*(*)())&CUnmannedTraderUserInfoTable::Instance)
            },
            _hook_record {
                (LPVOID)0x140363940L,
                (LPVOID *)&CUnmannedTraderUserInfoTableLoad56_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableLoad56_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableLoad56_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfoTable::*)(char, uint16_t, unsigned int, struct _TRADE_DB_BASE*))&CUnmannedTraderUserInfoTable::Load)
            },
            _hook_record {
                (LPVOID)0x140365fd0L,
                (LPVOID *)&CUnmannedTraderUserInfoTableLog58_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableLog58_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableLog58_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(char*))&CUnmannedTraderUserInfoTable::Log)
            },
            _hook_record {
                (LPVOID)0x1403659f0L,
                (LPVOID *)&CUnmannedTraderUserInfoTableLogOut60_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableLogOut60_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableLogOut60_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(uint16_t, unsigned int))&CUnmannedTraderUserInfoTable::LogOut)
            },
            _hook_record {
                (LPVOID)0x1401d4760L,
                (LPVOID *)&CUnmannedTraderUserInfoTableModifyPrice62_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableModifyPrice62_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableModifyPrice62_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(uint16_t, char, struct _a_trade_adjust_price_request_clzo*))&CUnmannedTraderUserInfoTable::ModifyPrice)
            },
            _hook_record {
                (LPVOID)0x140364a10L,
                (LPVOID *)&CUnmannedTraderUserInfoTablePushUpdateBuyRollBack64_user,
                (LPVOID *)&CUnmannedTraderUserInfoTablePushUpdateBuyRollBack64_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTablePushUpdateBuyRollBack64_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(struct _qry_case_unmandtrader_buy_update_wait*))&CUnmannedTraderUserInfoTable::PushUpdateBuyRollBack)
            },
            _hook_record {
                (LPVOID)0x1401d4ba0L,
                (LPVOID *)&CUnmannedTraderUserInfoTableReRegist66_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableReRegist66_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableReRegist66_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(uint16_t, char, struct _unmannedtrader_re_regist_request_clzo*))&CUnmannedTraderUserInfoTable::ReRegist)
            },
            _hook_record {
                (LPVOID)0x1401d45b0L,
                (LPVOID *)&CUnmannedTraderUserInfoTableRegist68_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableRegist68_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableRegist68_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(uint16_t, char, struct _a_trade_reg_item_request_clzo*))&CUnmannedTraderUserInfoTable::Regist)
            },
            _hook_record {
                (LPVOID)0x1401d4a90L,
                (LPVOID *)&CUnmannedTraderUserInfoTableSearch70_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableSearch70_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableSearch70_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(uint16_t, char, struct _unmannedtrader_search_list_request_clzo*))&CUnmannedTraderUserInfoTable::Search)
            },
            _hook_record {
                (LPVOID)0x140366060L,
                (LPVOID *)&CUnmannedTraderUserInfoTableServiceLog72_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableServiceLog72_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableServiceLog72_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(char*))&CUnmannedTraderUserInfoTable::ServiceLog)
            },
            _hook_record {
                (LPVOID)0x140351910L,
                (LPVOID *)&CUnmannedTraderUserInfoTableSetLogger74_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableSetLogger74_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableSetLogger74_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(struct CLogFile*, struct CLogFile*))&CUnmannedTraderUserInfoTable::SetLogger)
            },
            _hook_record {
                (LPVOID)0x140364bc0L,
                (LPVOID *)&CUnmannedTraderUserInfoTableSubCompleteBuyFindBuyer76_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableSubCompleteBuyFindBuyer76_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableSubCompleteBuyFindBuyer76_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfoTable::*)(struct _qry_case_unmandtrader_buy_update_wait*, struct CUnmannedTraderUserInfo**, struct CPlayer**))&CUnmannedTraderUserInfoTable::SubCompleteBuyFindBuyer)
            },
            _hook_record {
                (LPVOID)0x1403657b0L,
                (LPVOID *)&CUnmannedTraderUserInfoTableSubCompleteBuyIncreaseVesion78_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableSubCompleteBuyIncreaseVesion78_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableSubCompleteBuyIncreaseVesion78_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)(char, char))&CUnmannedTraderUserInfoTable::SubCompleteBuyIncreaseVesion)
            },
            _hook_record {
                (LPVOID)0x140364f10L,
                (LPVOID *)&CUnmannedTraderUserInfoTableSubCompleteBuyProcBuy80_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableSubCompleteBuyProcBuy80_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableSubCompleteBuyProcBuy80_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfoTable::*)(struct CPlayer*, struct CUnmannedTraderUserInfo*, int64_t, struct _qry_case_unmandtrader_buy_update_wait::__list*, struct _unmannedtrader_buy_item_result_zocl::__list*, struct _qry_case_unmandtrader_buy_update_complete::__list*, char*, unsigned int*, struct CUnmannedTraderTradeInfo*))&CUnmannedTraderUserInfoTable::SubCompleteBuyProcBuy)
            },
            _hook_record {
                (LPVOID)0x140365650L,
                (LPVOID *)&CUnmannedTraderUserInfoTableSubCompleteBuyProcBuyResult82_user,
                (LPVOID *)&CUnmannedTraderUserInfoTableSubCompleteBuyProcBuyResult82_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTableSubCompleteBuyProcBuyResult82_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfoTable::*)(char, struct _qry_case_unmandtrader_buy_update_complete::__list*, char*))&CUnmannedTraderUserInfoTable::SubCompleteBuyProcBuyResult)
            },
            _hook_record {
                (LPVOID)0x140366c50L,
                (LPVOID *)&CUnmannedTraderUserInfoTabledtor_CUnmannedTraderUserInfoTable86_user,
                (LPVOID *)&CUnmannedTraderUserInfoTabledtor_CUnmannedTraderUserInfoTable86_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoTabledtor_CUnmannedTraderUserInfoTable86_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfoTable::*)())&CUnmannedTraderUserInfoTable::dtor_CUnmannedTraderUserInfoTable)
            },
        };
    }; // end namespace Detail
END_ATF_NAMESPACE
