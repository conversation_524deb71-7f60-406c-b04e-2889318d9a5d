#include <GUILD_BATTLE__CGuildBattleScheduleManagerDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Detail
        {
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerAdd2_ptr GUILD_BATTLE__CGuildBattleScheduleManagerAdd2_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerAdd2_clbk GUILD_BATTLE__CGuildBattleScheduleManagerAdd2_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerAddDefaultDBTable4_ptr GUILD_BATTLE__CGuildBattleScheduleManagerAddDefaultDBTable4_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerAddDefaultDBTable4_clbk GUILD_BATTLE__CGuildBattleScheduleManagerAddDefaultDBTable4_user(nullptr);
            
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerctor_CGuildBattleScheduleManager6_ptr GUILD_BATTLE__CGuildBattleScheduleManagerctor_CGuildBattleScheduleManager6_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerctor_CGuildBattleScheduleManager6_clbk GUILD_BATTLE__CGuildBattleScheduleManagerctor_CGuildBattleScheduleManager6_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerCleanUpDanglingReservedSchedule8_ptr GUILD_BATTLE__CGuildBattleScheduleManagerCleanUpDanglingReservedSchedule8_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerCleanUpDanglingReservedSchedule8_clbk GUILD_BATTLE__CGuildBattleScheduleManagerCleanUpDanglingReservedSchedule8_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerClear10_ptr GUILD_BATTLE__CGuildBattleScheduleManagerClear10_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerClear10_clbk GUILD_BATTLE__CGuildBattleScheduleManagerClear10_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerClearTommorowScheduleByID12_ptr GUILD_BATTLE__CGuildBattleScheduleManagerClearTommorowScheduleByID12_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerClearTommorowScheduleByID12_clbk GUILD_BATTLE__CGuildBattleScheduleManagerClearTommorowScheduleByID12_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerDestroy14_ptr GUILD_BATTLE__CGuildBattleScheduleManagerDestroy14_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerDestroy14_clbk GUILD_BATTLE__CGuildBattleScheduleManagerDestroy14_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerFlip16_ptr GUILD_BATTLE__CGuildBattleScheduleManagerFlip16_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerFlip16_clbk GUILD_BATTLE__CGuildBattleScheduleManagerFlip16_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerGetCurScheduleID18_ptr GUILD_BATTLE__CGuildBattleScheduleManagerGetCurScheduleID18_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerGetCurScheduleID18_clbk GUILD_BATTLE__CGuildBattleScheduleManagerGetCurScheduleID18_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerGetTodayDayID20_ptr GUILD_BATTLE__CGuildBattleScheduleManagerGetTodayDayID20_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerGetTodayDayID20_clbk GUILD_BATTLE__CGuildBattleScheduleManagerGetTodayDayID20_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerGetTodaySLIDByMap22_ptr GUILD_BATTLE__CGuildBattleScheduleManagerGetTodaySLIDByMap22_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerGetTodaySLIDByMap22_clbk GUILD_BATTLE__CGuildBattleScheduleManagerGetTodaySLIDByMap22_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowDayID24_ptr GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowDayID24_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowDayID24_clbk GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowDayID24_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowSLIDByMap26_ptr GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowSLIDByMap26_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowSLIDByMap26_clbk GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowSLIDByMap26_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerInit28_ptr GUILD_BATTLE__CGuildBattleScheduleManagerInit28_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerInit28_clbk GUILD_BATTLE__CGuildBattleScheduleManagerInit28_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerInstance30_ptr GUILD_BATTLE__CGuildBattleScheduleManagerInstance30_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerInstance30_clbk GUILD_BATTLE__CGuildBattleScheduleManagerInstance30_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerIsDayChanged32_ptr GUILD_BATTLE__CGuildBattleScheduleManagerIsDayChanged32_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerIsDayChanged32_clbk GUILD_BATTLE__CGuildBattleScheduleManagerIsDayChanged32_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerIsEmptyTime34_ptr GUILD_BATTLE__CGuildBattleScheduleManagerIsEmptyTime34_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerIsEmptyTime34_clbk GUILD_BATTLE__CGuildBattleScheduleManagerIsEmptyTime34_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerLoad36_ptr GUILD_BATTLE__CGuildBattleScheduleManagerLoad36_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerLoad36_clbk GUILD_BATTLE__CGuildBattleScheduleManagerLoad36_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerLoop38_ptr GUILD_BATTLE__CGuildBattleScheduleManagerLoop38_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerLoop38_clbk GUILD_BATTLE__CGuildBattleScheduleManagerLoop38_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerSetNextEvnet40_ptr GUILD_BATTLE__CGuildBattleScheduleManagerSetNextEvnet40_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerSetNextEvnet40_clbk GUILD_BATTLE__CGuildBattleScheduleManagerSetNextEvnet40_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerUpdateDayChangedWork42_ptr GUILD_BATTLE__CGuildBattleScheduleManagerUpdateDayChangedWork42_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerUpdateDayChangedWork42_clbk GUILD_BATTLE__CGuildBattleScheduleManagerUpdateDayChangedWork42_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerUpdateUseFlag44_ptr GUILD_BATTLE__CGuildBattleScheduleManagerUpdateUseFlag44_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerUpdateUseFlag44_clbk GUILD_BATTLE__CGuildBattleScheduleManagerUpdateUseFlag44_user(nullptr);
            
            
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerdtor_CGuildBattleScheduleManager48_ptr GUILD_BATTLE__CGuildBattleScheduleManagerdtor_CGuildBattleScheduleManager48_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleScheduleManagerdtor_CGuildBattleScheduleManager48_clbk GUILD_BATTLE__CGuildBattleScheduleManagerdtor_CGuildBattleScheduleManager48_user(nullptr);
            
            char GUILD_BATTLE__CGuildBattleScheduleManagerAdd2_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this, unsigned int uiFieldInx, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt, struct GUILD_BATTLE::CGuildBattleSchedule** ppkSchedule, unsigned int* uiSLID)
            {
               return GUILD_BATTLE__CGuildBattleScheduleManagerAdd2_user(_this, uiFieldInx, dwStartTimeInx, dwElapseTimeCnt, ppkSchedule, uiSLID, GUILD_BATTLE__CGuildBattleScheduleManagerAdd2_next);
            };
            bool GUILD_BATTLE__CGuildBattleScheduleManagerAddDefaultDBTable4_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this)
            {
               return GUILD_BATTLE__CGuildBattleScheduleManagerAddDefaultDBTable4_user(_this, GUILD_BATTLE__CGuildBattleScheduleManagerAddDefaultDBTable4_next);
            };
            
            void GUILD_BATTLE__CGuildBattleScheduleManagerctor_CGuildBattleScheduleManager6_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this)
            {
               GUILD_BATTLE__CGuildBattleScheduleManagerctor_CGuildBattleScheduleManager6_user(_this, GUILD_BATTLE__CGuildBattleScheduleManagerctor_CGuildBattleScheduleManager6_next);
            };
            bool GUILD_BATTLE__CGuildBattleScheduleManagerCleanUpDanglingReservedSchedule8_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this)
            {
               return GUILD_BATTLE__CGuildBattleScheduleManagerCleanUpDanglingReservedSchedule8_user(_this, GUILD_BATTLE__CGuildBattleScheduleManagerCleanUpDanglingReservedSchedule8_next);
            };
            void GUILD_BATTLE__CGuildBattleScheduleManagerClear10_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this)
            {
               GUILD_BATTLE__CGuildBattleScheduleManagerClear10_user(_this, GUILD_BATTLE__CGuildBattleScheduleManagerClear10_next);
            };
            bool GUILD_BATTLE__CGuildBattleScheduleManagerClearTommorowScheduleByID12_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this, unsigned int uiMapID, unsigned int dwID)
            {
               return GUILD_BATTLE__CGuildBattleScheduleManagerClearTommorowScheduleByID12_user(_this, uiMapID, dwID, GUILD_BATTLE__CGuildBattleScheduleManagerClearTommorowScheduleByID12_next);
            };
            void GUILD_BATTLE__CGuildBattleScheduleManagerDestroy14_wrapper()
            {
               GUILD_BATTLE__CGuildBattleScheduleManagerDestroy14_user(GUILD_BATTLE__CGuildBattleScheduleManagerDestroy14_next);
            };
            void GUILD_BATTLE__CGuildBattleScheduleManagerFlip16_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this)
            {
               GUILD_BATTLE__CGuildBattleScheduleManagerFlip16_user(_this, GUILD_BATTLE__CGuildBattleScheduleManagerFlip16_next);
            };
            unsigned int GUILD_BATTLE__CGuildBattleScheduleManagerGetCurScheduleID18_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this, unsigned int uiMapID)
            {
               return GUILD_BATTLE__CGuildBattleScheduleManagerGetCurScheduleID18_user(_this, uiMapID, GUILD_BATTLE__CGuildBattleScheduleManagerGetCurScheduleID18_next);
            };
            unsigned int GUILD_BATTLE__CGuildBattleScheduleManagerGetTodayDayID20_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this)
            {
               return GUILD_BATTLE__CGuildBattleScheduleManagerGetTodayDayID20_user(_this, GUILD_BATTLE__CGuildBattleScheduleManagerGetTodayDayID20_next);
            };
            bool GUILD_BATTLE__CGuildBattleScheduleManagerGetTodaySLIDByMap22_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this, unsigned int uiMap, unsigned int* uiSLID)
            {
               return GUILD_BATTLE__CGuildBattleScheduleManagerGetTodaySLIDByMap22_user(_this, uiMap, uiSLID, GUILD_BATTLE__CGuildBattleScheduleManagerGetTodaySLIDByMap22_next);
            };
            unsigned int GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowDayID24_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this)
            {
               return GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowDayID24_user(_this, GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowDayID24_next);
            };
            bool GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowSLIDByMap26_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this, unsigned int uiMap, unsigned int* uiSLID)
            {
               return GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowSLIDByMap26_user(_this, uiMap, uiSLID, GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowSLIDByMap26_next);
            };
            bool GUILD_BATTLE__CGuildBattleScheduleManagerInit28_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this, unsigned int uiMapCnt)
            {
               return GUILD_BATTLE__CGuildBattleScheduleManagerInit28_user(_this, uiMapCnt, GUILD_BATTLE__CGuildBattleScheduleManagerInit28_next);
            };
            struct GUILD_BATTLE::CGuildBattleScheduleManager* GUILD_BATTLE__CGuildBattleScheduleManagerInstance30_wrapper()
            {
               return GUILD_BATTLE__CGuildBattleScheduleManagerInstance30_user(GUILD_BATTLE__CGuildBattleScheduleManagerInstance30_next);
            };
            int GUILD_BATTLE__CGuildBattleScheduleManagerIsDayChanged32_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this)
            {
               return GUILD_BATTLE__CGuildBattleScheduleManagerIsDayChanged32_user(_this, GUILD_BATTLE__CGuildBattleScheduleManagerIsDayChanged32_next);
            };
            char GUILD_BATTLE__CGuildBattleScheduleManagerIsEmptyTime34_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this, unsigned int uiFieldInx, unsigned int dwStartTimeInx)
            {
               return GUILD_BATTLE__CGuildBattleScheduleManagerIsEmptyTime34_user(_this, uiFieldInx, dwStartTimeInx, GUILD_BATTLE__CGuildBattleScheduleManagerIsEmptyTime34_next);
            };
            bool GUILD_BATTLE__CGuildBattleScheduleManagerLoad36_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this, int iCurDay, unsigned int uiOldMapCnt, int iToday, int iTodayDayID, int iTomorrow, int iTomorrowDayID)
            {
               return GUILD_BATTLE__CGuildBattleScheduleManagerLoad36_user(_this, iCurDay, uiOldMapCnt, iToday, iTodayDayID, iTomorrow, iTomorrowDayID, GUILD_BATTLE__CGuildBattleScheduleManagerLoad36_next);
            };
            void GUILD_BATTLE__CGuildBattleScheduleManagerLoop38_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this)
            {
               GUILD_BATTLE__CGuildBattleScheduleManagerLoop38_user(_this, GUILD_BATTLE__CGuildBattleScheduleManagerLoop38_next);
            };
            void GUILD_BATTLE__CGuildBattleScheduleManagerSetNextEvnet40_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this)
            {
               GUILD_BATTLE__CGuildBattleScheduleManagerSetNextEvnet40_user(_this, GUILD_BATTLE__CGuildBattleScheduleManagerSetNextEvnet40_next);
            };
            void GUILD_BATTLE__CGuildBattleScheduleManagerUpdateDayChangedWork42_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this)
            {
               GUILD_BATTLE__CGuildBattleScheduleManagerUpdateDayChangedWork42_user(_this, GUILD_BATTLE__CGuildBattleScheduleManagerUpdateDayChangedWork42_next);
            };
            struct GUILD_BATTLE::CGuildBattleSchedule* GUILD_BATTLE__CGuildBattleScheduleManagerUpdateUseFlag44_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this, unsigned int uiDayID, unsigned int uiMapID, unsigned int dwID)
            {
               return GUILD_BATTLE__CGuildBattleScheduleManagerUpdateUseFlag44_user(_this, uiDayID, uiMapID, dwID, GUILD_BATTLE__CGuildBattleScheduleManagerUpdateUseFlag44_next);
            };
            
            void GUILD_BATTLE__CGuildBattleScheduleManagerdtor_CGuildBattleScheduleManager48_wrapper(struct GUILD_BATTLE::CGuildBattleScheduleManager* _this)
            {
               GUILD_BATTLE__CGuildBattleScheduleManagerdtor_CGuildBattleScheduleManager48_user(_this, GUILD_BATTLE__CGuildBattleScheduleManagerdtor_CGuildBattleScheduleManager48_next);
            };
            
            ::std::array<hook_record, 23> CGuildBattleScheduleManager_functions = 
            {
                _hook_record {
                    (LPVOID)0x1403d9180L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerAdd2_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerAdd2_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerAdd2_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CGuildBattleScheduleManager::*)(unsigned int, unsigned int, unsigned int, struct GUILD_BATTLE::CGuildBattleSchedule**, unsigned int*))&GUILD_BATTLE::CGuildBattleScheduleManager::Add)
                },
                _hook_record {
                    (LPVOID)0x1403dd320L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerAddDefaultDBTable4_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerAddDefaultDBTable4_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerAddDefaultDBTable4_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleScheduleManager::*)())&GUILD_BATTLE::CGuildBattleScheduleManager::AddDefaultDBTable)
                },
                _hook_record {
                    (LPVOID)0x1403dc830L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerctor_CGuildBattleScheduleManager6_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerctor_CGuildBattleScheduleManager6_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerctor_CGuildBattleScheduleManager6_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleScheduleManager::*)())&GUILD_BATTLE::CGuildBattleScheduleManager::ctor_CGuildBattleScheduleManager)
                },
                _hook_record {
                    (LPVOID)0x1403dd1c0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerCleanUpDanglingReservedSchedule8_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerCleanUpDanglingReservedSchedule8_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerCleanUpDanglingReservedSchedule8_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleScheduleManager::*)())&GUILD_BATTLE::CGuildBattleScheduleManager::CleanUpDanglingReservedSchedule)
                },
                _hook_record {
                    (LPVOID)0x1403dd480L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerClear10_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerClear10_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerClear10_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleScheduleManager::*)())&GUILD_BATTLE::CGuildBattleScheduleManager::Clear)
                },
                _hook_record {
                    (LPVOID)0x1403dd0d0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerClearTommorowScheduleByID12_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerClearTommorowScheduleByID12_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerClearTommorowScheduleByID12_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleScheduleManager::*)(unsigned int, unsigned int))&GUILD_BATTLE::CGuildBattleScheduleManager::ClearTommorowScheduleByID)
                },
                _hook_record {
                    (LPVOID)0x1403dcad0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerDestroy14_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerDestroy14_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerDestroy14_wrapper),
                    (LPVOID)cast_pointer_function((void(*)())&GUILD_BATTLE::CGuildBattleScheduleManager::Destroy)
                },
                _hook_record {
                    (LPVOID)0x1403dd3b0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerFlip16_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerFlip16_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerFlip16_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleScheduleManager::*)())&GUILD_BATTLE::CGuildBattleScheduleManager::Flip)
                },
                _hook_record {
                    (LPVOID)0x1403dd2d0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerGetCurScheduleID18_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerGetCurScheduleID18_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerGetCurScheduleID18_wrapper),
                    (LPVOID)cast_pointer_function((unsigned int(GUILD_BATTLE::CGuildBattleScheduleManager::*)(unsigned int))&GUILD_BATTLE::CGuildBattleScheduleManager::GetCurScheduleID)
                },
                _hook_record {
                    (LPVOID)0x1403d9a40L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerGetTodayDayID20_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerGetTodayDayID20_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerGetTodayDayID20_wrapper),
                    (LPVOID)cast_pointer_function((unsigned int(GUILD_BATTLE::CGuildBattleScheduleManager::*)())&GUILD_BATTLE::CGuildBattleScheduleManager::GetTodayDayID)
                },
                _hook_record {
                    (LPVOID)0x1403dd230L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerGetTodaySLIDByMap22_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerGetTodaySLIDByMap22_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerGetTodaySLIDByMap22_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleScheduleManager::*)(unsigned int, unsigned int*))&GUILD_BATTLE::CGuildBattleScheduleManager::GetTodaySLIDByMap)
                },
                _hook_record {
                    (LPVOID)0x1403d9ab0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowDayID24_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowDayID24_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowDayID24_wrapper),
                    (LPVOID)cast_pointer_function((unsigned int(GUILD_BATTLE::CGuildBattleScheduleManager::*)())&GUILD_BATTLE::CGuildBattleScheduleManager::GetTomorrowDayID)
                },
                _hook_record {
                    (LPVOID)0x1403dd280L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowSLIDByMap26_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowSLIDByMap26_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerGetTomorrowSLIDByMap26_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleScheduleManager::*)(unsigned int, unsigned int*))&GUILD_BATTLE::CGuildBattleScheduleManager::GetTomorrowSLIDByMap)
                },
                _hook_record {
                    (LPVOID)0x1403dcb50L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerInit28_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerInit28_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerInit28_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleScheduleManager::*)(unsigned int))&GUILD_BATTLE::CGuildBattleScheduleManager::Init)
                },
                _hook_record {
                    (LPVOID)0x1403dca10L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerInstance30_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerInstance30_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerInstance30_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CGuildBattleScheduleManager*(*)())&GUILD_BATTLE::CGuildBattleScheduleManager::Instance)
                },
                _hook_record {
                    (LPVOID)0x1403dd530L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerIsDayChanged32_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerIsDayChanged32_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerIsDayChanged32_wrapper),
                    (LPVOID)cast_pointer_function((int(GUILD_BATTLE::CGuildBattleScheduleManager::*)())&GUILD_BATTLE::CGuildBattleScheduleManager::IsDayChanged)
                },
                _hook_record {
                    (LPVOID)0x1403d9870L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerIsEmptyTime34_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerIsEmptyTime34_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerIsEmptyTime34_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CGuildBattleScheduleManager::*)(unsigned int, unsigned int))&GUILD_BATTLE::CGuildBattleScheduleManager::IsEmptyTime)
                },
                _hook_record {
                    (LPVOID)0x1403dcdd0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerLoad36_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerLoad36_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerLoad36_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleScheduleManager::*)(int, unsigned int, int, int, int, int))&GUILD_BATTLE::CGuildBattleScheduleManager::Load)
                },
                _hook_record {
                    (LPVOID)0x1403dcd30L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerLoop38_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerLoop38_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerLoop38_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleScheduleManager::*)())&GUILD_BATTLE::CGuildBattleScheduleManager::Loop)
                },
                _hook_record {
                    (LPVOID)0x1403dd6c0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerSetNextEvnet40_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerSetNextEvnet40_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerSetNextEvnet40_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleScheduleManager::*)())&GUILD_BATTLE::CGuildBattleScheduleManager::SetNextEvnet)
                },
                _hook_record {
                    (LPVOID)0x1403dd650L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerUpdateDayChangedWork42_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerUpdateDayChangedWork42_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerUpdateDayChangedWork42_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleScheduleManager::*)())&GUILD_BATTLE::CGuildBattleScheduleManager::UpdateDayChangedWork)
                },
                _hook_record {
                    (LPVOID)0x1403dd120L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerUpdateUseFlag44_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerUpdateUseFlag44_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerUpdateUseFlag44_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CGuildBattleSchedule*(GUILD_BATTLE::CGuildBattleScheduleManager::*)(unsigned int, unsigned int, unsigned int))&GUILD_BATTLE::CGuildBattleScheduleManager::UpdateUseFlag)
                },
                _hook_record {
                    (LPVOID)0x1403dc8e0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerdtor_CGuildBattleScheduleManager48_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleScheduleManagerdtor_CGuildBattleScheduleManager48_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleScheduleManagerdtor_CGuildBattleScheduleManager48_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleScheduleManager::*)())&GUILD_BATTLE::CGuildBattleScheduleManager::dtor_CGuildBattleScheduleManager)
                },
            };
        }; // end namespace Detail
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE
