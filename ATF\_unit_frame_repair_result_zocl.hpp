// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 2)
    struct  _unit_frame_repair_result_zocl
    {
        char byRetCode;
        char bySlotIndex;
        unsigned int dwNewGauge;
        unsigned int dwConsumDalant;
        unsigned int dwLeftDalant;
    };
    #pragma pack(pop)
END_ATF_NAMESPACE
