#include <GUILD_BATTLE__CGuildBattleReservedScheduleListManagerDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Detail
        {
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerctor_CGuildBattleReservedScheduleListManager2_ptr GUILD_BATTLE__CGuildBattleReservedScheduleListManagerctor_CGuildBattleReservedScheduleListManager2_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerctor_CGuildBattleReservedScheduleListManager2_clbk GUILD_BATTLE__CGuildBattleReservedScheduleListManagerctor_CGuildBattleReservedScheduleListManager2_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerClear4_ptr GUILD_BATTLE__CGuildBattleReservedScheduleListManagerClear4_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerClear4_clbk GUILD_BATTLE__CGuildBattleReservedScheduleListManagerClear4_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerDestroy6_ptr GUILD_BATTLE__CGuildBattleReservedScheduleListManagerDestroy6_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerDestroy6_clbk GUILD_BATTLE__CGuildBattleReservedScheduleListManagerDestroy6_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerFlip8_ptr GUILD_BATTLE__CGuildBattleReservedScheduleListManagerFlip8_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerFlip8_clbk GUILD_BATTLE__CGuildBattleReservedScheduleListManagerFlip8_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInit10_ptr GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInit10_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInit10_clbk GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInit10_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInstance12_ptr GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInstance12_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInstance12_clbk GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInstance12_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoad14_ptr GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoad14_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoad14_clbk GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoad14_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTodaySchedule16_ptr GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTodaySchedule16_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTodaySchedule16_clbk GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTodaySchedule16_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTomorrowSchedule18_ptr GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTomorrowSchedule18_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTomorrowSchedule18_clbk GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTomorrowSchedule18_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerPushDQS20_ptr GUILD_BATTLE__CGuildBattleReservedScheduleListManagerPushDQS20_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerPushDQS20_clbk GUILD_BATTLE__CGuildBattleReservedScheduleListManagerPushDQS20_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerSend22_ptr GUILD_BATTLE__CGuildBattleReservedScheduleListManagerSend22_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerSend22_clbk GUILD_BATTLE__CGuildBattleReservedScheduleListManagerSend22_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateReservedShedule24_ptr GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateReservedShedule24_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateReservedShedule24_clbk GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateReservedShedule24_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTodaySchedule26_ptr GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTodaySchedule26_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTodaySchedule26_clbk GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTodaySchedule26_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowComplete28_ptr GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowComplete28_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowComplete28_clbk GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowComplete28_user(nullptr);
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowSchedule30_ptr GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowSchedule30_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowSchedule30_clbk GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowSchedule30_user(nullptr);
            
            
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerdtor_CGuildBattleReservedScheduleListManager34_ptr GUILD_BATTLE__CGuildBattleReservedScheduleListManagerdtor_CGuildBattleReservedScheduleListManager34_next(nullptr);
            Info::GUILD_BATTLE__CGuildBattleReservedScheduleListManagerdtor_CGuildBattleReservedScheduleListManager34_clbk GUILD_BATTLE__CGuildBattleReservedScheduleListManagerdtor_CGuildBattleReservedScheduleListManager34_user(nullptr);
            
            
            void GUILD_BATTLE__CGuildBattleReservedScheduleListManagerctor_CGuildBattleReservedScheduleListManager2_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager* _this)
            {
               GUILD_BATTLE__CGuildBattleReservedScheduleListManagerctor_CGuildBattleReservedScheduleListManager2_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerctor_CGuildBattleReservedScheduleListManager2_next);
            };
            void GUILD_BATTLE__CGuildBattleReservedScheduleListManagerClear4_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager* _this)
            {
               GUILD_BATTLE__CGuildBattleReservedScheduleListManagerClear4_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerClear4_next);
            };
            void GUILD_BATTLE__CGuildBattleReservedScheduleListManagerDestroy6_wrapper()
            {
               GUILD_BATTLE__CGuildBattleReservedScheduleListManagerDestroy6_user(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerDestroy6_next);
            };
            void GUILD_BATTLE__CGuildBattleReservedScheduleListManagerFlip8_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager* _this)
            {
               GUILD_BATTLE__CGuildBattleReservedScheduleListManagerFlip8_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerFlip8_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInit10_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager* _this)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInit10_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInit10_next);
            };
            struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager* GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInstance12_wrapper()
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInstance12_user(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInstance12_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoad14_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager* _this, int iCurDay, unsigned int uiOldMapCnt, int iToday, int iTomorrow)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoad14_user(_this, iCurDay, uiOldMapCnt, iToday, iTomorrow, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoad14_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTodaySchedule16_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager* _this)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTodaySchedule16_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTodaySchedule16_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTomorrowSchedule18_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager* _this)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTomorrowSchedule18_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTomorrowSchedule18_next);
            };
            void GUILD_BATTLE__CGuildBattleReservedScheduleListManagerPushDQS20_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager* _this, unsigned int dwMapID, unsigned int dwSLID)
            {
               GUILD_BATTLE__CGuildBattleReservedScheduleListManagerPushDQS20_user(_this, dwMapID, dwSLID, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerPushDQS20_next);
            };
            void GUILD_BATTLE__CGuildBattleReservedScheduleListManagerSend22_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager* _this, unsigned int uiMapID, int n, unsigned int dwVer, char byDay, char byPage, unsigned int dwGuildSerial)
            {
               GUILD_BATTLE__CGuildBattleReservedScheduleListManagerSend22_user(_this, uiMapID, n, dwVer, byDay, byPage, dwGuildSerial, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerSend22_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateReservedShedule24_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager* _this, unsigned int dwSLID, char* byOutData)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateReservedShedule24_user(_this, dwSLID, byOutData, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateReservedShedule24_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTodaySchedule26_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager* _this, unsigned int uiMapID)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTodaySchedule26_user(_this, uiMapID, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTodaySchedule26_next);
            };
            void GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowComplete28_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager* _this, unsigned int dwMapID, char* pLoadData)
            {
               GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowComplete28_user(_this, dwMapID, pLoadData, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowComplete28_next);
            };
            bool GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowSchedule30_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager* _this, unsigned int uiMapID)
            {
               return GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowSchedule30_user(_this, uiMapID, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowSchedule30_next);
            };
            
            void GUILD_BATTLE__CGuildBattleReservedScheduleListManagerdtor_CGuildBattleReservedScheduleListManager34_wrapper(struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager* _this)
            {
               GUILD_BATTLE__CGuildBattleReservedScheduleListManagerdtor_CGuildBattleReservedScheduleListManager34_user(_this, GUILD_BATTLE__CGuildBattleReservedScheduleListManagerdtor_CGuildBattleReservedScheduleListManager34_next);
            };
            
            ::std::array<hook_record, 16> CGuildBattleReservedScheduleListManager_functions = 
            {
                _hook_record {
                    (LPVOID)0x1403cd260L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerctor_CGuildBattleReservedScheduleListManager2_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerctor_CGuildBattleReservedScheduleListManager2_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerctor_CGuildBattleReservedScheduleListManager2_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleReservedScheduleListManager::*)())&GUILD_BATTLE::CGuildBattleReservedScheduleListManager::ctor_CGuildBattleReservedScheduleListManager)
                },
                _hook_record {
                    (LPVOID)0x1403cd970L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerClear4_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerClear4_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerClear4_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleReservedScheduleListManager::*)())&GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Clear)
                },
                _hook_record {
                    (LPVOID)0x1403cd420L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerDestroy6_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerDestroy6_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerDestroy6_wrapper),
                    (LPVOID)cast_pointer_function((void(*)())&GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Destroy)
                },
                _hook_record {
                    (LPVOID)0x1403cd8f0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerFlip8_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerFlip8_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerFlip8_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleReservedScheduleListManager::*)())&GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Flip)
                },
                _hook_record {
                    (LPVOID)0x1403cd4a0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInit10_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInit10_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInit10_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedScheduleListManager::*)())&GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Init)
                },
                _hook_record {
                    (LPVOID)0x1403cd360L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInstance12_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInstance12_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerInstance12_wrapper),
                    (LPVOID)cast_pointer_function((struct GUILD_BATTLE::CGuildBattleReservedScheduleListManager*(*)())&GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Instance)
                },
                _hook_record {
                    (LPVOID)0x1403cd540L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoad14_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoad14_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoad14_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedScheduleListManager::*)(int, unsigned int, int, int))&GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Load)
                },
                _hook_record {
                    (LPVOID)0x1403cdd00L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTodaySchedule16_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTodaySchedule16_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTodaySchedule16_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedScheduleListManager::*)())&GUILD_BATTLE::CGuildBattleReservedScheduleListManager::LoadTodaySchedule)
                },
                _hook_record {
                    (LPVOID)0x1403cdda0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTomorrowSchedule18_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTomorrowSchedule18_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerLoadTomorrowSchedule18_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedScheduleListManager::*)())&GUILD_BATTLE::CGuildBattleReservedScheduleListManager::LoadTomorrowSchedule)
                },
                _hook_record {
                    (LPVOID)0x1403cd690L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerPushDQS20_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerPushDQS20_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerPushDQS20_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleReservedScheduleListManager::*)(unsigned int, unsigned int))&GUILD_BATTLE::CGuildBattleReservedScheduleListManager::PushDQS)
                },
                _hook_record {
                    (LPVOID)0x1403cd830L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerSend22_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerSend22_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerSend22_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleReservedScheduleListManager::*)(unsigned int, int, unsigned int, char, char, unsigned int))&GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Send)
                },
                _hook_record {
                    (LPVOID)0x1403cd720L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateReservedShedule24_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateReservedShedule24_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateReservedShedule24_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedScheduleListManager::*)(unsigned int, char*))&GUILD_BATTLE::CGuildBattleReservedScheduleListManager::UpdateReservedShedule)
                },
                _hook_record {
                    (LPVOID)0x1403cd9c0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTodaySchedule26_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTodaySchedule26_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTodaySchedule26_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedScheduleListManager::*)(unsigned int))&GUILD_BATTLE::CGuildBattleReservedScheduleListManager::UpdateTodaySchedule)
                },
                _hook_record {
                    (LPVOID)0x1403cd7a0L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowComplete28_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowComplete28_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowComplete28_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleReservedScheduleListManager::*)(unsigned int, char*))&GUILD_BATTLE::CGuildBattleReservedScheduleListManager::UpdateTomorrowComplete)
                },
                _hook_record {
                    (LPVOID)0x1403cdb60L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowSchedule30_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowSchedule30_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerUpdateTomorrowSchedule30_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CGuildBattleReservedScheduleListManager::*)(unsigned int))&GUILD_BATTLE::CGuildBattleReservedScheduleListManager::UpdateTomorrowSchedule)
                },
                _hook_record {
                    (LPVOID)0x1403cd300L,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerdtor_CGuildBattleReservedScheduleListManager34_user,
                    (LPVOID *)&GUILD_BATTLE__CGuildBattleReservedScheduleListManagerdtor_CGuildBattleReservedScheduleListManager34_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CGuildBattleReservedScheduleListManagerdtor_CGuildBattleReservedScheduleListManager34_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CGuildBattleReservedScheduleListManager::*)())&GUILD_BATTLE::CGuildBattleReservedScheduleListManager::dtor_CGuildBattleReservedScheduleListManager)
                },
            };
        }; // end namespace Detail
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE
