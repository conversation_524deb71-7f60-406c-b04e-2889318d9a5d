#include <_qry_case_amine_mineore.hpp>


START_ATF_NAMESPACE
    _qry_case_amine_mineore::_qry_case_amine_mineore()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_amine_mineore*);
        (org_ptr(0x1402d4320L))(this);
    };
    void _qry_case_amine_mineore::ctor__qry_case_amine_mineore()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_amine_mineore*);
        (org_ptr(0x1402d4320L))(this);
    };
    int _qry_case_amine_mineore::size()
    {
        using org_ptr = int (WINAPIV*)(struct _qry_case_amine_mineore*);
        return (org_ptr(0x1402d4340L))(this);
    };
END_ATF_NAMESPACE
