// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _userHMETAFILEPICT
    {
        union __MIDL_IWinTypes_0005
        {
            int hInproc;
            _remoteMETAFILEPICT *hRemote;
            __int64 hInproc64;
        };
        int fContext;
        __MIDL_IWinTypes_0005 u;
    };
END_ATF_NAMESPACE
