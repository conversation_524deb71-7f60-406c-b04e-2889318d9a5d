// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <_monster_fld.hpp>


START_ATF_NAMESPACE
    struct __change_monster
    {
        _monster_fld *pMonsterFldA;
        _monster_fld *pMonsterFldB;
        int nProb;
        char *pszIfMissionDescirptCode;
        char *pszifCompleteMsg;
    public:
        __change_monster();
        void ctor___change_monster();
        ~__change_monster();
        void dtor___change_monster();
    };
END_ATF_NAMESPACE
