#include <GUILD_BATTLE__CNormalGuildBattleGuildMemberDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Detail
        {
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberAddGoldCnt2_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberAddGoldCnt2_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberAddGoldCnt2_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberAddGoldCnt2_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberAddKillCnt4_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberAddKillCnt4_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberAddKillCnt4_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberAddKillCnt4_user(nullptr);
            
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberctor_CNormalGuildBattleGuildMember6_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberctor_CNormalGuildBattleGuildMember6_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberctor_CNormalGuildBattleGuildMember6_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberctor_CNormalGuildBattleGuildMember6_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberCleanUpBattle8_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberCleanUpBattle8_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberCleanUpBattle8_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberCleanUpBattle8_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberClear10_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberClear10_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberClear10_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberClear10_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberDecPvpPoint12_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberDecPvpPoint12_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberDecPvpPoint12_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberDecPvpPoint12_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberGetGoalCount14_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberGetGoalCount14_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberGetGoalCount14_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberGetGoalCount14_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberGetIndex16_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberGetIndex16_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberGetIndex16_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberGetIndex16_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberGetKillCount18_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberGetKillCount18_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberGetKillCount18_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberGetKillCount18_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberGetPlayer20_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberGetPlayer20_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberGetPlayer20_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberGetPlayer20_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberGetSerial22_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberGetSerial22_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberGetSerial22_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberGetSerial22_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberIncPvpPoint24_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberIncPvpPoint24_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberIncPvpPoint24_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberIncPvpPoint24_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberIsCommitteeMember26_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberIsCommitteeMember26_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberIsCommitteeMember26_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberIsCommitteeMember26_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEmpty28_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEmpty28_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEmpty28_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEmpty28_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEnableStart30_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEnableStart30_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEnableStart30_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEnableStart30_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberIsExist32_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberIsExist32_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberIsExist32_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberIsExist32_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberIsReStart34_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberIsReStart34_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberIsReStart34_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberIsReStart34_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberJoin36_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberJoin36_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberJoin36_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberJoin36_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberLogin38_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberLogin38_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberLogin38_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberLogin38_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberNetClose40_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberNetClose40_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberNetClose40_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberNetClose40_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberPushDQSPvpPoint42_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberPushDQSPvpPoint42_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberPushDQSPvpPoint42_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberPushDQSPvpPoint42_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnBindPos44_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnBindPos44_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnBindPos44_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnBindPos44_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnStartPos46_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnStartPos46_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnStartPos46_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnStartPos46_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberSend48_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberSend48_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberSend48_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberSend48_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberSetBattleState50_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberSetBattleState50_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberSetBattleState50_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberSetBattleState50_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberSetReStartFlag52_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberSetReStartFlag52_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberSetReStartFlag52_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberSetReStartFlag52_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberStockOldInfo54_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberStockOldInfo54_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberStockOldInfo54_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberStockOldInfo54_user(nullptr);
            
            
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberdtor_CNormalGuildBattleGuildMember56_ptr GUILD_BATTLE__CNormalGuildBattleGuildMemberdtor_CNormalGuildBattleGuildMember56_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleGuildMemberdtor_CNormalGuildBattleGuildMember56_clbk GUILD_BATTLE__CNormalGuildBattleGuildMemberdtor_CNormalGuildBattleGuildMember56_user(nullptr);
            
            void GUILD_BATTLE__CNormalGuildBattleGuildMemberAddGoldCnt2_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildMemberAddGoldCnt2_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberAddGoldCnt2_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildMemberAddKillCnt4_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildMemberAddKillCnt4_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberAddKillCnt4_next);
            };
            
            void GUILD_BATTLE__CNormalGuildBattleGuildMemberctor_CNormalGuildBattleGuildMember6_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildMemberctor_CNormalGuildBattleGuildMember6_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberctor_CNormalGuildBattleGuildMember6_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildMemberCleanUpBattle8_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildMemberCleanUpBattle8_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberCleanUpBattle8_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildMemberClear10_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildMemberClear10_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberClear10_next);
            };
            long double GUILD_BATTLE__CNormalGuildBattleGuildMemberDecPvpPoint12_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this, struct GUILD_BATTLE::CNormalGuildBattleLogger* kLogger)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildMemberDecPvpPoint12_user(_this, kLogger, GUILD_BATTLE__CNormalGuildBattleGuildMemberDecPvpPoint12_next);
            };
            uint16_t GUILD_BATTLE__CNormalGuildBattleGuildMemberGetGoalCount14_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildMemberGetGoalCount14_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberGetGoalCount14_next);
            };
            uint16_t GUILD_BATTLE__CNormalGuildBattleGuildMemberGetIndex16_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildMemberGetIndex16_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberGetIndex16_next);
            };
            uint16_t GUILD_BATTLE__CNormalGuildBattleGuildMemberGetKillCount18_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildMemberGetKillCount18_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberGetKillCount18_next);
            };
            struct CPlayer* GUILD_BATTLE__CNormalGuildBattleGuildMemberGetPlayer20_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildMemberGetPlayer20_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberGetPlayer20_next);
            };
            unsigned int GUILD_BATTLE__CNormalGuildBattleGuildMemberGetSerial22_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildMemberGetSerial22_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberGetSerial22_next);
            };
            long double GUILD_BATTLE__CNormalGuildBattleGuildMemberIncPvpPoint24_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this, long double dInc, struct GUILD_BATTLE::CNormalGuildBattleLogger* kLogger)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildMemberIncPvpPoint24_user(_this, dInc, kLogger, GUILD_BATTLE__CNormalGuildBattleGuildMemberIncPvpPoint24_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleGuildMemberIsCommitteeMember26_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildMemberIsCommitteeMember26_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberIsCommitteeMember26_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEmpty28_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEmpty28_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEmpty28_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEnableStart30_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEnableStart30_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEnableStart30_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleGuildMemberIsExist32_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildMemberIsExist32_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberIsExist32_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleGuildMemberIsReStart34_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleGuildMemberIsReStart34_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberIsReStart34_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildMemberJoin36_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this, struct _guild_member_info* pkMember)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildMemberJoin36_user(_this, pkMember, GUILD_BATTLE__CNormalGuildBattleGuildMemberJoin36_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildMemberLogin38_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildMemberLogin38_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberLogin38_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildMemberNetClose40_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildMemberNetClose40_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberNetClose40_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildMemberPushDQSPvpPoint42_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this, unsigned int dwPvpPoint)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildMemberPushDQSPvpPoint42_user(_this, dwPvpPoint, GUILD_BATTLE__CNormalGuildBattleGuildMemberPushDQSPvpPoint42_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnBindPos44_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnBindPos44_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnBindPos44_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnStartPos46_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnStartPos46_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnStartPos46_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildMemberSend48_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this, char* byType, char* pSend, unsigned int uiSize)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildMemberSend48_user(_this, byType, pSend, uiSize, GUILD_BATTLE__CNormalGuildBattleGuildMemberSend48_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildMemberSetBattleState50_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this, bool bFlag, char byColorInx)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildMemberSetBattleState50_user(_this, bFlag, byColorInx, GUILD_BATTLE__CNormalGuildBattleGuildMemberSetBattleState50_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildMemberSetReStartFlag52_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildMemberSetReStartFlag52_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberSetReStartFlag52_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleGuildMemberStockOldInfo54_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildMemberStockOldInfo54_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberStockOldInfo54_next);
            };
            
            void GUILD_BATTLE__CNormalGuildBattleGuildMemberdtor_CNormalGuildBattleGuildMember56_wrapper(struct GUILD_BATTLE::CNormalGuildBattleGuildMember* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleGuildMemberdtor_CNormalGuildBattleGuildMember56_user(_this, GUILD_BATTLE__CNormalGuildBattleGuildMemberdtor_CNormalGuildBattleGuildMember56_next);
            };
            
            ::std::array<hook_record, 28> CNormalGuildBattleGuildMember_functions = 
            {
                _hook_record {
                    (LPVOID)0x1403eae50L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberAddGoldCnt2_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberAddGoldCnt2_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberAddGoldCnt2_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::AddGoldCnt)
                },
                _hook_record {
                    (LPVOID)0x1403eadf0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberAddKillCnt4_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberAddKillCnt4_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberAddKillCnt4_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::AddKillCnt)
                },
                _hook_record {
                    (LPVOID)0x1403df960L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberctor_CNormalGuildBattleGuildMember6_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberctor_CNormalGuildBattleGuildMember6_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberctor_CNormalGuildBattleGuildMember6_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::ctor_CNormalGuildBattleGuildMember)
                },
                _hook_record {
                    (LPVOID)0x1403e00d0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberCleanUpBattle8_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberCleanUpBattle8_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberCleanUpBattle8_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::CleanUpBattle)
                },
                _hook_record {
                    (LPVOID)0x1403df9b0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberClear10_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberClear10_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberClear10_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::Clear)
                },
                _hook_record {
                    (LPVOID)0x1403dfe10L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberDecPvpPoint12_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberDecPvpPoint12_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberDecPvpPoint12_wrapper),
                    (LPVOID)cast_pointer_function((long double(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)(struct GUILD_BATTLE::CNormalGuildBattleLogger*))&GUILD_BATTLE::CNormalGuildBattleGuildMember::DecPvpPoint)
                },
                _hook_record {
                    (LPVOID)0x1403eb380L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberGetGoalCount14_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberGetGoalCount14_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberGetGoalCount14_wrapper),
                    (LPVOID)cast_pointer_function((uint16_t(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::GetGoalCount)
                },
                _hook_record {
                    (LPVOID)0x1403e0300L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberGetIndex16_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberGetIndex16_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberGetIndex16_wrapper),
                    (LPVOID)cast_pointer_function((uint16_t(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::GetIndex)
                },
                _hook_record {
                    (LPVOID)0x1403eb360L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberGetKillCount18_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberGetKillCount18_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberGetKillCount18_wrapper),
                    (LPVOID)cast_pointer_function((uint16_t(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::GetKillCount)
                },
                _hook_record {
                    (LPVOID)0x1403e0290L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberGetPlayer20_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberGetPlayer20_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberGetPlayer20_wrapper),
                    (LPVOID)cast_pointer_function((struct CPlayer*(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer)
                },
                _hook_record {
                    (LPVOID)0x1403eafa0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberGetSerial22_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberGetSerial22_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberGetSerial22_wrapper),
                    (LPVOID)cast_pointer_function((unsigned int(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::GetSerial)
                },
                _hook_record {
                    (LPVOID)0x1403dfce0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberIncPvpPoint24_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberIncPvpPoint24_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberIncPvpPoint24_wrapper),
                    (LPVOID)cast_pointer_function((long double(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)(long double, struct GUILD_BATTLE::CNormalGuildBattleLogger*))&GUILD_BATTLE::CNormalGuildBattleGuildMember::IncPvpPoint)
                },
                _hook_record {
                    (LPVOID)0x1403e0220L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberIsCommitteeMember26_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberIsCommitteeMember26_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberIsCommitteeMember26_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::IsCommitteeMember)
                },
                _hook_record {
                    (LPVOID)0x1403ead50L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEmpty28_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEmpty28_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEmpty28_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::IsEmpty)
                },
                _hook_record {
                    (LPVOID)0x1403e01b0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEnableStart30_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEnableStart30_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberIsEnableStart30_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::IsEnableStart)
                },
                _hook_record {
                    (LPVOID)0x1403e0130L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberIsExist32_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberIsExist32_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberIsExist32_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::IsExist)
                },
                _hook_record {
                    (LPVOID)0x1403eadd0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberIsReStart34_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberIsReStart34_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberIsReStart34_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::IsReStart)
                },
                _hook_record {
                    (LPVOID)0x1403dfa30L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberJoin36_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberJoin36_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberJoin36_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)(struct _guild_member_info*))&GUILD_BATTLE::CNormalGuildBattleGuildMember::Join)
                },
                _hook_record {
                    (LPVOID)0x1403dfa80L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberLogin38_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberLogin38_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberLogin38_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::Login)
                },
                _hook_record {
                    (LPVOID)0x1403dfae0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberNetClose40_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberNetClose40_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberNetClose40_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::NetClose)
                },
                _hook_record {
                    (LPVOID)0x1403e03f0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberPushDQSPvpPoint42_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberPushDQSPvpPoint42_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberPushDQSPvpPoint42_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)(unsigned int))&GUILD_BATTLE::CNormalGuildBattleGuildMember::PushDQSPvpPoint)
                },
                _hook_record {
                    (LPVOID)0x1403e0020L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnBindPos44_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnBindPos44_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnBindPos44_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::ReturnBindPos)
                },
                _hook_record {
                    (LPVOID)0x1403dffc0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnStartPos46_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnStartPos46_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberReturnStartPos46_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::ReturnStartPos)
                },
                _hook_record {
                    (LPVOID)0x1403e0360L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberSend48_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberSend48_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberSend48_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)(char*, char*, unsigned int))&GUILD_BATTLE::CNormalGuildBattleGuildMember::Send)
                },
                _hook_record {
                    (LPVOID)0x1403dfc80L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberSetBattleState50_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberSetBattleState50_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberSetBattleState50_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)(bool, char))&GUILD_BATTLE::CNormalGuildBattleGuildMember::SetBattleState)
                },
                _hook_record {
                    (LPVOID)0x1403eadb0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberSetReStartFlag52_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberSetReStartFlag52_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberSetReStartFlag52_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::SetReStartFlag)
                },
                _hook_record {
                    (LPVOID)0x1403dfb40L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberStockOldInfo54_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberStockOldInfo54_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberStockOldInfo54_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::StockOldInfo)
                },
                _hook_record {
                    (LPVOID)0x1403df9a0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberdtor_CNormalGuildBattleGuildMember56_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleGuildMemberdtor_CNormalGuildBattleGuildMember56_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleGuildMemberdtor_CNormalGuildBattleGuildMember56_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleGuildMember::*)())&GUILD_BATTLE::CNormalGuildBattleGuildMember::dtor_CNormalGuildBattleGuildMember)
                },
            };
        }; // end namespace Detail
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE
