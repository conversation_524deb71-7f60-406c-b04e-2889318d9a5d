// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>
#include <__dp_mission_potal.hpp>


START_ATF_NAMESPACE
    namespace Info
    {
        
        using __dp_mission_potalctor___dp_mission_potal2_ptr = void (WINAPIV*)(struct __dp_mission_potal*);
        using __dp_mission_potalctor___dp_mission_potal2_clbk = void (WINAPIV*)(struct __dp_mission_potal*, __dp_mission_potalctor___dp_mission_potal2_ptr);
        using __dp_mission_potalfind4_ptr = bool (WINAPIV*)(struct __dp_mission_potal*, char*);
        using __dp_mission_potalfind4_clbk = bool (WINAPIV*)(struct __dp_mission_potal*, char*, __dp_mission_potalfind4_ptr);
        using __dp_mission_potalset6_ptr = void (WINAPIV*)(struct __dp_mission_potal*, char*);
        using __dp_mission_potalset6_clbk = void (WINAPIV*)(struct __dp_mission_potal*, char*, __dp_mission_potalset6_ptr);
        
        using __dp_mission_potaldtor___dp_mission_potal8_ptr = void (WINAPIV*)(struct __dp_mission_potal*);
        using __dp_mission_potaldtor___dp_mission_potal8_clbk = void (WINAPIV*)(struct __dp_mission_potal*, __dp_mission_potaldtor___dp_mission_potal8_ptr);
    }; // end namespace Info
END_ATF_NAMESPACE
