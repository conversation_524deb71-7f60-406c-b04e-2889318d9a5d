#include <CUnmannedTraderUserInfoDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace Detail
    {
        Info::CUnmannedTraderUserInfoBuy2_ptr CUnmannedTraderUserInfoBuy2_next(nullptr);
        Info::CUnmannedTraderUserInfoBuy2_clbk CUnmannedTraderUserInfoBuy2_user(nullptr);
        
        Info::CUnmannedTraderUserInfoBuyComplete4_ptr CUnmannedTraderUserInfoBuyComplete4_next(nullptr);
        Info::CUnmannedTraderUserInfoBuyComplete4_clbk CUnmannedTraderUserInfoBuyComplete4_user(nullptr);
        
        
        Info::CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo6_ptr CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo6_next(nullptr);
        Info::CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo6_clbk CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo6_user(nullptr);
        
        
        Info::CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo8_ptr CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo8_next(nullptr);
        Info::CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo8_clbk CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo8_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCancelRegist10_ptr CUnmannedTraderUserInfoCancelRegist10_next(nullptr);
        Info::CUnmannedTraderUserInfoCancelRegist10_clbk CUnmannedTraderUserInfoCancelRegist10_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCheatCancelRegist12_ptr CUnmannedTraderUserInfoCheatCancelRegist12_next(nullptr);
        Info::CUnmannedTraderUserInfoCheatCancelRegist12_clbk CUnmannedTraderUserInfoCheatCancelRegist12_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCheatCancelRegistAll14_ptr CUnmannedTraderUserInfoCheatCancelRegistAll14_next(nullptr);
        Info::CUnmannedTraderUserInfoCheatCancelRegistAll14_clbk CUnmannedTraderUserInfoCheatCancelRegistAll14_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCheatCancelRegistSingle16_ptr CUnmannedTraderUserInfoCheatCancelRegistSingle16_next(nullptr);
        Info::CUnmannedTraderUserInfoCheatCancelRegistSingle16_clbk CUnmannedTraderUserInfoCheatCancelRegistSingle16_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCheckBuy18_ptr CUnmannedTraderUserInfoCheckBuy18_next(nullptr);
        Info::CUnmannedTraderUserInfoCheckBuy18_clbk CUnmannedTraderUserInfoCheckBuy18_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCheckBuyComplete20_ptr CUnmannedTraderUserInfoCheckBuyComplete20_next(nullptr);
        Info::CUnmannedTraderUserInfoCheckBuyComplete20_clbk CUnmannedTraderUserInfoCheckBuyComplete20_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCheckCancelRegist22_ptr CUnmannedTraderUserInfoCheckCancelRegist22_next(nullptr);
        Info::CUnmannedTraderUserInfoCheckCancelRegist22_clbk CUnmannedTraderUserInfoCheckCancelRegist22_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCheckIsUpdatedTaxRate24_ptr CUnmannedTraderUserInfoCheckIsUpdatedTaxRate24_next(nullptr);
        Info::CUnmannedTraderUserInfoCheckIsUpdatedTaxRate24_clbk CUnmannedTraderUserInfoCheckIsUpdatedTaxRate24_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCheckModifyPrice26_ptr CUnmannedTraderUserInfoCheckModifyPrice26_next(nullptr);
        Info::CUnmannedTraderUserInfoCheckModifyPrice26_clbk CUnmannedTraderUserInfoCheckModifyPrice26_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCheckReRegist28_ptr CUnmannedTraderUserInfoCheckReRegist28_next(nullptr);
        Info::CUnmannedTraderUserInfoCheckReRegist28_clbk CUnmannedTraderUserInfoCheckReRegist28_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCheckRegist30_ptr CUnmannedTraderUserInfoCheckRegist30_next(nullptr);
        Info::CUnmannedTraderUserInfoCheckRegist30_clbk CUnmannedTraderUserInfoCheckRegist30_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCheckSearch32_ptr CUnmannedTraderUserInfoCheckSearch32_next(nullptr);
        Info::CUnmannedTraderUserInfoCheckSearch32_clbk CUnmannedTraderUserInfoCheckSearch32_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCheckSellComplete34_ptr CUnmannedTraderUserInfoCheckSellComplete34_next(nullptr);
        Info::CUnmannedTraderUserInfoCheckSellComplete34_clbk CUnmannedTraderUserInfoCheckSellComplete34_user(nullptr);
        
        Info::CUnmannedTraderUserInfoClear36_ptr CUnmannedTraderUserInfoClear36_next(nullptr);
        Info::CUnmannedTraderUserInfoClear36_clbk CUnmannedTraderUserInfoClear36_user(nullptr);
        
        Info::CUnmannedTraderUserInfoClearLoadItemInfo38_ptr CUnmannedTraderUserInfoClearLoadItemInfo38_next(nullptr);
        Info::CUnmannedTraderUserInfoClearLoadItemInfo38_clbk CUnmannedTraderUserInfoClearLoadItemInfo38_user(nullptr);
        
        Info::CUnmannedTraderUserInfoClearRequest40_ptr CUnmannedTraderUserInfoClearRequest40_next(nullptr);
        Info::CUnmannedTraderUserInfoClearRequest40_clbk CUnmannedTraderUserInfoClearRequest40_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCompleteCancelRegist42_ptr CUnmannedTraderUserInfoCompleteCancelRegist42_next(nullptr);
        Info::CUnmannedTraderUserInfoCompleteCancelRegist42_clbk CUnmannedTraderUserInfoCompleteCancelRegist42_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCompleteCancelRegistItem44_ptr CUnmannedTraderUserInfoCompleteCancelRegistItem44_next(nullptr);
        Info::CUnmannedTraderUserInfoCompleteCancelRegistItem44_clbk CUnmannedTraderUserInfoCompleteCancelRegistItem44_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCompleteCreate46_ptr CUnmannedTraderUserInfoCompleteCreate46_next(nullptr);
        Info::CUnmannedTraderUserInfoCompleteCreate46_clbk CUnmannedTraderUserInfoCompleteCreate46_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCompleteReRegist48_ptr CUnmannedTraderUserInfoCompleteReRegist48_next(nullptr);
        Info::CUnmannedTraderUserInfoCompleteReRegist48_clbk CUnmannedTraderUserInfoCompleteReRegist48_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCompleteReRegistItem50_ptr CUnmannedTraderUserInfoCompleteReRegistItem50_next(nullptr);
        Info::CUnmannedTraderUserInfoCompleteReRegistItem50_clbk CUnmannedTraderUserInfoCompleteReRegistItem50_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCompleteReRegistRollBack52_ptr CUnmannedTraderUserInfoCompleteReRegistRollBack52_next(nullptr);
        Info::CUnmannedTraderUserInfoCompleteReRegistRollBack52_clbk CUnmannedTraderUserInfoCompleteReRegistRollBack52_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCompleteRegist54_ptr CUnmannedTraderUserInfoCompleteRegist54_next(nullptr);
        Info::CUnmannedTraderUserInfoCompleteRegist54_clbk CUnmannedTraderUserInfoCompleteRegist54_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCompleteRegistItem56_ptr CUnmannedTraderUserInfoCompleteRegistItem56_next(nullptr);
        Info::CUnmannedTraderUserInfoCompleteRegistItem56_clbk CUnmannedTraderUserInfoCompleteRegistItem56_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCompleteReprice58_ptr CUnmannedTraderUserInfoCompleteReprice58_next(nullptr);
        Info::CUnmannedTraderUserInfoCompleteReprice58_clbk CUnmannedTraderUserInfoCompleteReprice58_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCompleteRepriceItem60_ptr CUnmannedTraderUserInfoCompleteRepriceItem60_next(nullptr);
        Info::CUnmannedTraderUserInfoCompleteRepriceItem60_clbk CUnmannedTraderUserInfoCompleteRepriceItem60_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCompleteTimeOutClear62_ptr CUnmannedTraderUserInfoCompleteTimeOutClear62_next(nullptr);
        Info::CUnmannedTraderUserInfoCompleteTimeOutClear62_clbk CUnmannedTraderUserInfoCompleteTimeOutClear62_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCompleteUpdateCheatRegistTime64_ptr CUnmannedTraderUserInfoCompleteUpdateCheatRegistTime64_next(nullptr);
        Info::CUnmannedTraderUserInfoCompleteUpdateCheatRegistTime64_clbk CUnmannedTraderUserInfoCompleteUpdateCheatRegistTime64_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCompleteUpdateState66_ptr CUnmannedTraderUserInfoCompleteUpdateState66_next(nullptr);
        Info::CUnmannedTraderUserInfoCompleteUpdateState66_clbk CUnmannedTraderUserInfoCompleteUpdateState66_user(nullptr);
        
        Info::CUnmannedTraderUserInfoCountRegistItem68_ptr CUnmannedTraderUserInfoCountRegistItem68_next(nullptr);
        Info::CUnmannedTraderUserInfoCountRegistItem68_clbk CUnmannedTraderUserInfoCountRegistItem68_user(nullptr);
        
        Info::CUnmannedTraderUserInfoFind70_ptr CUnmannedTraderUserInfoFind70_next(nullptr);
        Info::CUnmannedTraderUserInfoFind70_clbk CUnmannedTraderUserInfoFind70_user(nullptr);
        
        Info::CUnmannedTraderUserInfoFindEmpty72_ptr CUnmannedTraderUserInfoFindEmpty72_next(nullptr);
        Info::CUnmannedTraderUserInfoFindEmpty72_clbk CUnmannedTraderUserInfoFindEmpty72_user(nullptr);
        
        Info::CUnmannedTraderUserInfoFindOwner74_ptr CUnmannedTraderUserInfoFindOwner74_next(nullptr);
        Info::CUnmannedTraderUserInfoFindOwner74_clbk CUnmannedTraderUserInfoFindOwner74_user(nullptr);
        
        Info::CUnmannedTraderUserInfoFindRegist76_ptr CUnmannedTraderUserInfoFindRegist76_next(nullptr);
        Info::CUnmannedTraderUserInfoFindRegist76_clbk CUnmannedTraderUserInfoFindRegist76_user(nullptr);
        
        Info::CUnmannedTraderUserInfoGetCloseItemForPassTimeUpdateInfo78_ptr CUnmannedTraderUserInfoGetCloseItemForPassTimeUpdateInfo78_next(nullptr);
        Info::CUnmannedTraderUserInfoGetCloseItemForPassTimeUpdateInfo78_clbk CUnmannedTraderUserInfoGetCloseItemForPassTimeUpdateInfo78_user(nullptr);
        
        Info::CUnmannedTraderUserInfoGetCurrentRegItemStateStr80_ptr CUnmannedTraderUserInfoGetCurrentRegItemStateStr80_next(nullptr);
        Info::CUnmannedTraderUserInfoGetCurrentRegItemStateStr80_clbk CUnmannedTraderUserInfoGetCurrentRegItemStateStr80_user(nullptr);
        
        Info::CUnmannedTraderUserInfoGetIndex82_ptr CUnmannedTraderUserInfoGetIndex82_next(nullptr);
        Info::CUnmannedTraderUserInfoGetIndex82_clbk CUnmannedTraderUserInfoGetIndex82_user(nullptr);
        
        Info::CUnmannedTraderUserInfoGetMaxRegistCnt84_ptr CUnmannedTraderUserInfoGetMaxRegistCnt84_next(nullptr);
        Info::CUnmannedTraderUserInfoGetMaxRegistCnt84_clbk CUnmannedTraderUserInfoGetMaxRegistCnt84_user(nullptr);
        
        Info::CUnmannedTraderUserInfoGetRegItemInfo86_ptr CUnmannedTraderUserInfoGetRegItemInfo86_next(nullptr);
        Info::CUnmannedTraderUserInfoGetRegItemInfo86_clbk CUnmannedTraderUserInfoGetRegItemInfo86_user(nullptr);
        
        Info::CUnmannedTraderUserInfoGetSerial88_ptr CUnmannedTraderUserInfoGetSerial88_next(nullptr);
        Info::CUnmannedTraderUserInfoGetSerial88_clbk CUnmannedTraderUserInfoGetSerial88_user(nullptr);
        
        Info::CUnmannedTraderUserInfoInit90_ptr CUnmannedTraderUserInfoInit90_next(nullptr);
        Info::CUnmannedTraderUserInfoInit90_clbk CUnmannedTraderUserInfoInit90_user(nullptr);
        
        Info::CUnmannedTraderUserInfoIsLogInState92_ptr CUnmannedTraderUserInfoIsLogInState92_next(nullptr);
        Info::CUnmannedTraderUserInfoIsLogInState92_clbk CUnmannedTraderUserInfoIsLogInState92_user(nullptr);
        
        Info::CUnmannedTraderUserInfoIsNull94_ptr CUnmannedTraderUserInfoIsNull94_next(nullptr);
        Info::CUnmannedTraderUserInfoIsNull94_clbk CUnmannedTraderUserInfoIsNull94_user(nullptr);
        
        Info::CUnmannedTraderUserInfoLoad96_ptr CUnmannedTraderUserInfoLoad96_next(nullptr);
        Info::CUnmannedTraderUserInfoLoad96_clbk CUnmannedTraderUserInfoLoad96_user(nullptr);
        
        Info::CUnmannedTraderUserInfoLogOut98_ptr CUnmannedTraderUserInfoLogOut98_next(nullptr);
        Info::CUnmannedTraderUserInfoLogOut98_clbk CUnmannedTraderUserInfoLogOut98_user(nullptr);
        
        Info::CUnmannedTraderUserInfoModifyPrice100_ptr CUnmannedTraderUserInfoModifyPrice100_next(nullptr);
        Info::CUnmannedTraderUserInfoModifyPrice100_clbk CUnmannedTraderUserInfoModifyPrice100_user(nullptr);
        
        Info::CUnmannedTraderUserInfoNotifyCloseItem102_ptr CUnmannedTraderUserInfoNotifyCloseItem102_next(nullptr);
        Info::CUnmannedTraderUserInfoNotifyCloseItem102_clbk CUnmannedTraderUserInfoNotifyCloseItem102_user(nullptr);
        
        Info::CUnmannedTraderUserInfoNotifyRegistItem104_ptr CUnmannedTraderUserInfoNotifyRegistItem104_next(nullptr);
        Info::CUnmannedTraderUserInfoNotifyRegistItem104_clbk CUnmannedTraderUserInfoNotifyRegistItem104_user(nullptr);
        
        Info::CUnmannedTraderUserInfoPrcoSellUpdateWaitItem106_ptr CUnmannedTraderUserInfoPrcoSellUpdateWaitItem106_next(nullptr);
        Info::CUnmannedTraderUserInfoPrcoSellUpdateWaitItem106_clbk CUnmannedTraderUserInfoPrcoSellUpdateWaitItem106_user(nullptr);
        
        Info::CUnmannedTraderUserInfoProcSellWaitItem108_ptr CUnmannedTraderUserInfoProcSellWaitItem108_next(nullptr);
        Info::CUnmannedTraderUserInfoProcSellWaitItem108_clbk CUnmannedTraderUserInfoProcSellWaitItem108_user(nullptr);
        
        Info::CUnmannedTraderUserInfoReRegist110_ptr CUnmannedTraderUserInfoReRegist110_next(nullptr);
        Info::CUnmannedTraderUserInfoReRegist110_clbk CUnmannedTraderUserInfoReRegist110_user(nullptr);
        
        Info::CUnmannedTraderUserInfoRegist112_ptr CUnmannedTraderUserInfoRegist112_next(nullptr);
        Info::CUnmannedTraderUserInfoRegist112_clbk CUnmannedTraderUserInfoRegist112_user(nullptr);
        
        Info::CUnmannedTraderUserInfoRegistItem114_ptr CUnmannedTraderUserInfoRegistItem114_next(nullptr);
        Info::CUnmannedTraderUserInfoRegistItem114_clbk CUnmannedTraderUserInfoRegistItem114_user(nullptr);
        
        Info::CUnmannedTraderUserInfoSearch116_ptr CUnmannedTraderUserInfoSearch116_next(nullptr);
        Info::CUnmannedTraderUserInfoSearch116_clbk CUnmannedTraderUserInfoSearch116_user(nullptr);
        
        Info::CUnmannedTraderUserInfoSellComplete118_ptr CUnmannedTraderUserInfoSellComplete118_next(nullptr);
        Info::CUnmannedTraderUserInfoSellComplete118_clbk CUnmannedTraderUserInfoSellComplete118_user(nullptr);
        
        Info::CUnmannedTraderUserInfoSendBuyErrorResult120_ptr CUnmannedTraderUserInfoSendBuyErrorResult120_next(nullptr);
        Info::CUnmannedTraderUserInfoSendBuyErrorResult120_clbk CUnmannedTraderUserInfoSendBuyErrorResult120_user(nullptr);
        
        Info::CUnmannedTraderUserInfoSendCancelRegistErrorResult122_ptr CUnmannedTraderUserInfoSendCancelRegistErrorResult122_next(nullptr);
        Info::CUnmannedTraderUserInfoSendCancelRegistErrorResult122_clbk CUnmannedTraderUserInfoSendCancelRegistErrorResult122_user(nullptr);
        
        Info::CUnmannedTraderUserInfoSendCancelRegistSuccessResult124_ptr CUnmannedTraderUserInfoSendCancelRegistSuccessResult124_next(nullptr);
        Info::CUnmannedTraderUserInfoSendCancelRegistSuccessResult124_clbk CUnmannedTraderUserInfoSendCancelRegistSuccessResult124_user(nullptr);
        
        Info::CUnmannedTraderUserInfoSendNotifyCloseItem126_ptr CUnmannedTraderUserInfoSendNotifyCloseItem126_next(nullptr);
        Info::CUnmannedTraderUserInfoSendNotifyCloseItem126_clbk CUnmannedTraderUserInfoSendNotifyCloseItem126_user(nullptr);
        
        Info::CUnmannedTraderUserInfoSendRegistItemErrorResult128_ptr CUnmannedTraderUserInfoSendRegistItemErrorResult128_next(nullptr);
        Info::CUnmannedTraderUserInfoSendRegistItemErrorResult128_clbk CUnmannedTraderUserInfoSendRegistItemErrorResult128_user(nullptr);
        
        Info::CUnmannedTraderUserInfoSendRegistItemSuccessResult130_ptr CUnmannedTraderUserInfoSendRegistItemSuccessResult130_next(nullptr);
        Info::CUnmannedTraderUserInfoSendRegistItemSuccessResult130_clbk CUnmannedTraderUserInfoSendRegistItemSuccessResult130_user(nullptr);
        
        Info::CUnmannedTraderUserInfoSendRepriceErrorResult132_ptr CUnmannedTraderUserInfoSendRepriceErrorResult132_next(nullptr);
        Info::CUnmannedTraderUserInfoSendRepriceErrorResult132_clbk CUnmannedTraderUserInfoSendRepriceErrorResult132_user(nullptr);
        
        Info::CUnmannedTraderUserInfoSendRepriceSuccessResult134_ptr CUnmannedTraderUserInfoSendRepriceSuccessResult134_next(nullptr);
        Info::CUnmannedTraderUserInfoSendRepriceSuccessResult134_clbk CUnmannedTraderUserInfoSendRepriceSuccessResult134_user(nullptr);
        
        Info::CUnmannedTraderUserInfoSendSearchErrorResult136_ptr CUnmannedTraderUserInfoSendSearchErrorResult136_next(nullptr);
        Info::CUnmannedTraderUserInfoSendSearchErrorResult136_clbk CUnmannedTraderUserInfoSendSearchErrorResult136_user(nullptr);
        
        Info::CUnmannedTraderUserInfoSendSearchResult138_ptr CUnmannedTraderUserInfoSendSearchResult138_next(nullptr);
        Info::CUnmannedTraderUserInfoSendSearchResult138_clbk CUnmannedTraderUserInfoSendSearchResult138_user(nullptr);
        
        Info::CUnmannedTraderUserInfoSendSellInfom140_ptr CUnmannedTraderUserInfoSendSellInfom140_next(nullptr);
        Info::CUnmannedTraderUserInfoSendSellInfom140_clbk CUnmannedTraderUserInfoSendSellInfom140_user(nullptr);
        
        Info::CUnmannedTraderUserInfoSetAllItemState142_ptr CUnmannedTraderUserInfoSetAllItemState142_next(nullptr);
        Info::CUnmannedTraderUserInfoSetAllItemState142_clbk CUnmannedTraderUserInfoSetAllItemState142_user(nullptr);
        
        Info::CUnmannedTraderUserInfoSetCompleteInfo144_ptr CUnmannedTraderUserInfoSetCompleteInfo144_next(nullptr);
        Info::CUnmannedTraderUserInfoSetCompleteInfo144_clbk CUnmannedTraderUserInfoSetCompleteInfo144_user(nullptr);
        
        Info::CUnmannedTraderUserInfoSetLoadInfo146_ptr CUnmannedTraderUserInfoSetLoadInfo146_next(nullptr);
        Info::CUnmannedTraderUserInfoSetLoadInfo146_clbk CUnmannedTraderUserInfoSetLoadInfo146_user(nullptr);
        
        
        Info::CUnmannedTraderUserInfodtor_CUnmannedTraderUserInfo154_ptr CUnmannedTraderUserInfodtor_CUnmannedTraderUserInfo154_next(nullptr);
        Info::CUnmannedTraderUserInfodtor_CUnmannedTraderUserInfo154_clbk CUnmannedTraderUserInfodtor_CUnmannedTraderUserInfo154_user(nullptr);
        
        void CUnmannedTraderUserInfoBuy2_wrapper(struct CUnmannedTraderUserInfo* _this, char byType, struct _unmannedtrader_buy_item_request_clzo* pRequest, struct CLogFile* pkLogger)
        {
           CUnmannedTraderUserInfoBuy2_user(_this, byType, pRequest, pkLogger, CUnmannedTraderUserInfoBuy2_next);
        };
        char CUnmannedTraderUserInfoBuyComplete4_wrapper(struct CUnmannedTraderUserInfo* _this, struct CPlayer* pkBuyer, unsigned int dwSellerSerial, char* wszSellerName, char* szSellerAccountName, unsigned int dwRegistSerial, unsigned int dwK, uint64_t dwD, unsigned int dwU, unsigned int dwPrice, uint64_t lnUID, struct CLogFile* pkLogger, uint16_t* wAddItemSerial)
        {
           return CUnmannedTraderUserInfoBuyComplete4_user(_this, pkBuyer, dwSellerSerial, wszSellerName, szSellerAccountName, dwRegistSerial, dwK, dwD, dwU, dwPrice, lnUID, pkLogger, wAddItemSerial, CUnmannedTraderUserInfoBuyComplete4_next);
        };
        
        void CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo6_wrapper(struct CUnmannedTraderUserInfo* _this, struct CUnmannedTraderUserInfo* __that)
        {
           CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo6_user(_this, __that, CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo6_next);
        };
        
        void CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo8_wrapper(struct CUnmannedTraderUserInfo* _this)
        {
           CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo8_user(_this, CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo8_next);
        };
        void CUnmannedTraderUserInfoCancelRegist10_wrapper(struct CUnmannedTraderUserInfo* _this, char byType, struct _a_trade_clear_item_request_clzo* pRequest, struct CLogFile* pkLogger)
        {
           CUnmannedTraderUserInfoCancelRegist10_user(_this, byType, pRequest, pkLogger, CUnmannedTraderUserInfoCancelRegist10_next);
        };
        bool CUnmannedTraderUserInfoCheatCancelRegist12_wrapper(struct CUnmannedTraderUserInfo* _this, char byNth)
        {
           return CUnmannedTraderUserInfoCheatCancelRegist12_user(_this, byNth, CUnmannedTraderUserInfoCheatCancelRegist12_next);
        };
        bool CUnmannedTraderUserInfoCheatCancelRegistAll14_wrapper(struct CUnmannedTraderUserInfo* _this)
        {
           return CUnmannedTraderUserInfoCheatCancelRegistAll14_user(_this, CUnmannedTraderUserInfoCheatCancelRegistAll14_next);
        };
        bool CUnmannedTraderUserInfoCheatCancelRegistSingle16_wrapper(struct CUnmannedTraderUserInfo* _this, char byNth)
        {
           return CUnmannedTraderUserInfoCheatCancelRegistSingle16_user(_this, byNth, CUnmannedTraderUserInfoCheatCancelRegistSingle16_next);
        };
        char CUnmannedTraderUserInfoCheckBuy18_wrapper(struct CUnmannedTraderUserInfo* _this, char byType, struct _unmannedtrader_buy_item_request_clzo* pRequest, struct CPlayer** pkBuyer, struct CLogFile* pkLogger)
        {
           return CUnmannedTraderUserInfoCheckBuy18_user(_this, byType, pRequest, pkBuyer, pkLogger, CUnmannedTraderUserInfoCheckBuy18_next);
        };
        char CUnmannedTraderUserInfoCheckBuyComplete20_wrapper(struct CUnmannedTraderUserInfo* _this, struct CPlayer* pkBuyer, unsigned int dwPrice)
        {
           return CUnmannedTraderUserInfoCheckBuyComplete20_user(_this, pkBuyer, dwPrice, CUnmannedTraderUserInfoCheckBuyComplete20_next);
        };
        char CUnmannedTraderUserInfoCheckCancelRegist22_wrapper(struct CUnmannedTraderUserInfo* _this, char byType, struct _a_trade_clear_item_request_clzo* pRequest, struct CLogFile* pkLogger)
        {
           return CUnmannedTraderUserInfoCheckCancelRegist22_user(_this, byType, pRequest, pkLogger, CUnmannedTraderUserInfoCheckCancelRegist22_next);
        };
        bool CUnmannedTraderUserInfoCheckIsUpdatedTaxRate24_wrapper(struct CUnmannedTraderUserInfo* _this, char byTax, struct CLogFile* pkLogger)
        {
           return CUnmannedTraderUserInfoCheckIsUpdatedTaxRate24_user(_this, byTax, pkLogger, CUnmannedTraderUserInfoCheckIsUpdatedTaxRate24_next);
        };
        char CUnmannedTraderUserInfoCheckModifyPrice26_wrapper(struct CUnmannedTraderUserInfo* _this, char byType, struct _a_trade_adjust_price_request_clzo* pRequest, unsigned int* dwOldPrice, struct CLogFile* pkLogger, unsigned int* pdwTax)
        {
           return CUnmannedTraderUserInfoCheckModifyPrice26_user(_this, byType, pRequest, dwOldPrice, pkLogger, pdwTax, CUnmannedTraderUserInfoCheckModifyPrice26_next);
        };
        char CUnmannedTraderUserInfoCheckReRegist28_wrapper(struct CUnmannedTraderUserInfo* _this, char byType, struct CLogFile* pkLogger, uint16_t wItemSerial, char byAmount, char byItemTableCode, uint16_t wItemIndex, unsigned int dwRegistSerial, unsigned int dwPrice, char* pbyDivision, char* pbyClass, char* pbySubClass, unsigned int* pdwTax, unsigned int* pdwListIndex)
        {
           return CUnmannedTraderUserInfoCheckReRegist28_user(_this, byType, pkLogger, wItemSerial, byAmount, byItemTableCode, wItemIndex, dwRegistSerial, dwPrice, pbyDivision, pbyClass, pbySubClass, pdwTax, pdwListIndex, CUnmannedTraderUserInfoCheckReRegist28_next);
        };
        char CUnmannedTraderUserInfoCheckRegist30_wrapper(struct CUnmannedTraderUserInfo* _this, char byType, struct _a_trade_reg_item_request_clzo* pRequest, struct CLogFile* pkLogger, char* byTempSlotIndex, char* byDivision, char* byClass, char* bySubClass, unsigned int* dwListIndex, unsigned int* dwTax)
        {
           return CUnmannedTraderUserInfoCheckRegist30_user(_this, byType, pRequest, pkLogger, byTempSlotIndex, byDivision, byClass, bySubClass, dwListIndex, dwTax, CUnmannedTraderUserInfoCheckRegist30_next);
        };
        char CUnmannedTraderUserInfoCheckSearch32_wrapper(struct CUnmannedTraderUserInfo* _this, char byType, struct _unmannedtrader_search_list_request_clzo* pRequest, unsigned int* dwListIndex, unsigned int* dwCurVer, struct CLogFile* pkLogger)
        {
           return CUnmannedTraderUserInfoCheckSearch32_user(_this, byType, pRequest, dwListIndex, dwCurVer, pkLogger, CUnmannedTraderUserInfoCheckSearch32_next);
        };
        char CUnmannedTraderUserInfoCheckSellComplete34_wrapper(struct CUnmannedTraderUserInfo* _this, struct CPlayer* pkSellPlayer, struct CPlayer* pkBuyer, unsigned int dwRegistSerial, unsigned int dwRealPrice, struct CLogFile* pkLogger)
        {
           return CUnmannedTraderUserInfoCheckSellComplete34_user(_this, pkSellPlayer, pkBuyer, dwRegistSerial, dwRealPrice, pkLogger, CUnmannedTraderUserInfoCheckSellComplete34_next);
        };
        void CUnmannedTraderUserInfoClear36_wrapper(struct CUnmannedTraderUserInfo* _this)
        {
           CUnmannedTraderUserInfoClear36_user(_this, CUnmannedTraderUserInfoClear36_next);
        };
        void CUnmannedTraderUserInfoClearLoadItemInfo38_wrapper(struct CUnmannedTraderUserInfo* _this)
        {
           CUnmannedTraderUserInfoClearLoadItemInfo38_user(_this, CUnmannedTraderUserInfoClearLoadItemInfo38_next);
        };
        void CUnmannedTraderUserInfoClearRequest40_wrapper(struct CUnmannedTraderUserInfo* _this)
        {
           CUnmannedTraderUserInfoClearRequest40_user(_this, CUnmannedTraderUserInfoClearRequest40_next);
        };
        void CUnmannedTraderUserInfoCompleteCancelRegist42_wrapper(struct CUnmannedTraderUserInfo* _this, char byRet, char* pLoadData, struct CLogFile* pkLogger)
        {
           CUnmannedTraderUserInfoCompleteCancelRegist42_user(_this, byRet, pLoadData, pkLogger, CUnmannedTraderUserInfoCompleteCancelRegist42_next);
        };
        bool CUnmannedTraderUserInfoCompleteCancelRegistItem44_wrapper(struct CUnmannedTraderUserInfo* _this, unsigned int dwRegistSerial, uint16_t dwItemSerial, struct CLogFile* pkLogger)
        {
           return CUnmannedTraderUserInfoCompleteCancelRegistItem44_user(_this, dwRegistSerial, dwItemSerial, pkLogger, CUnmannedTraderUserInfoCompleteCancelRegistItem44_next);
        };
        void CUnmannedTraderUserInfoCompleteCreate46_wrapper(struct CUnmannedTraderUserInfo* _this, struct CLogFile* pkLogger)
        {
           CUnmannedTraderUserInfoCompleteCreate46_user(_this, pkLogger, CUnmannedTraderUserInfoCompleteCreate46_next);
        };
        void CUnmannedTraderUserInfoCompleteReRegist48_wrapper(struct CUnmannedTraderUserInfo* _this, char* pLoadData, struct CLogFile* pkLogger)
        {
           CUnmannedTraderUserInfoCompleteReRegist48_user(_this, pLoadData, pkLogger, CUnmannedTraderUserInfoCompleteReRegist48_next);
        };
        bool CUnmannedTraderUserInfoCompleteReRegistItem50_wrapper(struct CUnmannedTraderUserInfo* _this, unsigned int dwRegistSerial, uint16_t dwItemSerial, unsigned int dwPrice, struct CLogFile* pkLogger, char* pbyProcRet)
        {
           return CUnmannedTraderUserInfoCompleteReRegistItem50_user(_this, dwRegistSerial, dwItemSerial, dwPrice, pkLogger, pbyProcRet, CUnmannedTraderUserInfoCompleteReRegistItem50_next);
        };
        void CUnmannedTraderUserInfoCompleteReRegistRollBack52_wrapper(struct CUnmannedTraderUserInfo* _this, char* pData, struct CLogFile* pkLogger)
        {
           CUnmannedTraderUserInfoCompleteReRegistRollBack52_user(_this, pData, pkLogger, CUnmannedTraderUserInfoCompleteReRegistRollBack52_next);
        };
        void CUnmannedTraderUserInfoCompleteRegist54_wrapper(struct CUnmannedTraderUserInfo* _this, char byRet, char* pLoadData, struct CLogFile* pkLogger)
        {
           CUnmannedTraderUserInfoCompleteRegist54_user(_this, byRet, pLoadData, pkLogger, CUnmannedTraderUserInfoCompleteRegist54_next);
        };
        bool CUnmannedTraderUserInfoCompleteRegistItem56_wrapper(struct CUnmannedTraderUserInfo* _this, unsigned int dwRegistSerial, uint16_t dwItemSerial, unsigned int dwETSerialNumber, unsigned int dwPrice, char bySellTurm, char byTableCode, uint16_t wItemIndex, char byStorageIndex, uint64_t dwD, unsigned int dwU, bool bInserted)
        {
           return CUnmannedTraderUserInfoCompleteRegistItem56_user(_this, dwRegistSerial, dwItemSerial, dwETSerialNumber, dwPrice, bySellTurm, byTableCode, wItemIndex, byStorageIndex, dwD, dwU, bInserted, CUnmannedTraderUserInfoCompleteRegistItem56_next);
        };
        void CUnmannedTraderUserInfoCompleteReprice58_wrapper(struct CUnmannedTraderUserInfo* _this, char byRet, char* pLoadData, struct CLogFile* pkLogger)
        {
           CUnmannedTraderUserInfoCompleteReprice58_user(_this, byRet, pLoadData, pkLogger, CUnmannedTraderUserInfoCompleteReprice58_next);
        };
        bool CUnmannedTraderUserInfoCompleteRepriceItem60_wrapper(struct CUnmannedTraderUserInfo* _this, unsigned int dwRegistSerial, uint16_t dwItemSerial, unsigned int dwPrice)
        {
           return CUnmannedTraderUserInfoCompleteRepriceItem60_user(_this, dwRegistSerial, dwItemSerial, dwPrice, CUnmannedTraderUserInfoCompleteRepriceItem60_next);
        };
        void CUnmannedTraderUserInfoCompleteTimeOutClear62_wrapper(struct CUnmannedTraderUserInfo* _this, unsigned int dwRegistSerial, struct CLogFile* pkLogger)
        {
           CUnmannedTraderUserInfoCompleteTimeOutClear62_user(_this, dwRegistSerial, pkLogger, CUnmannedTraderUserInfoCompleteTimeOutClear62_next);
        };
        void CUnmannedTraderUserInfoCompleteUpdateCheatRegistTime64_wrapper(struct CUnmannedTraderUserInfo* _this, char* pLoadData)
        {
           CUnmannedTraderUserInfoCompleteUpdateCheatRegistTime64_user(_this, pLoadData, CUnmannedTraderUserInfoCompleteUpdateCheatRegistTime64_next);
        };
        bool CUnmannedTraderUserInfoCompleteUpdateState66_wrapper(struct CUnmannedTraderUserInfo* _this, unsigned int dwRegistSerial, char byState, bool bReCountRegist)
        {
           return CUnmannedTraderUserInfoCompleteUpdateState66_user(_this, dwRegistSerial, byState, bReCountRegist, CUnmannedTraderUserInfoCompleteUpdateState66_next);
        };
        void CUnmannedTraderUserInfoCountRegistItem68_wrapper(struct CUnmannedTraderUserInfo* _this)
        {
           CUnmannedTraderUserInfoCountRegistItem68_user(_this, CUnmannedTraderUserInfoCountRegistItem68_next);
        };
        struct std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> >* CUnmannedTraderUserInfoFind70_wrapper(struct CUnmannedTraderUserInfo* _this, struct std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> >* result, unsigned int dwRegistSerial)
        {
           return CUnmannedTraderUserInfoFind70_user(_this, result, dwRegistSerial, CUnmannedTraderUserInfoFind70_next);
        };
        struct std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> >* CUnmannedTraderUserInfoFindEmpty72_wrapper(struct CUnmannedTraderUserInfo* _this, struct std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> >* result)
        {
           return CUnmannedTraderUserInfoFindEmpty72_user(_this, result, CUnmannedTraderUserInfoFindEmpty72_next);
        };
        struct CPlayer* CUnmannedTraderUserInfoFindOwner74_wrapper(struct CUnmannedTraderUserInfo* _this)
        {
           return CUnmannedTraderUserInfoFindOwner74_user(_this, CUnmannedTraderUserInfoFindOwner74_next);
        };
        struct std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> >* CUnmannedTraderUserInfoFindRegist76_wrapper(struct CUnmannedTraderUserInfo* _this, struct std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> >* result, struct std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > iterStart)
        {
           return CUnmannedTraderUserInfoFindRegist76_user(_this, result, iterStart, CUnmannedTraderUserInfoFindRegist76_next);
        };
        CUnmannedTraderItemState::STATE CUnmannedTraderUserInfoGetCloseItemForPassTimeUpdateInfo78_wrapper(struct CUnmannedTraderUserInfo* _this, unsigned int dwRegistSerial, struct CPlayer** pkOwner)
        {
           return CUnmannedTraderUserInfoGetCloseItemForPassTimeUpdateInfo78_user(_this, dwRegistSerial, pkOwner, CUnmannedTraderUserInfoGetCloseItemForPassTimeUpdateInfo78_next);
        };
        void CUnmannedTraderUserInfoGetCurrentRegItemStateStr80_wrapper(struct CUnmannedTraderUserInfo* _this, char* szStateStr, int iBuffSize)
        {
           CUnmannedTraderUserInfoGetCurrentRegItemStateStr80_user(_this, szStateStr, iBuffSize, CUnmannedTraderUserInfoGetCurrentRegItemStateStr80_next);
        };
        uint16_t CUnmannedTraderUserInfoGetIndex82_wrapper(struct CUnmannedTraderUserInfo* _this)
        {
           return CUnmannedTraderUserInfoGetIndex82_user(_this, CUnmannedTraderUserInfoGetIndex82_next);
        };
        char CUnmannedTraderUserInfoGetMaxRegistCnt84_wrapper(struct CUnmannedTraderUserInfo* _this)
        {
           return CUnmannedTraderUserInfoGetMaxRegistCnt84_user(_this, CUnmannedTraderUserInfoGetMaxRegistCnt84_next);
        };
        struct CUnmannedTraderRegistItemInfo* CUnmannedTraderUserInfoGetRegItemInfo86_wrapper(struct CUnmannedTraderUserInfo* _this)
        {
           return CUnmannedTraderUserInfoGetRegItemInfo86_user(_this, CUnmannedTraderUserInfoGetRegItemInfo86_next);
        };
        unsigned int CUnmannedTraderUserInfoGetSerial88_wrapper(struct CUnmannedTraderUserInfo* _this)
        {
           return CUnmannedTraderUserInfoGetSerial88_user(_this, CUnmannedTraderUserInfoGetSerial88_next);
        };
        bool CUnmannedTraderUserInfoInit90_wrapper(struct CUnmannedTraderUserInfo* _this, uint16_t wInx)
        {
           return CUnmannedTraderUserInfoInit90_user(_this, wInx, CUnmannedTraderUserInfoInit90_next);
        };
        bool CUnmannedTraderUserInfoIsLogInState92_wrapper(struct CUnmannedTraderUserInfo* _this)
        {
           return CUnmannedTraderUserInfoIsLogInState92_user(_this, CUnmannedTraderUserInfoIsLogInState92_next);
        };
        bool CUnmannedTraderUserInfoIsNull94_wrapper(struct CUnmannedTraderUserInfo* _this)
        {
           return CUnmannedTraderUserInfoIsNull94_user(_this, CUnmannedTraderUserInfoIsNull94_next);
        };
        bool CUnmannedTraderUserInfoLoad96_wrapper(struct CUnmannedTraderUserInfo* _this, char byType, uint16_t wInx, unsigned int dwSerial, struct _TRADE_DB_BASE* kInfo, struct CLogFile* pkLogger)
        {
           return CUnmannedTraderUserInfoLoad96_user(_this, byType, wInx, dwSerial, kInfo, pkLogger, CUnmannedTraderUserInfoLoad96_next);
        };
        void CUnmannedTraderUserInfoLogOut98_wrapper(struct CUnmannedTraderUserInfo* _this, unsigned int dwSerial, struct CLogFile* pkLogger)
        {
           CUnmannedTraderUserInfoLogOut98_user(_this, dwSerial, pkLogger, CUnmannedTraderUserInfoLogOut98_next);
        };
        void CUnmannedTraderUserInfoModifyPrice100_wrapper(struct CUnmannedTraderUserInfo* _this, char byType, struct _a_trade_adjust_price_request_clzo* pRequest, struct CLogFile* pkLogger)
        {
           CUnmannedTraderUserInfoModifyPrice100_user(_this, byType, pRequest, pkLogger, CUnmannedTraderUserInfoModifyPrice100_next);
        };
        void CUnmannedTraderUserInfoNotifyCloseItem102_wrapper(struct CUnmannedTraderUserInfo* _this, struct _qry_case_unmandtrader_log_in_proc_update_complete* pkResult, struct CLogFile* pkLogger)
        {
           CUnmannedTraderUserInfoNotifyCloseItem102_user(_this, pkResult, pkLogger, CUnmannedTraderUserInfoNotifyCloseItem102_next);
        };
        void CUnmannedTraderUserInfoNotifyRegistItem104_wrapper(struct CUnmannedTraderUserInfo* _this)
        {
           CUnmannedTraderUserInfoNotifyRegistItem104_user(_this, CUnmannedTraderUserInfoNotifyRegistItem104_next);
        };
        void CUnmannedTraderUserInfoPrcoSellUpdateWaitItem106_wrapper(struct CUnmannedTraderUserInfo* _this, struct _qry_case_unmandtrader_log_in_proc_update_complete* pkResult, char byGroupType, struct CLogFile* pkLogger)
        {
           CUnmannedTraderUserInfoPrcoSellUpdateWaitItem106_user(_this, pkResult, byGroupType, pkLogger, CUnmannedTraderUserInfoPrcoSellUpdateWaitItem106_next);
        };
        void CUnmannedTraderUserInfoProcSellWaitItem108_wrapper(struct CUnmannedTraderUserInfo* _this, struct _qry_case_unmandtrader_log_in_proc_update_complete* pkResult, char byGroupType, struct CLogFile* pkLogger)
        {
           CUnmannedTraderUserInfoProcSellWaitItem108_user(_this, pkResult, byGroupType, pkLogger, CUnmannedTraderUserInfoProcSellWaitItem108_next);
        };
        void CUnmannedTraderUserInfoReRegist110_wrapper(struct CUnmannedTraderUserInfo* _this, char byType, struct _unmannedtrader_re_regist_request_clzo* pRequest, struct CLogFile* pkLogger)
        {
           CUnmannedTraderUserInfoReRegist110_user(_this, byType, pRequest, pkLogger, CUnmannedTraderUserInfoReRegist110_next);
        };
        void CUnmannedTraderUserInfoRegist112_wrapper(struct CUnmannedTraderUserInfo* _this, char byType, struct _a_trade_reg_item_request_clzo* pRequest, struct CLogFile* pkLogger)
        {
           CUnmannedTraderUserInfoRegist112_user(_this, byType, pRequest, pkLogger, CUnmannedTraderUserInfoRegist112_next);
        };
        char CUnmannedTraderUserInfoRegistItem114_wrapper(struct CUnmannedTraderUserInfo* _this, char byType, struct _a_trade_reg_item_request_clzo* pRequest, char byTempSlotIndex, char byDivision, char byClass, char bySubClass, unsigned int dwListIndex, unsigned int dwTax)
        {
           return CUnmannedTraderUserInfoRegistItem114_user(_this, byType, pRequest, byTempSlotIndex, byDivision, byClass, bySubClass, dwListIndex, dwTax, CUnmannedTraderUserInfoRegistItem114_next);
        };
        void CUnmannedTraderUserInfoSearch116_wrapper(struct CUnmannedTraderUserInfo* _this, char byType, struct _unmannedtrader_search_list_request_clzo* pRequest, struct CLogFile* pkLogger)
        {
           CUnmannedTraderUserInfoSearch116_user(_this, byType, pRequest, pkLogger, CUnmannedTraderUserInfoSearch116_next);
        };
        char CUnmannedTraderUserInfoSellComplete118_wrapper(struct CUnmannedTraderUserInfo* _this, struct CPlayer* pkSellPlayer, struct CPlayer* pkBuyer, unsigned int dwOriPrice, unsigned int dwRealPrice, unsigned int dwTax, unsigned int dwRegistSerial, int64_t tResultTime, struct CLogFile* pkLogger)
        {
           return CUnmannedTraderUserInfoSellComplete118_user(_this, pkSellPlayer, pkBuyer, dwOriPrice, dwRealPrice, dwTax, dwRegistSerial, tResultTime, pkLogger, CUnmannedTraderUserInfoSellComplete118_next);
        };
        void CUnmannedTraderUserInfoSendBuyErrorResult120_wrapper(struct CUnmannedTraderUserInfo* _this, uint16_t wInx, char byRet)
        {
           CUnmannedTraderUserInfoSendBuyErrorResult120_user(_this, wInx, byRet, CUnmannedTraderUserInfoSendBuyErrorResult120_next);
        };
        void CUnmannedTraderUserInfoSendCancelRegistErrorResult122_wrapper(struct CUnmannedTraderUserInfo* _this, uint16_t wInx, char byRet)
        {
           CUnmannedTraderUserInfoSendCancelRegistErrorResult122_user(_this, wInx, byRet, CUnmannedTraderUserInfoSendCancelRegistErrorResult122_next);
        };
        void CUnmannedTraderUserInfoSendCancelRegistSuccessResult124_wrapper(struct CUnmannedTraderUserInfo* _this, uint16_t wInx, uint16_t wItemSerial, unsigned int dwRegistSerial)
        {
           CUnmannedTraderUserInfoSendCancelRegistSuccessResult124_user(_this, wInx, wItemSerial, dwRegistSerial, CUnmannedTraderUserInfoSendCancelRegistSuccessResult124_next);
        };
        void CUnmannedTraderUserInfoSendNotifyCloseItem126_wrapper(struct CUnmannedTraderUserInfo* _this, uint16_t wInx, uint16_t wItemSerial, unsigned int dwRegistSerial, unsigned int dwPrice, char byTax)
        {
           CUnmannedTraderUserInfoSendNotifyCloseItem126_user(_this, wInx, wItemSerial, dwRegistSerial, dwPrice, byTax, CUnmannedTraderUserInfoSendNotifyCloseItem126_next);
        };
        void CUnmannedTraderUserInfoSendRegistItemErrorResult128_wrapper(struct CUnmannedTraderUserInfo* _this, uint16_t wInx, char byRet, uint16_t wItemSerial, unsigned int dwRetParam1)
        {
           CUnmannedTraderUserInfoSendRegistItemErrorResult128_user(_this, wInx, byRet, wItemSerial, dwRetParam1, CUnmannedTraderUserInfoSendRegistItemErrorResult128_next);
        };
        void CUnmannedTraderUserInfoSendRegistItemSuccessResult130_wrapper(struct CUnmannedTraderUserInfo* _this, unsigned int dwLeftDalant, uint16_t wInx, char* pLoadData)
        {
           CUnmannedTraderUserInfoSendRegistItemSuccessResult130_user(_this, dwLeftDalant, wInx, pLoadData, CUnmannedTraderUserInfoSendRegistItemSuccessResult130_next);
        };
        void CUnmannedTraderUserInfoSendRepriceErrorResult132_wrapper(struct CUnmannedTraderUserInfo* _this, struct CPlayer* pReceiver, char byRet)
        {
           CUnmannedTraderUserInfoSendRepriceErrorResult132_user(_this, pReceiver, byRet, CUnmannedTraderUserInfoSendRepriceErrorResult132_next);
        };
        void CUnmannedTraderUserInfoSendRepriceSuccessResult134_wrapper(struct CUnmannedTraderUserInfo* _this, struct CPlayer* pReceiver, uint16_t wItemSerial, unsigned int dwNewPrice, unsigned int dwRegistSerial, unsigned int dwTax)
        {
           CUnmannedTraderUserInfoSendRepriceSuccessResult134_user(_this, pReceiver, wItemSerial, dwNewPrice, dwRegistSerial, dwTax, CUnmannedTraderUserInfoSendRepriceSuccessResult134_next);
        };
        void CUnmannedTraderUserInfoSendSearchErrorResult136_wrapper(struct CUnmannedTraderUserInfo* _this, uint16_t wInx, char byRet)
        {
           CUnmannedTraderUserInfoSendSearchErrorResult136_user(_this, wInx, byRet, CUnmannedTraderUserInfoSendSearchErrorResult136_next);
        };
        void CUnmannedTraderUserInfoSendSearchResult138_wrapper(struct CUnmannedTraderUserInfo* _this, uint16_t wInx, char* pLoadData)
        {
           CUnmannedTraderUserInfoSendSearchResult138_user(_this, wInx, pLoadData, CUnmannedTraderUserInfoSendSearchResult138_next);
        };
        void CUnmannedTraderUserInfoSendSellInfom140_wrapper(struct CUnmannedTraderUserInfo* _this, uint16_t wInx, uint16_t wItemSerial, unsigned int dwAddDalant, unsigned int dwTaxDalant, unsigned int dwTotalDalant)
        {
           CUnmannedTraderUserInfoSendSellInfom140_user(_this, wInx, wItemSerial, dwAddDalant, dwTaxDalant, dwTotalDalant, CUnmannedTraderUserInfoSendSellInfom140_next);
        };
        void CUnmannedTraderUserInfoSetAllItemState142_wrapper(struct CUnmannedTraderUserInfo* _this, char byState, char byMaxCnt)
        {
           CUnmannedTraderUserInfoSetAllItemState142_user(_this, byState, byMaxCnt, CUnmannedTraderUserInfoSetAllItemState142_next);
        };
        void CUnmannedTraderUserInfoSetCompleteInfo144_wrapper(struct CUnmannedTraderUserInfo* _this, struct CLogFile* pkLogger)
        {
           CUnmannedTraderUserInfoSetCompleteInfo144_user(_this, pkLogger, CUnmannedTraderUserInfoSetCompleteInfo144_next);
        };
        bool CUnmannedTraderUserInfoSetLoadInfo146_wrapper(struct CUnmannedTraderUserInfo* _this, char byType, unsigned int dwSerial, struct _TRADE_DB_BASE* kInfo, struct CLogFile* pkLogger)
        {
           return CUnmannedTraderUserInfoSetLoadInfo146_user(_this, byType, dwSerial, kInfo, pkLogger, CUnmannedTraderUserInfoSetLoadInfo146_next);
        };
        
        void CUnmannedTraderUserInfodtor_CUnmannedTraderUserInfo154_wrapper(struct CUnmannedTraderUserInfo* _this)
        {
           CUnmannedTraderUserInfodtor_CUnmannedTraderUserInfo154_user(_this, CUnmannedTraderUserInfodtor_CUnmannedTraderUserInfo154_next);
        };
        
        ::std::array<hook_record, 74> CUnmannedTraderUserInfo_functions = 
        {
            _hook_record {
                (LPVOID)0x140353e80L,
                (LPVOID *)&CUnmannedTraderUserInfoBuy2_user,
                (LPVOID *)&CUnmannedTraderUserInfoBuy2_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoBuy2_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(char, struct _unmannedtrader_buy_item_request_clzo*, struct CLogFile*))&CUnmannedTraderUserInfo::Buy)
            },
            _hook_record {
                (LPVOID)0x1403568c0L,
                (LPVOID *)&CUnmannedTraderUserInfoBuyComplete4_user,
                (LPVOID *)&CUnmannedTraderUserInfoBuyComplete4_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoBuyComplete4_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderUserInfo::*)(struct CPlayer*, unsigned int, char*, char*, unsigned int, unsigned int, uint64_t, unsigned int, unsigned int, uint64_t, struct CLogFile*, uint16_t*))&CUnmannedTraderUserInfo::BuyComplete)
            },
            _hook_record {
                (LPVOID)0x140367900L,
                (LPVOID *)&CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo6_user,
                (LPVOID *)&CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo6_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo6_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(struct CUnmannedTraderUserInfo*))&CUnmannedTraderUserInfo::ctor_CUnmannedTraderUserInfo)
            },
            _hook_record {
                (LPVOID)0x140353030L,
                (LPVOID *)&CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo8_user,
                (LPVOID *)&CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo8_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoctor_CUnmannedTraderUserInfo8_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)())&CUnmannedTraderUserInfo::ctor_CUnmannedTraderUserInfo)
            },
            _hook_record {
                (LPVOID)0x140353c80L,
                (LPVOID *)&CUnmannedTraderUserInfoCancelRegist10_user,
                (LPVOID *)&CUnmannedTraderUserInfoCancelRegist10_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCancelRegist10_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(char, struct _a_trade_clear_item_request_clzo*, struct CLogFile*))&CUnmannedTraderUserInfo::CancelRegist)
            },
            _hook_record {
                (LPVOID)0x140354860L,
                (LPVOID *)&CUnmannedTraderUserInfoCheatCancelRegist12_user,
                (LPVOID *)&CUnmannedTraderUserInfoCheatCancelRegist12_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCheatCancelRegist12_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfo::*)(char))&CUnmannedTraderUserInfo::CheatCancelRegist)
            },
            _hook_record {
                (LPVOID)0x14035c5f0L,
                (LPVOID *)&CUnmannedTraderUserInfoCheatCancelRegistAll14_user,
                (LPVOID *)&CUnmannedTraderUserInfoCheatCancelRegistAll14_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCheatCancelRegistAll14_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfo::*)())&CUnmannedTraderUserInfo::CheatCancelRegistAll)
            },
            _hook_record {
                (LPVOID)0x14035c100L,
                (LPVOID *)&CUnmannedTraderUserInfoCheatCancelRegistSingle16_user,
                (LPVOID *)&CUnmannedTraderUserInfoCheatCancelRegistSingle16_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCheatCancelRegistSingle16_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfo::*)(char))&CUnmannedTraderUserInfo::CheatCancelRegistSingle)
            },
            _hook_record {
                (LPVOID)0x14035b7e0L,
                (LPVOID *)&CUnmannedTraderUserInfoCheckBuy18_user,
                (LPVOID *)&CUnmannedTraderUserInfoCheckBuy18_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCheckBuy18_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderUserInfo::*)(char, struct _unmannedtrader_buy_item_request_clzo*, struct CPlayer**, struct CLogFile*))&CUnmannedTraderUserInfo::CheckBuy)
            },
            _hook_record {
                (LPVOID)0x140356460L,
                (LPVOID *)&CUnmannedTraderUserInfoCheckBuyComplete20_user,
                (LPVOID *)&CUnmannedTraderUserInfoCheckBuyComplete20_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCheckBuyComplete20_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderUserInfo::*)(struct CPlayer*, unsigned int))&CUnmannedTraderUserInfo::CheckBuyComplete)
            },
            _hook_record {
                (LPVOID)0x14035b350L,
                (LPVOID *)&CUnmannedTraderUserInfoCheckCancelRegist22_user,
                (LPVOID *)&CUnmannedTraderUserInfoCheckCancelRegist22_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCheckCancelRegist22_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderUserInfo::*)(char, struct _a_trade_clear_item_request_clzo*, struct CLogFile*))&CUnmannedTraderUserInfo::CheckCancelRegist)
            },
            _hook_record {
                (LPVOID)0x14035ba90L,
                (LPVOID *)&CUnmannedTraderUserInfoCheckIsUpdatedTaxRate24_user,
                (LPVOID *)&CUnmannedTraderUserInfoCheckIsUpdatedTaxRate24_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCheckIsUpdatedTaxRate24_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfo::*)(char, struct CLogFile*))&CUnmannedTraderUserInfo::CheckIsUpdatedTaxRate)
            },
            _hook_record {
                (LPVOID)0x14035ae60L,
                (LPVOID *)&CUnmannedTraderUserInfoCheckModifyPrice26_user,
                (LPVOID *)&CUnmannedTraderUserInfoCheckModifyPrice26_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCheckModifyPrice26_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderUserInfo::*)(char, struct _a_trade_adjust_price_request_clzo*, unsigned int*, struct CLogFile*, unsigned int*))&CUnmannedTraderUserInfo::CheckModifyPrice)
            },
            _hook_record {
                (LPVOID)0x14035bbe0L,
                (LPVOID *)&CUnmannedTraderUserInfoCheckReRegist28_user,
                (LPVOID *)&CUnmannedTraderUserInfoCheckReRegist28_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCheckReRegist28_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderUserInfo::*)(char, struct CLogFile*, uint16_t, char, char, uint16_t, unsigned int, unsigned int, char*, char*, char*, unsigned int*, unsigned int*))&CUnmannedTraderUserInfo::CheckReRegist)
            },
            _hook_record {
                (LPVOID)0x14035a1a0L,
                (LPVOID *)&CUnmannedTraderUserInfoCheckRegist30_user,
                (LPVOID *)&CUnmannedTraderUserInfoCheckRegist30_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCheckRegist30_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderUserInfo::*)(char, struct _a_trade_reg_item_request_clzo*, struct CLogFile*, char*, char*, char*, char*, unsigned int*, unsigned int*))&CUnmannedTraderUserInfo::CheckRegist)
            },
            _hook_record {
                (LPVOID)0x14035b920L,
                (LPVOID *)&CUnmannedTraderUserInfoCheckSearch32_user,
                (LPVOID *)&CUnmannedTraderUserInfoCheckSearch32_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCheckSearch32_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderUserInfo::*)(char, struct _unmannedtrader_search_list_request_clzo*, unsigned int*, unsigned int*, struct CLogFile*))&CUnmannedTraderUserInfo::CheckSearch)
            },
            _hook_record {
                (LPVOID)0x1403564e0L,
                (LPVOID *)&CUnmannedTraderUserInfoCheckSellComplete34_user,
                (LPVOID *)&CUnmannedTraderUserInfoCheckSellComplete34_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCheckSellComplete34_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderUserInfo::*)(struct CPlayer*, struct CPlayer*, unsigned int, unsigned int, struct CLogFile*))&CUnmannedTraderUserInfo::CheckSellComplete)
            },
            _hook_record {
                (LPVOID)0x14035f230L,
                (LPVOID *)&CUnmannedTraderUserInfoClear36_user,
                (LPVOID *)&CUnmannedTraderUserInfoClear36_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoClear36_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)())&CUnmannedTraderUserInfo::Clear)
            },
            _hook_record {
                (LPVOID)0x14035f520L,
                (LPVOID *)&CUnmannedTraderUserInfoClearLoadItemInfo38_user,
                (LPVOID *)&CUnmannedTraderUserInfoClearLoadItemInfo38_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoClearLoadItemInfo38_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)())&CUnmannedTraderUserInfo::ClearLoadItemInfo)
            },
            _hook_record {
                (LPVOID)0x140366e30L,
                (LPVOID *)&CUnmannedTraderUserInfoClearRequest40_user,
                (LPVOID *)&CUnmannedTraderUserInfoClearRequest40_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoClearRequest40_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)())&CUnmannedTraderUserInfo::ClearRequest)
            },
            _hook_record {
                (LPVOID)0x1403556c0L,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteCancelRegist42_user,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteCancelRegist42_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCompleteCancelRegist42_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(char, char*, struct CLogFile*))&CUnmannedTraderUserInfo::CompleteCancelRegist)
            },
            _hook_record {
                (LPVOID)0x14035b5c0L,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteCancelRegistItem44_user,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteCancelRegistItem44_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCompleteCancelRegistItem44_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfo::*)(unsigned int, uint16_t, struct CLogFile*))&CUnmannedTraderUserInfo::CompleteCancelRegistItem)
            },
            _hook_record {
                (LPVOID)0x140353510L,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteCreate46_user,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteCreate46_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCompleteCreate46_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(struct CLogFile*))&CUnmannedTraderUserInfo::CompleteCreate)
            },
            _hook_record {
                (LPVOID)0x1403558e0L,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteReRegist48_user,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteReRegist48_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCompleteReRegist48_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(char*, struct CLogFile*))&CUnmannedTraderUserInfo::CompleteReRegist)
            },
            _hook_record {
                (LPVOID)0x14035ac10L,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteReRegistItem50_user,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteReRegistItem50_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCompleteReRegistItem50_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfo::*)(unsigned int, uint16_t, unsigned int, struct CLogFile*, char*))&CUnmannedTraderUserInfo::CompleteReRegistItem)
            },
            _hook_record {
                (LPVOID)0x1403562e0L,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteReRegistRollBack52_user,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteReRegistRollBack52_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCompleteReRegistRollBack52_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(char*, struct CLogFile*))&CUnmannedTraderUserInfo::CompleteReRegistRollBack)
            },
            _hook_record {
                (LPVOID)0x140354a80L,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteRegist54_user,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteRegist54_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCompleteRegist54_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(char, char*, struct CLogFile*))&CUnmannedTraderUserInfo::CompleteRegist)
            },
            _hook_record {
                (LPVOID)0x14035aa00L,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteRegistItem56_user,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteRegistItem56_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCompleteRegistItem56_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfo::*)(unsigned int, uint16_t, unsigned int, unsigned int, char, char, uint16_t, char, uint64_t, unsigned int, bool))&CUnmannedTraderUserInfo::CompleteRegistItem)
            },
            _hook_record {
                (LPVOID)0x1403553c0L,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteReprice58_user,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteReprice58_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCompleteReprice58_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(char, char*, struct CLogFile*))&CUnmannedTraderUserInfo::CompleteReprice)
            },
            _hook_record {
                (LPVOID)0x14035b160L,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteRepriceItem60_user,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteRepriceItem60_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCompleteRepriceItem60_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfo::*)(unsigned int, uint16_t, unsigned int))&CUnmannedTraderUserInfo::CompleteRepriceItem)
            },
            _hook_record {
                (LPVOID)0x140357130L,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteTimeOutClear62_user,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteTimeOutClear62_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCompleteTimeOutClear62_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(unsigned int, struct CLogFile*))&CUnmannedTraderUserInfo::CompleteTimeOutClear)
            },
            _hook_record {
                (LPVOID)0x1403574b0L,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteUpdateCheatRegistTime64_user,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteUpdateCheatRegistTime64_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCompleteUpdateCheatRegistTime64_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(char*))&CUnmannedTraderUserInfo::CompleteUpdateCheatRegistTime)
            },
            _hook_record {
                (LPVOID)0x1403548e0L,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteUpdateState66_user,
                (LPVOID *)&CUnmannedTraderUserInfoCompleteUpdateState66_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCompleteUpdateState66_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfo::*)(unsigned int, char, bool))&CUnmannedTraderUserInfo::CompleteUpdateState)
            },
            _hook_record {
                (LPVOID)0x14035a100L,
                (LPVOID *)&CUnmannedTraderUserInfoCountRegistItem68_user,
                (LPVOID *)&CUnmannedTraderUserInfoCountRegistItem68_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoCountRegistItem68_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)())&CUnmannedTraderUserInfo::CountRegistItem)
            },
            _hook_record {
                (LPVOID)0x140359c40L,
                (LPVOID *)&CUnmannedTraderUserInfoFind70_user,
                (LPVOID *)&CUnmannedTraderUserInfoFind70_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoFind70_wrapper),
                (LPVOID)cast_pointer_function((struct std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> >*(CUnmannedTraderUserInfo::*)(struct std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> >*, unsigned int))&CUnmannedTraderUserInfo::Find)
            },
            _hook_record {
                (LPVOID)0x140359db0L,
                (LPVOID *)&CUnmannedTraderUserInfoFindEmpty72_user,
                (LPVOID *)&CUnmannedTraderUserInfoFindEmpty72_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoFindEmpty72_wrapper),
                (LPVOID)cast_pointer_function((struct std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> >*(CUnmannedTraderUserInfo::*)(struct std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> >*))&CUnmannedTraderUserInfo::FindEmpty)
            },
            _hook_record {
                (LPVOID)0x140357930L,
                (LPVOID *)&CUnmannedTraderUserInfoFindOwner74_user,
                (LPVOID *)&CUnmannedTraderUserInfoFindOwner74_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoFindOwner74_wrapper),
                (LPVOID)cast_pointer_function((struct CPlayer*(CUnmannedTraderUserInfo::*)())&CUnmannedTraderUserInfo::FindOwner)
            },
            _hook_record {
                (LPVOID)0x140359f20L,
                (LPVOID *)&CUnmannedTraderUserInfoFindRegist76_user,
                (LPVOID *)&CUnmannedTraderUserInfoFindRegist76_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoFindRegist76_wrapper),
                (LPVOID)cast_pointer_function((struct std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> >*(CUnmannedTraderUserInfo::*)(struct std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> >*, struct std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> >))&CUnmannedTraderUserInfo::FindRegist)
            },
            _hook_record {
                (LPVOID)0x140357710L,
                (LPVOID *)&CUnmannedTraderUserInfoGetCloseItemForPassTimeUpdateInfo78_user,
                (LPVOID *)&CUnmannedTraderUserInfoGetCloseItemForPassTimeUpdateInfo78_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoGetCloseItemForPassTimeUpdateInfo78_wrapper),
                (LPVOID)cast_pointer_function((CUnmannedTraderItemState::STATE(CUnmannedTraderUserInfo::*)(unsigned int, struct CPlayer**))&CUnmannedTraderUserInfo::GetCloseItemForPassTimeUpdateInfo)
            },
            _hook_record {
                (LPVOID)0x14035c900L,
                (LPVOID *)&CUnmannedTraderUserInfoGetCurrentRegItemStateStr80_user,
                (LPVOID *)&CUnmannedTraderUserInfoGetCurrentRegItemStateStr80_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoGetCurrentRegItemStateStr80_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(char*, int))&CUnmannedTraderUserInfo::GetCurrentRegItemStateStr)
            },
            _hook_record {
                (LPVOID)0x140366e80L,
                (LPVOID *)&CUnmannedTraderUserInfoGetIndex82_user,
                (LPVOID *)&CUnmannedTraderUserInfoGetIndex82_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoGetIndex82_wrapper),
                (LPVOID)cast_pointer_function((uint16_t(CUnmannedTraderUserInfo::*)())&CUnmannedTraderUserInfo::GetIndex)
            },
            _hook_record {
                (LPVOID)0x140366f00L,
                (LPVOID *)&CUnmannedTraderUserInfoGetMaxRegistCnt84_user,
                (LPVOID *)&CUnmannedTraderUserInfoGetMaxRegistCnt84_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoGetMaxRegistCnt84_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderUserInfo::*)())&CUnmannedTraderUserInfo::GetMaxRegistCnt)
            },
            _hook_record {
                (LPVOID)0x140366f80L,
                (LPVOID *)&CUnmannedTraderUserInfoGetRegItemInfo86_user,
                (LPVOID *)&CUnmannedTraderUserInfoGetRegItemInfo86_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoGetRegItemInfo86_wrapper),
                (LPVOID)cast_pointer_function((struct CUnmannedTraderRegistItemInfo*(CUnmannedTraderUserInfo::*)())&CUnmannedTraderUserInfo::GetRegItemInfo)
            },
            _hook_record {
                (LPVOID)0x140366ea0L,
                (LPVOID *)&CUnmannedTraderUserInfoGetSerial88_user,
                (LPVOID *)&CUnmannedTraderUserInfoGetSerial88_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoGetSerial88_wrapper),
                (LPVOID)cast_pointer_function((unsigned int(CUnmannedTraderUserInfo::*)())&CUnmannedTraderUserInfo::GetSerial)
            },
            _hook_record {
                (LPVOID)0x140353280L,
                (LPVOID *)&CUnmannedTraderUserInfoInit90_user,
                (LPVOID *)&CUnmannedTraderUserInfoInit90_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoInit90_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfo::*)(uint16_t))&CUnmannedTraderUserInfo::Init)
            },
            _hook_record {
                (LPVOID)0x140366f20L,
                (LPVOID *)&CUnmannedTraderUserInfoIsLogInState92_user,
                (LPVOID *)&CUnmannedTraderUserInfoIsLogInState92_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoIsLogInState92_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfo::*)())&CUnmannedTraderUserInfo::IsLogInState)
            },
            _hook_record {
                (LPVOID)0x14035f400L,
                (LPVOID *)&CUnmannedTraderUserInfoIsNull94_user,
                (LPVOID *)&CUnmannedTraderUserInfoIsNull94_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoIsNull94_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfo::*)())&CUnmannedTraderUserInfo::IsNull)
            },
            _hook_record {
                (LPVOID)0x140353440L,
                (LPVOID *)&CUnmannedTraderUserInfoLoad96_user,
                (LPVOID *)&CUnmannedTraderUserInfoLoad96_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoLoad96_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfo::*)(char, uint16_t, unsigned int, struct _TRADE_DB_BASE*, struct CLogFile*))&CUnmannedTraderUserInfo::Load)
            },
            _hook_record {
                (LPVOID)0x140353750L,
                (LPVOID *)&CUnmannedTraderUserInfoLogOut98_user,
                (LPVOID *)&CUnmannedTraderUserInfoLogOut98_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoLogOut98_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(unsigned int, struct CLogFile*))&CUnmannedTraderUserInfo::LogOut)
            },
            _hook_record {
                (LPVOID)0x140353a00L,
                (LPVOID *)&CUnmannedTraderUserInfoModifyPrice100_user,
                (LPVOID *)&CUnmannedTraderUserInfoModifyPrice100_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoModifyPrice100_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(char, struct _a_trade_adjust_price_request_clzo*, struct CLogFile*))&CUnmannedTraderUserInfo::ModifyPrice)
            },
            _hook_record {
                (LPVOID)0x140359790L,
                (LPVOID *)&CUnmannedTraderUserInfoNotifyCloseItem102_user,
                (LPVOID *)&CUnmannedTraderUserInfoNotifyCloseItem102_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoNotifyCloseItem102_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(struct _qry_case_unmandtrader_log_in_proc_update_complete*, struct CLogFile*))&CUnmannedTraderUserInfo::NotifyCloseItem)
            },
            _hook_record {
                (LPVOID)0x140358e50L,
                (LPVOID *)&CUnmannedTraderUserInfoNotifyRegistItem104_user,
                (LPVOID *)&CUnmannedTraderUserInfoNotifyRegistItem104_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoNotifyRegistItem104_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)())&CUnmannedTraderUserInfo::NotifyRegistItem)
            },
            _hook_record {
                (LPVOID)0x140358870L,
                (LPVOID *)&CUnmannedTraderUserInfoPrcoSellUpdateWaitItem106_user,
                (LPVOID *)&CUnmannedTraderUserInfoPrcoSellUpdateWaitItem106_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoPrcoSellUpdateWaitItem106_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(struct _qry_case_unmandtrader_log_in_proc_update_complete*, char, struct CLogFile*))&CUnmannedTraderUserInfo::PrcoSellUpdateWaitItem)
            },
            _hook_record {
                (LPVOID)0x140359150L,
                (LPVOID *)&CUnmannedTraderUserInfoProcSellWaitItem108_user,
                (LPVOID *)&CUnmannedTraderUserInfoProcSellWaitItem108_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoProcSellWaitItem108_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(struct _qry_case_unmandtrader_log_in_proc_update_complete*, char, struct CLogFile*))&CUnmannedTraderUserInfo::ProcSellWaitItem)
            },
            _hook_record {
                (LPVOID)0x140354340L,
                (LPVOID *)&CUnmannedTraderUserInfoReRegist110_user,
                (LPVOID *)&CUnmannedTraderUserInfoReRegist110_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoReRegist110_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(char, struct _unmannedtrader_re_regist_request_clzo*, struct CLogFile*))&CUnmannedTraderUserInfo::ReRegist)
            },
            _hook_record {
                (LPVOID)0x1403537f0L,
                (LPVOID *)&CUnmannedTraderUserInfoRegist112_user,
                (LPVOID *)&CUnmannedTraderUserInfoRegist112_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoRegist112_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(char, struct _a_trade_reg_item_request_clzo*, struct CLogFile*))&CUnmannedTraderUserInfo::Regist)
            },
            _hook_record {
                (LPVOID)0x14035a520L,
                (LPVOID *)&CUnmannedTraderUserInfoRegistItem114_user,
                (LPVOID *)&CUnmannedTraderUserInfoRegistItem114_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoRegistItem114_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderUserInfo::*)(char, struct _a_trade_reg_item_request_clzo*, char, char, char, char, unsigned int, unsigned int))&CUnmannedTraderUserInfo::RegistItem)
            },
            _hook_record {
                (LPVOID)0x1403540d0L,
                (LPVOID *)&CUnmannedTraderUserInfoSearch116_user,
                (LPVOID *)&CUnmannedTraderUserInfoSearch116_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoSearch116_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(char, struct _unmannedtrader_search_list_request_clzo*, struct CLogFile*))&CUnmannedTraderUserInfo::Search)
            },
            _hook_record {
                (LPVOID)0x140356af0L,
                (LPVOID *)&CUnmannedTraderUserInfoSellComplete118_user,
                (LPVOID *)&CUnmannedTraderUserInfoSellComplete118_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoSellComplete118_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderUserInfo::*)(struct CPlayer*, struct CPlayer*, unsigned int, unsigned int, unsigned int, unsigned int, int64_t, struct CLogFile*))&CUnmannedTraderUserInfo::SellComplete)
            },
            _hook_record {
                (LPVOID)0x140357fe0L,
                (LPVOID *)&CUnmannedTraderUserInfoSendBuyErrorResult120_user,
                (LPVOID *)&CUnmannedTraderUserInfoSendBuyErrorResult120_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoSendBuyErrorResult120_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(uint16_t, char))&CUnmannedTraderUserInfo::SendBuyErrorResult)
            },
            _hook_record {
                (LPVOID)0x140357ea0L,
                (LPVOID *)&CUnmannedTraderUserInfoSendCancelRegistErrorResult122_user,
                (LPVOID *)&CUnmannedTraderUserInfoSendCancelRegistErrorResult122_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoSendCancelRegistErrorResult122_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(uint16_t, char))&CUnmannedTraderUserInfo::SendCancelRegistErrorResult)
            },
            _hook_record {
                (LPVOID)0x140357f30L,
                (LPVOID *)&CUnmannedTraderUserInfoSendCancelRegistSuccessResult124_user,
                (LPVOID *)&CUnmannedTraderUserInfoSendCancelRegistSuccessResult124_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoSendCancelRegistSuccessResult124_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(uint16_t, uint16_t, unsigned int))&CUnmannedTraderUserInfo::SendCancelRegistSuccessResult)
            },
            _hook_record {
                (LPVOID)0x140357a50L,
                (LPVOID *)&CUnmannedTraderUserInfoSendNotifyCloseItem126_user,
                (LPVOID *)&CUnmannedTraderUserInfoSendNotifyCloseItem126_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoSendNotifyCloseItem126_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(uint16_t, uint16_t, unsigned int, unsigned int, char))&CUnmannedTraderUserInfo::SendNotifyCloseItem)
            },
            _hook_record {
                (LPVOID)0x140357b10L,
                (LPVOID *)&CUnmannedTraderUserInfoSendRegistItemErrorResult128_user,
                (LPVOID *)&CUnmannedTraderUserInfoSendRegistItemErrorResult128_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoSendRegistItemErrorResult128_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(uint16_t, char, uint16_t, unsigned int))&CUnmannedTraderUserInfo::SendRegistItemErrorResult)
            },
            _hook_record {
                (LPVOID)0x140357be0L,
                (LPVOID *)&CUnmannedTraderUserInfoSendRegistItemSuccessResult130_user,
                (LPVOID *)&CUnmannedTraderUserInfoSendRegistItemSuccessResult130_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoSendRegistItemSuccessResult130_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(unsigned int, uint16_t, char*))&CUnmannedTraderUserInfo::SendRegistItemSuccessResult)
            },
            _hook_record {
                (LPVOID)0x140357cf0L,
                (LPVOID *)&CUnmannedTraderUserInfoSendRepriceErrorResult132_user,
                (LPVOID *)&CUnmannedTraderUserInfoSendRepriceErrorResult132_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoSendRepriceErrorResult132_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(struct CPlayer*, char))&CUnmannedTraderUserInfo::SendRepriceErrorResult)
            },
            _hook_record {
                (LPVOID)0x140357db0L,
                (LPVOID *)&CUnmannedTraderUserInfoSendRepriceSuccessResult134_user,
                (LPVOID *)&CUnmannedTraderUserInfoSendRepriceSuccessResult134_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoSendRepriceSuccessResult134_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(struct CPlayer*, uint16_t, unsigned int, unsigned int, unsigned int))&CUnmannedTraderUserInfo::SendRepriceSuccessResult)
            },
            _hook_record {
                (LPVOID)0x140358150L,
                (LPVOID *)&CUnmannedTraderUserInfoSendSearchErrorResult136_user,
                (LPVOID *)&CUnmannedTraderUserInfoSendSearchErrorResult136_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoSendSearchErrorResult136_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(uint16_t, char))&CUnmannedTraderUserInfo::SendSearchErrorResult)
            },
            _hook_record {
                (LPVOID)0x140358210L,
                (LPVOID *)&CUnmannedTraderUserInfoSendSearchResult138_user,
                (LPVOID *)&CUnmannedTraderUserInfoSendSearchResult138_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoSendSearchResult138_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(uint16_t, char*))&CUnmannedTraderUserInfo::SendSearchResult)
            },
            _hook_record {
                (LPVOID)0x140358090L,
                (LPVOID *)&CUnmannedTraderUserInfoSendSellInfom140_user,
                (LPVOID *)&CUnmannedTraderUserInfoSendSellInfom140_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoSendSellInfom140_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(uint16_t, uint16_t, unsigned int, unsigned int, unsigned int))&CUnmannedTraderUserInfo::SendSellInfom)
            },
            _hook_record {
                (LPVOID)0x14035ff90L,
                (LPVOID *)&CUnmannedTraderUserInfoSetAllItemState142_user,
                (LPVOID *)&CUnmannedTraderUserInfoSetAllItemState142_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoSetAllItemState142_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(char, char))&CUnmannedTraderUserInfo::SetAllItemState)
            },
            _hook_record {
                (LPVOID)0x140358b50L,
                (LPVOID *)&CUnmannedTraderUserInfoSetCompleteInfo144_user,
                (LPVOID *)&CUnmannedTraderUserInfoSetCompleteInfo144_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoSetCompleteInfo144_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)(struct CLogFile*))&CUnmannedTraderUserInfo::SetCompleteInfo)
            },
            _hook_record {
                (LPVOID)0x140358590L,
                (LPVOID *)&CUnmannedTraderUserInfoSetLoadInfo146_user,
                (LPVOID *)&CUnmannedTraderUserInfoSetLoadInfo146_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfoSetLoadInfo146_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderUserInfo::*)(char, unsigned int, struct _TRADE_DB_BASE*, struct CLogFile*))&CUnmannedTraderUserInfo::SetLoadInfo)
            },
            _hook_record {
                (LPVOID)0x140353160L,
                (LPVOID *)&CUnmannedTraderUserInfodtor_CUnmannedTraderUserInfo154_user,
                (LPVOID *)&CUnmannedTraderUserInfodtor_CUnmannedTraderUserInfo154_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderUserInfodtor_CUnmannedTraderUserInfo154_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderUserInfo::*)())&CUnmannedTraderUserInfo::dtor_CUnmannedTraderUserInfo)
            },
        };
    }; // end namespace Detail
END_ATF_NAMESPACE
