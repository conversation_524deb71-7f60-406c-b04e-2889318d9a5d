#include <CUnmannedTraderControllerDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace Detail
    {
        Info::CUnmannedTraderControllerBuy2_ptr CUnmannedTraderControllerBuy2_next(nullptr);
        Info::CUnmannedTraderControllerBuy2_clbk CUnmannedTraderControllerBuy2_user(nullptr);
        
        
        Info::CUnmannedTraderControllerctor_CUnmannedTraderController4_ptr CUnmannedTraderControllerctor_CUnmannedTraderController4_next(nullptr);
        Info::CUnmannedTraderControllerctor_CUnmannedTraderController4_clbk CUnmannedTraderControllerctor_CUnmannedTraderController4_user(nullptr);
        
        Info::CUnmannedTraderControllerCancelRegist6_ptr CUnmannedTraderControllerCancelRegist6_next(nullptr);
        Info::CUnmannedTraderControllerCancelRegist6_clbk CUnmannedTraderControllerCancelRegist6_user(nullptr);
        
        Info::CUnmannedTraderControllerCheatCancelRegist8_ptr CUnmannedTraderControllerCheatCancelRegist8_next(nullptr);
        Info::CUnmannedTraderControllerCheatCancelRegist8_clbk CUnmannedTraderControllerCheatCancelRegist8_user(nullptr);
        
        Info::CUnmannedTraderControllerCheckDBItemState10_ptr CUnmannedTraderControllerCheckDBItemState10_next(nullptr);
        Info::CUnmannedTraderControllerCheckDBItemState10_clbk CUnmannedTraderControllerCheckDBItemState10_user(nullptr);
        
        Info::CUnmannedTraderControllerComleteLazyClean12_ptr CUnmannedTraderControllerComleteLazyClean12_next(nullptr);
        Info::CUnmannedTraderControllerComleteLazyClean12_clbk CUnmannedTraderControllerComleteLazyClean12_user(nullptr);
        
        Info::CUnmannedTraderControllerCompleteBuy14_ptr CUnmannedTraderControllerCompleteBuy14_next(nullptr);
        Info::CUnmannedTraderControllerCompleteBuy14_clbk CUnmannedTraderControllerCompleteBuy14_user(nullptr);
        
        Info::CUnmannedTraderControllerCompleteBuyComplete16_ptr CUnmannedTraderControllerCompleteBuyComplete16_next(nullptr);
        Info::CUnmannedTraderControllerCompleteBuyComplete16_clbk CUnmannedTraderControllerCompleteBuyComplete16_user(nullptr);
        
        Info::CUnmannedTraderControllerCompleteBuyRollBack18_ptr CUnmannedTraderControllerCompleteBuyRollBack18_next(nullptr);
        Info::CUnmannedTraderControllerCompleteBuyRollBack18_clbk CUnmannedTraderControllerCompleteBuyRollBack18_user(nullptr);
        
        Info::CUnmannedTraderControllerCompleteCancelRegist20_ptr CUnmannedTraderControllerCompleteCancelRegist20_next(nullptr);
        Info::CUnmannedTraderControllerCompleteCancelRegist20_clbk CUnmannedTraderControllerCompleteCancelRegist20_user(nullptr);
        
        Info::CUnmannedTraderControllerCompleteCreate22_ptr CUnmannedTraderControllerCompleteCreate22_next(nullptr);
        Info::CUnmannedTraderControllerCompleteCreate22_clbk CUnmannedTraderControllerCompleteCreate22_user(nullptr);
        
        Info::CUnmannedTraderControllerCompleteCreateNotifyTradeInfo24_ptr CUnmannedTraderControllerCompleteCreateNotifyTradeInfo24_next(nullptr);
        Info::CUnmannedTraderControllerCompleteCreateNotifyTradeInfo24_clbk CUnmannedTraderControllerCompleteCreateNotifyTradeInfo24_user(nullptr);
        
        Info::CUnmannedTraderControllerCompleteLogInCompete26_ptr CUnmannedTraderControllerCompleteLogInCompete26_next(nullptr);
        Info::CUnmannedTraderControllerCompleteLogInCompete26_clbk CUnmannedTraderControllerCompleteLogInCompete26_user(nullptr);
        
        Info::CUnmannedTraderControllerCompleteReRegist28_ptr CUnmannedTraderControllerCompleteReRegist28_next(nullptr);
        Info::CUnmannedTraderControllerCompleteReRegist28_clbk CUnmannedTraderControllerCompleteReRegist28_user(nullptr);
        
        Info::CUnmannedTraderControllerCompleteReRegistRollBack30_ptr CUnmannedTraderControllerCompleteReRegistRollBack30_next(nullptr);
        Info::CUnmannedTraderControllerCompleteReRegistRollBack30_clbk CUnmannedTraderControllerCompleteReRegistRollBack30_user(nullptr);
        
        Info::CUnmannedTraderControllerCompleteRegistItem32_ptr CUnmannedTraderControllerCompleteRegistItem32_next(nullptr);
        Info::CUnmannedTraderControllerCompleteRegistItem32_clbk CUnmannedTraderControllerCompleteRegistItem32_user(nullptr);
        
        Info::CUnmannedTraderControllerCompleteReprice34_ptr CUnmannedTraderControllerCompleteReprice34_next(nullptr);
        Info::CUnmannedTraderControllerCompleteReprice34_clbk CUnmannedTraderControllerCompleteReprice34_user(nullptr);
        
        Info::CUnmannedTraderControllerCompleteSelectBuyInfo36_ptr CUnmannedTraderControllerCompleteSelectBuyInfo36_next(nullptr);
        Info::CUnmannedTraderControllerCompleteSelectBuyInfo36_clbk CUnmannedTraderControllerCompleteSelectBuyInfo36_user(nullptr);
        
        Info::CUnmannedTraderControllerCompleteSelectReservedSchedule38_ptr CUnmannedTraderControllerCompleteSelectReservedSchedule38_next(nullptr);
        Info::CUnmannedTraderControllerCompleteSelectReservedSchedule38_clbk CUnmannedTraderControllerCompleteSelectReservedSchedule38_user(nullptr);
        
        Info::CUnmannedTraderControllerCompleteSelectSearchList40_ptr CUnmannedTraderControllerCompleteSelectSearchList40_next(nullptr);
        Info::CUnmannedTraderControllerCompleteSelectSearchList40_clbk CUnmannedTraderControllerCompleteSelectSearchList40_user(nullptr);
        
        Info::CUnmannedTraderControllerCompleteTimeOutCancelRegist42_ptr CUnmannedTraderControllerCompleteTimeOutCancelRegist42_next(nullptr);
        Info::CUnmannedTraderControllerCompleteTimeOutCancelRegist42_clbk CUnmannedTraderControllerCompleteTimeOutCancelRegist42_user(nullptr);
        
        Info::CUnmannedTraderControllerCompleteUpdateCheatRegistTime44_ptr CUnmannedTraderControllerCompleteUpdateCheatRegistTime44_next(nullptr);
        Info::CUnmannedTraderControllerCompleteUpdateCheatRegistTime44_clbk CUnmannedTraderControllerCompleteUpdateCheatRegistTime44_user(nullptr);
        
        Info::CUnmannedTraderControllerCompleteUpdateState46_ptr CUnmannedTraderControllerCompleteUpdateState46_next(nullptr);
        Info::CUnmannedTraderControllerCompleteUpdateState46_clbk CUnmannedTraderControllerCompleteUpdateState46_user(nullptr);
        
        Info::CUnmannedTraderControllerDestroy48_ptr CUnmannedTraderControllerDestroy48_next(nullptr);
        Info::CUnmannedTraderControllerDestroy48_clbk CUnmannedTraderControllerDestroy48_user(nullptr);
        
        Info::CUnmannedTraderControllerGetEmptyRecordSerial50_ptr CUnmannedTraderControllerGetEmptyRecordSerial50_next(nullptr);
        Info::CUnmannedTraderControllerGetEmptyRecordSerial50_clbk CUnmannedTraderControllerGetEmptyRecordSerial50_user(nullptr);
        
        Info::CUnmannedTraderControllerGetMaxRegistCnt52_ptr CUnmannedTraderControllerGetMaxRegistCnt52_next(nullptr);
        Info::CUnmannedTraderControllerGetMaxRegistCnt52_clbk CUnmannedTraderControllerGetMaxRegistCnt52_user(nullptr);
        
        Info::CUnmannedTraderControllerGetRegItemInfo54_ptr CUnmannedTraderControllerGetRegItemInfo54_next(nullptr);
        Info::CUnmannedTraderControllerGetRegItemInfo54_clbk CUnmannedTraderControllerGetRegItemInfo54_user(nullptr);
        
        Info::CUnmannedTraderControllerInit56_ptr CUnmannedTraderControllerInit56_next(nullptr);
        Info::CUnmannedTraderControllerInit56_clbk CUnmannedTraderControllerInit56_user(nullptr);
        
        Info::CUnmannedTraderControllerInitLogger58_ptr CUnmannedTraderControllerInitLogger58_next(nullptr);
        Info::CUnmannedTraderControllerInitLogger58_clbk CUnmannedTraderControllerInitLogger58_user(nullptr);
        
        Info::CUnmannedTraderControllerInsertDefalutRecord60_ptr CUnmannedTraderControllerInsertDefalutRecord60_next(nullptr);
        Info::CUnmannedTraderControllerInsertDefalutRecord60_clbk CUnmannedTraderControllerInsertDefalutRecord60_user(nullptr);
        
        Info::CUnmannedTraderControllerInsertStateRecord62_ptr CUnmannedTraderControllerInsertStateRecord62_next(nullptr);
        Info::CUnmannedTraderControllerInsertStateRecord62_clbk CUnmannedTraderControllerInsertStateRecord62_user(nullptr);
        
        Info::CUnmannedTraderControllerInstance64_ptr CUnmannedTraderControllerInstance64_next(nullptr);
        Info::CUnmannedTraderControllerInstance64_clbk CUnmannedTraderControllerInstance64_user(nullptr);
        
        Info::CUnmannedTraderControllerLoad66_ptr CUnmannedTraderControllerLoad66_next(nullptr);
        Info::CUnmannedTraderControllerLoad66_clbk CUnmannedTraderControllerLoad66_user(nullptr);
        
        Info::CUnmannedTraderControllerLoad68_ptr CUnmannedTraderControllerLoad68_next(nullptr);
        Info::CUnmannedTraderControllerLoad68_clbk CUnmannedTraderControllerLoad68_user(nullptr);
        
        Info::CUnmannedTraderControllerLog70_ptr CUnmannedTraderControllerLog70_next(nullptr);
        Info::CUnmannedTraderControllerLog70_clbk CUnmannedTraderControllerLog70_user(nullptr);
        
        Info::CUnmannedTraderControllerLogOut72_ptr CUnmannedTraderControllerLogOut72_next(nullptr);
        Info::CUnmannedTraderControllerLogOut72_clbk CUnmannedTraderControllerLogOut72_user(nullptr);
        
        Info::CUnmannedTraderControllerLoop74_ptr CUnmannedTraderControllerLoop74_next(nullptr);
        Info::CUnmannedTraderControllerLoop74_clbk CUnmannedTraderControllerLoop74_user(nullptr);
        
        Info::CUnmannedTraderControllerModifyPrice76_ptr CUnmannedTraderControllerModifyPrice76_next(nullptr);
        Info::CUnmannedTraderControllerModifyPrice76_clbk CUnmannedTraderControllerModifyPrice76_user(nullptr);
        
        Info::CUnmannedTraderControllerReRegist78_ptr CUnmannedTraderControllerReRegist78_next(nullptr);
        Info::CUnmannedTraderControllerReRegist78_clbk CUnmannedTraderControllerReRegist78_user(nullptr);
        
        Info::CUnmannedTraderControllerRegist80_ptr CUnmannedTraderControllerRegist80_next(nullptr);
        Info::CUnmannedTraderControllerRegist80_clbk CUnmannedTraderControllerRegist80_user(nullptr);
        
        Info::CUnmannedTraderControllerSearch82_ptr CUnmannedTraderControllerSearch82_next(nullptr);
        Info::CUnmannedTraderControllerSearch82_clbk CUnmannedTraderControllerSearch82_user(nullptr);
        
        Info::CUnmannedTraderControllerSelectBuy84_ptr CUnmannedTraderControllerSelectBuy84_next(nullptr);
        Info::CUnmannedTraderControllerSelectBuy84_clbk CUnmannedTraderControllerSelectBuy84_user(nullptr);
        
        Info::CUnmannedTraderControllerSelectSearchList86_ptr CUnmannedTraderControllerSelectSearchList86_next(nullptr);
        Info::CUnmannedTraderControllerSelectSearchList86_clbk CUnmannedTraderControllerSelectSearchList86_user(nullptr);
        
        Info::CUnmannedTraderControllerUpdateBuy88_ptr CUnmannedTraderControllerUpdateBuy88_next(nullptr);
        Info::CUnmannedTraderControllerUpdateBuy88_clbk CUnmannedTraderControllerUpdateBuy88_user(nullptr);
        
        Info::CUnmannedTraderControllerUpdateBuyComplete90_ptr CUnmannedTraderControllerUpdateBuyComplete90_next(nullptr);
        Info::CUnmannedTraderControllerUpdateBuyComplete90_clbk CUnmannedTraderControllerUpdateBuyComplete90_user(nullptr);
        
        Info::CUnmannedTraderControllerUpdateBuyRollBack92_ptr CUnmannedTraderControllerUpdateBuyRollBack92_next(nullptr);
        Info::CUnmannedTraderControllerUpdateBuyRollBack92_clbk CUnmannedTraderControllerUpdateBuyRollBack92_user(nullptr);
        
        Info::CUnmannedTraderControllerUpdateCancelRegist94_ptr CUnmannedTraderControllerUpdateCancelRegist94_next(nullptr);
        Info::CUnmannedTraderControllerUpdateCancelRegist94_clbk CUnmannedTraderControllerUpdateCancelRegist94_user(nullptr);
        
        Info::CUnmannedTraderControllerUpdateCheatRegistTime96_ptr CUnmannedTraderControllerUpdateCheatRegistTime96_next(nullptr);
        Info::CUnmannedTraderControllerUpdateCheatRegistTime96_clbk CUnmannedTraderControllerUpdateCheatRegistTime96_user(nullptr);
        
        Info::CUnmannedTraderControllerUpdateClearDanglingOwnerRecord98_ptr CUnmannedTraderControllerUpdateClearDanglingOwnerRecord98_next(nullptr);
        Info::CUnmannedTraderControllerUpdateClearDanglingOwnerRecord98_clbk CUnmannedTraderControllerUpdateClearDanglingOwnerRecord98_user(nullptr);
        
        Info::CUnmannedTraderControllerUpdateItemState100_ptr CUnmannedTraderControllerUpdateItemState100_next(nullptr);
        Info::CUnmannedTraderControllerUpdateItemState100_clbk CUnmannedTraderControllerUpdateItemState100_user(nullptr);
        
        Info::CUnmannedTraderControllerUpdateLazyClean102_ptr CUnmannedTraderControllerUpdateLazyClean102_next(nullptr);
        Info::CUnmannedTraderControllerUpdateLazyClean102_clbk CUnmannedTraderControllerUpdateLazyClean102_user(nullptr);
        
        Info::CUnmannedTraderControllerUpdateLogInComplete104_ptr CUnmannedTraderControllerUpdateLogInComplete104_next(nullptr);
        Info::CUnmannedTraderControllerUpdateLogInComplete104_clbk CUnmannedTraderControllerUpdateLogInComplete104_user(nullptr);
        
        Info::CUnmannedTraderControllerUpdateRePrice106_ptr CUnmannedTraderControllerUpdateRePrice106_next(nullptr);
        Info::CUnmannedTraderControllerUpdateRePrice106_clbk CUnmannedTraderControllerUpdateRePrice106_user(nullptr);
        
        Info::CUnmannedTraderControllerUpdateReRegist108_ptr CUnmannedTraderControllerUpdateReRegist108_next(nullptr);
        Info::CUnmannedTraderControllerUpdateReRegist108_clbk CUnmannedTraderControllerUpdateReRegist108_user(nullptr);
        
        Info::CUnmannedTraderControllerUpdateReRegistRollBack110_ptr CUnmannedTraderControllerUpdateReRegistRollBack110_next(nullptr);
        Info::CUnmannedTraderControllerUpdateReRegistRollBack110_clbk CUnmannedTraderControllerUpdateReRegistRollBack110_user(nullptr);
        
        Info::CUnmannedTraderControllerUpdateRegistItem112_ptr CUnmannedTraderControllerUpdateRegistItem112_next(nullptr);
        Info::CUnmannedTraderControllerUpdateRegistItem112_clbk CUnmannedTraderControllerUpdateRegistItem112_user(nullptr);
        
        Info::CUnmannedTraderControllerUpdateTimeOutCancelRegist114_ptr CUnmannedTraderControllerUpdateTimeOutCancelRegist114_next(nullptr);
        Info::CUnmannedTraderControllerUpdateTimeOutCancelRegist114_clbk CUnmannedTraderControllerUpdateTimeOutCancelRegist114_user(nullptr);
        
        
        Info::CUnmannedTraderControllerdtor_CUnmannedTraderController118_ptr CUnmannedTraderControllerdtor_CUnmannedTraderController118_next(nullptr);
        Info::CUnmannedTraderControllerdtor_CUnmannedTraderController118_clbk CUnmannedTraderControllerdtor_CUnmannedTraderController118_user(nullptr);
        
        void CUnmannedTraderControllerBuy2_wrapper(struct CUnmannedTraderController* _this, uint16_t wInx, struct _unmannedtrader_buy_item_request_clzo* pRequest)
        {
           CUnmannedTraderControllerBuy2_user(_this, wInx, pRequest, CUnmannedTraderControllerBuy2_next);
        };
        
        void CUnmannedTraderControllerctor_CUnmannedTraderController4_wrapper(struct CUnmannedTraderController* _this)
        {
           CUnmannedTraderControllerctor_CUnmannedTraderController4_user(_this, CUnmannedTraderControllerctor_CUnmannedTraderController4_next);
        };
        void CUnmannedTraderControllerCancelRegist6_wrapper(struct CUnmannedTraderController* _this, uint16_t wInx, struct _a_trade_clear_item_request_clzo* pRequest)
        {
           CUnmannedTraderControllerCancelRegist6_user(_this, wInx, pRequest, CUnmannedTraderControllerCancelRegist6_next);
        };
        bool CUnmannedTraderControllerCheatCancelRegist8_wrapper(struct CUnmannedTraderController* _this, uint16_t wInx, unsigned int dwOwnerSerial, char byNth)
        {
           return CUnmannedTraderControllerCheatCancelRegist8_user(_this, wInx, dwOwnerSerial, byNth, CUnmannedTraderControllerCheatCancelRegist8_next);
        };
        char CUnmannedTraderControllerCheckDBItemState10_wrapper(struct CUnmannedTraderController* _this, char byType, unsigned int dwRegistSerial, char* byState, char* byProcRet)
        {
           return CUnmannedTraderControllerCheckDBItemState10_user(_this, byType, dwRegistSerial, byState, byProcRet, CUnmannedTraderControllerCheckDBItemState10_next);
        };
        void CUnmannedTraderControllerComleteLazyClean12_wrapper(struct CUnmannedTraderController* _this, char* pData)
        {
           CUnmannedTraderControllerComleteLazyClean12_user(_this, pData, CUnmannedTraderControllerComleteLazyClean12_next);
        };
        void CUnmannedTraderControllerCompleteBuy14_wrapper(struct CUnmannedTraderController* _this, char byRet, char* pLoadData)
        {
           CUnmannedTraderControllerCompleteBuy14_user(_this, byRet, pLoadData, CUnmannedTraderControllerCompleteBuy14_next);
        };
        void CUnmannedTraderControllerCompleteBuyComplete16_wrapper(struct CUnmannedTraderController* _this, char* pData)
        {
           CUnmannedTraderControllerCompleteBuyComplete16_user(_this, pData, CUnmannedTraderControllerCompleteBuyComplete16_next);
        };
        void CUnmannedTraderControllerCompleteBuyRollBack18_wrapper(struct CUnmannedTraderController* _this, char byRet, char* pLoadData)
        {
           CUnmannedTraderControllerCompleteBuyRollBack18_user(_this, byRet, pLoadData, CUnmannedTraderControllerCompleteBuyRollBack18_next);
        };
        void CUnmannedTraderControllerCompleteCancelRegist20_wrapper(struct CUnmannedTraderController* _this, char byRet, char* pLoadData)
        {
           CUnmannedTraderControllerCompleteCancelRegist20_user(_this, byRet, pLoadData, CUnmannedTraderControllerCompleteCancelRegist20_next);
        };
        void CUnmannedTraderControllerCompleteCreate22_wrapper(struct CUnmannedTraderController* _this, uint16_t wInx)
        {
           CUnmannedTraderControllerCompleteCreate22_user(_this, wInx, CUnmannedTraderControllerCompleteCreate22_next);
        };
        void CUnmannedTraderControllerCompleteCreateNotifyTradeInfo24_wrapper(struct CUnmannedTraderController* _this, char byRace, uint16_t wInx)
        {
           CUnmannedTraderControllerCompleteCreateNotifyTradeInfo24_user(_this, byRace, wInx, CUnmannedTraderControllerCompleteCreateNotifyTradeInfo24_next);
        };
        void CUnmannedTraderControllerCompleteLogInCompete26_wrapper(struct CUnmannedTraderController* _this, char* pData)
        {
           CUnmannedTraderControllerCompleteLogInCompete26_user(_this, pData, CUnmannedTraderControllerCompleteLogInCompete26_next);
        };
        void CUnmannedTraderControllerCompleteReRegist28_wrapper(struct CUnmannedTraderController* _this, char* pLoadData)
        {
           CUnmannedTraderControllerCompleteReRegist28_user(_this, pLoadData, CUnmannedTraderControllerCompleteReRegist28_next);
        };
        void CUnmannedTraderControllerCompleteReRegistRollBack30_wrapper(struct CUnmannedTraderController* _this, char* pData)
        {
           CUnmannedTraderControllerCompleteReRegistRollBack30_user(_this, pData, CUnmannedTraderControllerCompleteReRegistRollBack30_next);
        };
        void CUnmannedTraderControllerCompleteRegistItem32_wrapper(struct CUnmannedTraderController* _this, char byRet, char* pLoadData)
        {
           CUnmannedTraderControllerCompleteRegistItem32_user(_this, byRet, pLoadData, CUnmannedTraderControllerCompleteRegistItem32_next);
        };
        void CUnmannedTraderControllerCompleteReprice34_wrapper(struct CUnmannedTraderController* _this, char byRet, char* pLoadData)
        {
           CUnmannedTraderControllerCompleteReprice34_user(_this, byRet, pLoadData, CUnmannedTraderControllerCompleteReprice34_next);
        };
        void CUnmannedTraderControllerCompleteSelectBuyInfo36_wrapper(struct CUnmannedTraderController* _this, char byRet, char* pLoadData)
        {
           CUnmannedTraderControllerCompleteSelectBuyInfo36_user(_this, byRet, pLoadData, CUnmannedTraderControllerCompleteSelectBuyInfo36_next);
        };
        void CUnmannedTraderControllerCompleteSelectReservedSchedule38_wrapper(struct CUnmannedTraderController* _this, char byRet, char* pLoadData)
        {
           CUnmannedTraderControllerCompleteSelectReservedSchedule38_user(_this, byRet, pLoadData, CUnmannedTraderControllerCompleteSelectReservedSchedule38_next);
        };
        void CUnmannedTraderControllerCompleteSelectSearchList40_wrapper(struct CUnmannedTraderController* _this, char byDBRet, char byProcRet, char* pLoadData)
        {
           CUnmannedTraderControllerCompleteSelectSearchList40_user(_this, byDBRet, byProcRet, pLoadData, CUnmannedTraderControllerCompleteSelectSearchList40_next);
        };
        void CUnmannedTraderControllerCompleteTimeOutCancelRegist42_wrapper(struct CUnmannedTraderController* _this, char byRet, char* pLoadData)
        {
           CUnmannedTraderControllerCompleteTimeOutCancelRegist42_user(_this, byRet, pLoadData, CUnmannedTraderControllerCompleteTimeOutCancelRegist42_next);
        };
        void CUnmannedTraderControllerCompleteUpdateCheatRegistTime44_wrapper(struct CUnmannedTraderController* _this, char* pLoadData)
        {
           CUnmannedTraderControllerCompleteUpdateCheatRegistTime44_user(_this, pLoadData, CUnmannedTraderControllerCompleteUpdateCheatRegistTime44_next);
        };
        void CUnmannedTraderControllerCompleteUpdateState46_wrapper(struct CUnmannedTraderController* _this, char byRet, char* pLoadData)
        {
           CUnmannedTraderControllerCompleteUpdateState46_user(_this, byRet, pLoadData, CUnmannedTraderControllerCompleteUpdateState46_next);
        };
        void CUnmannedTraderControllerDestroy48_wrapper()
        {
           CUnmannedTraderControllerDestroy48_user(CUnmannedTraderControllerDestroy48_next);
        };
        char CUnmannedTraderControllerGetEmptyRecordSerial50_wrapper(struct CUnmannedTraderController* _this, unsigned int* dwSerial, bool* pbRecordInserted)
        {
           return CUnmannedTraderControllerGetEmptyRecordSerial50_user(_this, dwSerial, pbRecordInserted, CUnmannedTraderControllerGetEmptyRecordSerial50_next);
        };
        char CUnmannedTraderControllerGetMaxRegistCnt52_wrapper(struct CUnmannedTraderController* _this, uint16_t wInx, unsigned int dwSerial)
        {
           return CUnmannedTraderControllerGetMaxRegistCnt52_user(_this, wInx, dwSerial, CUnmannedTraderControllerGetMaxRegistCnt52_next);
        };
        struct CUnmannedTraderRegistItemInfo* CUnmannedTraderControllerGetRegItemInfo54_wrapper(struct CUnmannedTraderController* _this, uint16_t wInx, unsigned int dwSerial)
        {
           return CUnmannedTraderControllerGetRegItemInfo54_user(_this, wInx, dwSerial, CUnmannedTraderControllerGetRegItemInfo54_next);
        };
        bool CUnmannedTraderControllerInit56_wrapper(struct CUnmannedTraderController* _this)
        {
           return CUnmannedTraderControllerInit56_user(_this, CUnmannedTraderControllerInit56_next);
        };
        bool CUnmannedTraderControllerInitLogger58_wrapper(struct CUnmannedTraderController* _this)
        {
           return CUnmannedTraderControllerInitLogger58_user(_this, CUnmannedTraderControllerInitLogger58_next);
        };
        bool CUnmannedTraderControllerInsertDefalutRecord60_wrapper(struct CUnmannedTraderController* _this)
        {
           return CUnmannedTraderControllerInsertDefalutRecord60_user(_this, CUnmannedTraderControllerInsertDefalutRecord60_next);
        };
        bool CUnmannedTraderControllerInsertStateRecord62_wrapper(struct CUnmannedTraderController* _this)
        {
           return CUnmannedTraderControllerInsertStateRecord62_user(_this, CUnmannedTraderControllerInsertStateRecord62_next);
        };
        struct CUnmannedTraderController* CUnmannedTraderControllerInstance64_wrapper()
        {
           return CUnmannedTraderControllerInstance64_user(CUnmannedTraderControllerInstance64_next);
        };
        bool CUnmannedTraderControllerLoad66_wrapper(struct CUnmannedTraderController* _this, uint16_t wInx, unsigned int dwSerial, struct _TRADE_DB_BASE* kInfo)
        {
           return CUnmannedTraderControllerLoad66_user(_this, wInx, dwSerial, kInfo, CUnmannedTraderControllerLoad66_next);
        };
        bool CUnmannedTraderControllerLoad68_wrapper(struct CUnmannedTraderController* _this)
        {
           return CUnmannedTraderControllerLoad68_user(_this, CUnmannedTraderControllerLoad68_next);
        };
        void CUnmannedTraderControllerLog70_wrapper(struct CUnmannedTraderController* _this, char* fmt)
        {
           CUnmannedTraderControllerLog70_user(_this, fmt, CUnmannedTraderControllerLog70_next);
        };
        void CUnmannedTraderControllerLogOut72_wrapper(struct CUnmannedTraderController* _this, uint16_t wInx, unsigned int dwSerial)
        {
           CUnmannedTraderControllerLogOut72_user(_this, wInx, dwSerial, CUnmannedTraderControllerLogOut72_next);
        };
        void CUnmannedTraderControllerLoop74_wrapper(struct CUnmannedTraderController* _this)
        {
           CUnmannedTraderControllerLoop74_user(_this, CUnmannedTraderControllerLoop74_next);
        };
        void CUnmannedTraderControllerModifyPrice76_wrapper(struct CUnmannedTraderController* _this, uint16_t wInx, struct _a_trade_adjust_price_request_clzo* pRequest)
        {
           CUnmannedTraderControllerModifyPrice76_user(_this, wInx, pRequest, CUnmannedTraderControllerModifyPrice76_next);
        };
        void CUnmannedTraderControllerReRegist78_wrapper(struct CUnmannedTraderController* _this, uint16_t wInx, struct _unmannedtrader_re_regist_request_clzo* pRequest)
        {
           CUnmannedTraderControllerReRegist78_user(_this, wInx, pRequest, CUnmannedTraderControllerReRegist78_next);
        };
        void CUnmannedTraderControllerRegist80_wrapper(struct CUnmannedTraderController* _this, uint16_t wInx, struct _a_trade_reg_item_request_clzo* pRequest)
        {
           CUnmannedTraderControllerRegist80_user(_this, wInx, pRequest, CUnmannedTraderControllerRegist80_next);
        };
        void CUnmannedTraderControllerSearch82_wrapper(struct CUnmannedTraderController* _this, uint16_t wInx, struct _unmannedtrader_search_list_request_clzo* pRequest)
        {
           CUnmannedTraderControllerSearch82_user(_this, wInx, pRequest, CUnmannedTraderControllerSearch82_next);
        };
        char CUnmannedTraderControllerSelectBuy84_wrapper(struct CUnmannedTraderController* _this, char* pData)
        {
           return CUnmannedTraderControllerSelectBuy84_user(_this, pData, CUnmannedTraderControllerSelectBuy84_next);
        };
        char CUnmannedTraderControllerSelectSearchList86_wrapper(struct CUnmannedTraderController* _this, char* pData, struct CRFWorldDatabase* pkWorldDB, char* byProcRet)
        {
           return CUnmannedTraderControllerSelectSearchList86_user(_this, pData, pkWorldDB, byProcRet, CUnmannedTraderControllerSelectSearchList86_next);
        };
        char CUnmannedTraderControllerUpdateBuy88_wrapper(struct CUnmannedTraderController* _this, char* pData)
        {
           return CUnmannedTraderControllerUpdateBuy88_user(_this, pData, CUnmannedTraderControllerUpdateBuy88_next);
        };
        char CUnmannedTraderControllerUpdateBuyComplete90_wrapper(struct CUnmannedTraderController* _this, char* pData)
        {
           return CUnmannedTraderControllerUpdateBuyComplete90_user(_this, pData, CUnmannedTraderControllerUpdateBuyComplete90_next);
        };
        char CUnmannedTraderControllerUpdateBuyRollBack92_wrapper(struct CUnmannedTraderController* _this, char* pData)
        {
           return CUnmannedTraderControllerUpdateBuyRollBack92_user(_this, pData, CUnmannedTraderControllerUpdateBuyRollBack92_next);
        };
        char CUnmannedTraderControllerUpdateCancelRegist94_wrapper(struct CUnmannedTraderController* _this, char* pData)
        {
           return CUnmannedTraderControllerUpdateCancelRegist94_user(_this, pData, CUnmannedTraderControllerUpdateCancelRegist94_next);
        };
        char CUnmannedTraderControllerUpdateCheatRegistTime96_wrapper(struct CUnmannedTraderController* _this, char* pData)
        {
           return CUnmannedTraderControllerUpdateCheatRegistTime96_user(_this, pData, CUnmannedTraderControllerUpdateCheatRegistTime96_next);
        };
        bool CUnmannedTraderControllerUpdateClearDanglingOwnerRecord98_wrapper(struct CUnmannedTraderController* _this)
        {
           return CUnmannedTraderControllerUpdateClearDanglingOwnerRecord98_user(_this, CUnmannedTraderControllerUpdateClearDanglingOwnerRecord98_next);
        };
        char CUnmannedTraderControllerUpdateItemState100_wrapper(struct CUnmannedTraderController* _this, char* pData)
        {
           return CUnmannedTraderControllerUpdateItemState100_user(_this, pData, CUnmannedTraderControllerUpdateItemState100_next);
        };
        char CUnmannedTraderControllerUpdateLazyClean102_wrapper(struct CUnmannedTraderController* _this, char* pData)
        {
           return CUnmannedTraderControllerUpdateLazyClean102_user(_this, pData, CUnmannedTraderControllerUpdateLazyClean102_next);
        };
        char CUnmannedTraderControllerUpdateLogInComplete104_wrapper(struct CUnmannedTraderController* _this, char* pData)
        {
           return CUnmannedTraderControllerUpdateLogInComplete104_user(_this, pData, CUnmannedTraderControllerUpdateLogInComplete104_next);
        };
        char CUnmannedTraderControllerUpdateRePrice106_wrapper(struct CUnmannedTraderController* _this, char* pData)
        {
           return CUnmannedTraderControllerUpdateRePrice106_user(_this, pData, CUnmannedTraderControllerUpdateRePrice106_next);
        };
        char CUnmannedTraderControllerUpdateReRegist108_wrapper(struct CUnmannedTraderController* _this, char* pData)
        {
           return CUnmannedTraderControllerUpdateReRegist108_user(_this, pData, CUnmannedTraderControllerUpdateReRegist108_next);
        };
        char CUnmannedTraderControllerUpdateReRegistRollBack110_wrapper(struct CUnmannedTraderController* _this, char* pData)
        {
           return CUnmannedTraderControllerUpdateReRegistRollBack110_user(_this, pData, CUnmannedTraderControllerUpdateReRegistRollBack110_next);
        };
        char CUnmannedTraderControllerUpdateRegistItem112_wrapper(struct CUnmannedTraderController* _this, char* pData)
        {
           return CUnmannedTraderControllerUpdateRegistItem112_user(_this, pData, CUnmannedTraderControllerUpdateRegistItem112_next);
        };
        char CUnmannedTraderControllerUpdateTimeOutCancelRegist114_wrapper(struct CUnmannedTraderController* _this, char* pData)
        {
           return CUnmannedTraderControllerUpdateTimeOutCancelRegist114_user(_this, pData, CUnmannedTraderControllerUpdateTimeOutCancelRegist114_next);
        };
        
        void CUnmannedTraderControllerdtor_CUnmannedTraderController118_wrapper(struct CUnmannedTraderController* _this)
        {
           CUnmannedTraderControllerdtor_CUnmannedTraderController118_user(_this, CUnmannedTraderControllerdtor_CUnmannedTraderController118_next);
        };
        
        ::std::array<hook_record, 58> CUnmannedTraderController_functions = 
        {
            _hook_record {
                (LPVOID)0x1401d4920L,
                (LPVOID *)&CUnmannedTraderControllerBuy2_user,
                (LPVOID *)&CUnmannedTraderControllerBuy2_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerBuy2_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(uint16_t, struct _unmannedtrader_buy_item_request_clzo*))&CUnmannedTraderController::Buy)
            },
            _hook_record {
                (LPVOID)0x14034c880L,
                (LPVOID *)&CUnmannedTraderControllerctor_CUnmannedTraderController4_user,
                (LPVOID *)&CUnmannedTraderControllerctor_CUnmannedTraderController4_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerctor_CUnmannedTraderController4_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)())&CUnmannedTraderController::ctor_CUnmannedTraderController)
            },
            _hook_record {
                (LPVOID)0x1401d4810L,
                (LPVOID *)&CUnmannedTraderControllerCancelRegist6_user,
                (LPVOID *)&CUnmannedTraderControllerCancelRegist6_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCancelRegist6_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(uint16_t, struct _a_trade_clear_item_request_clzo*))&CUnmannedTraderController::CancelRegist)
            },
            _hook_record {
                (LPVOID)0x14029d760L,
                (LPVOID *)&CUnmannedTraderControllerCheatCancelRegist8_user,
                (LPVOID *)&CUnmannedTraderControllerCheatCancelRegist8_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCheatCancelRegist8_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderController::*)(uint16_t, unsigned int, char))&CUnmannedTraderController::CheatCancelRegist)
            },
            _hook_record {
                (LPVOID)0x1403506c0L,
                (LPVOID *)&CUnmannedTraderControllerCheckDBItemState10_user,
                (LPVOID *)&CUnmannedTraderControllerCheckDBItemState10_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCheckDBItemState10_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderController::*)(char, unsigned int, char*, char*))&CUnmannedTraderController::CheckDBItemState)
            },
            _hook_record {
                (LPVOID)0x1402074d0L,
                (LPVOID *)&CUnmannedTraderControllerComleteLazyClean12_user,
                (LPVOID *)&CUnmannedTraderControllerComleteLazyClean12_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerComleteLazyClean12_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::ComleteLazyClean)
            },
            _hook_record {
                (LPVOID)0x140207450L,
                (LPVOID *)&CUnmannedTraderControllerCompleteBuy14_user,
                (LPVOID *)&CUnmannedTraderControllerCompleteBuy14_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCompleteBuy14_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(char, char*))&CUnmannedTraderController::CompleteBuy)
            },
            _hook_record {
                (LPVOID)0x14034ee20L,
                (LPVOID *)&CUnmannedTraderControllerCompleteBuyComplete16_user,
                (LPVOID *)&CUnmannedTraderControllerCompleteBuyComplete16_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCompleteBuyComplete16_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::CompleteBuyComplete)
            },
            _hook_record {
                (LPVOID)0x14034ecf0L,
                (LPVOID *)&CUnmannedTraderControllerCompleteBuyRollBack18_user,
                (LPVOID *)&CUnmannedTraderControllerCompleteBuyRollBack18_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCompleteBuyRollBack18_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(char, char*))&CUnmannedTraderController::CompleteBuyRollBack)
            },
            _hook_record {
                (LPVOID)0x1402073f0L,
                (LPVOID *)&CUnmannedTraderControllerCompleteCancelRegist20_user,
                (LPVOID *)&CUnmannedTraderControllerCompleteCancelRegist20_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCompleteCancelRegist20_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(char, char*))&CUnmannedTraderController::CompleteCancelRegist)
            },
            _hook_record {
                (LPVOID)0x140079d90L,
                (LPVOID *)&CUnmannedTraderControllerCompleteCreate22_user,
                (LPVOID *)&CUnmannedTraderControllerCompleteCreate22_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCompleteCreate22_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(uint16_t))&CUnmannedTraderController::CompleteCreate)
            },
            _hook_record {
                (LPVOID)0x140079e00L,
                (LPVOID *)&CUnmannedTraderControllerCompleteCreateNotifyTradeInfo24_user,
                (LPVOID *)&CUnmannedTraderControllerCompleteCreateNotifyTradeInfo24_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCompleteCreateNotifyTradeInfo24_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(char, uint16_t))&CUnmannedTraderController::CompleteCreateNotifyTradeInfo)
            },
            _hook_record {
                (LPVOID)0x14034ef80L,
                (LPVOID *)&CUnmannedTraderControllerCompleteLogInCompete26_user,
                (LPVOID *)&CUnmannedTraderControllerCompleteLogInCompete26_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCompleteLogInCompete26_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::CompleteLogInCompete)
            },
            _hook_record {
                (LPVOID)0x140207530L,
                (LPVOID *)&CUnmannedTraderControllerCompleteReRegist28_user,
                (LPVOID *)&CUnmannedTraderControllerCompleteReRegist28_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCompleteReRegist28_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::CompleteReRegist)
            },
            _hook_record {
                (LPVOID)0x14034fbe0L,
                (LPVOID *)&CUnmannedTraderControllerCompleteReRegistRollBack30_user,
                (LPVOID *)&CUnmannedTraderControllerCompleteReRegistRollBack30_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCompleteReRegistRollBack30_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::CompleteReRegistRollBack)
            },
            _hook_record {
                (LPVOID)0x140207330L,
                (LPVOID *)&CUnmannedTraderControllerCompleteRegistItem32_user,
                (LPVOID *)&CUnmannedTraderControllerCompleteRegistItem32_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCompleteRegistItem32_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(char, char*))&CUnmannedTraderController::CompleteRegistItem)
            },
            _hook_record {
                (LPVOID)0x140207390L,
                (LPVOID *)&CUnmannedTraderControllerCompleteReprice34_user,
                (LPVOID *)&CUnmannedTraderControllerCompleteReprice34_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCompleteReprice34_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(char, char*))&CUnmannedTraderController::CompleteReprice)
            },
            _hook_record {
                (LPVOID)0x14034f3f0L,
                (LPVOID *)&CUnmannedTraderControllerCompleteSelectBuyInfo36_user,
                (LPVOID *)&CUnmannedTraderControllerCompleteSelectBuyInfo36_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCompleteSelectBuyInfo36_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(char, char*))&CUnmannedTraderController::CompleteSelectBuyInfo)
            },
            _hook_record {
                (LPVOID)0x14034d4b0L,
                (LPVOID *)&CUnmannedTraderControllerCompleteSelectReservedSchedule38_user,
                (LPVOID *)&CUnmannedTraderControllerCompleteSelectReservedSchedule38_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCompleteSelectReservedSchedule38_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(char, char*))&CUnmannedTraderController::CompleteSelectReservedSchedule)
            },
            _hook_record {
                (LPVOID)0x1402c42f0L,
                (LPVOID *)&CUnmannedTraderControllerCompleteSelectSearchList40_user,
                (LPVOID *)&CUnmannedTraderControllerCompleteSelectSearchList40_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCompleteSelectSearchList40_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(char, char, char*))&CUnmannedTraderController::CompleteSelectSearchList)
            },
            _hook_record {
                (LPVOID)0x14034f230L,
                (LPVOID *)&CUnmannedTraderControllerCompleteTimeOutCancelRegist42_user,
                (LPVOID *)&CUnmannedTraderControllerCompleteTimeOutCancelRegist42_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCompleteTimeOutCancelRegist42_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(char, char*))&CUnmannedTraderController::CompleteTimeOutCancelRegist)
            },
            _hook_record {
                (LPVOID)0x14034ff60L,
                (LPVOID *)&CUnmannedTraderControllerCompleteUpdateCheatRegistTime44_user,
                (LPVOID *)&CUnmannedTraderControllerCompleteUpdateCheatRegistTime44_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCompleteUpdateCheatRegistTime44_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::CompleteUpdateCheatRegistTime)
            },
            _hook_record {
                (LPVOID)0x14034f0e0L,
                (LPVOID *)&CUnmannedTraderControllerCompleteUpdateState46_user,
                (LPVOID *)&CUnmannedTraderControllerCompleteUpdateState46_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerCompleteUpdateState46_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(char, char*))&CUnmannedTraderController::CompleteUpdateState)
            },
            _hook_record {
                (LPVOID)0x14034cb60L,
                (LPVOID *)&CUnmannedTraderControllerDestroy48_user,
                (LPVOID *)&CUnmannedTraderControllerDestroy48_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerDestroy48_wrapper),
                (LPVOID)cast_pointer_function((void(*)())&CUnmannedTraderController::Destroy)
            },
            _hook_record {
                (LPVOID)0x140350270L,
                (LPVOID *)&CUnmannedTraderControllerGetEmptyRecordSerial50_user,
                (LPVOID *)&CUnmannedTraderControllerGetEmptyRecordSerial50_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerGetEmptyRecordSerial50_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderController::*)(unsigned int*, bool*))&CUnmannedTraderController::GetEmptyRecordSerial)
            },
            _hook_record {
                (LPVOID)0x14007a1b0L,
                (LPVOID *)&CUnmannedTraderControllerGetMaxRegistCnt52_user,
                (LPVOID *)&CUnmannedTraderControllerGetMaxRegistCnt52_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerGetMaxRegistCnt52_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderController::*)(uint16_t, unsigned int))&CUnmannedTraderController::GetMaxRegistCnt)
            },
            _hook_record {
                (LPVOID)0x14007a210L,
                (LPVOID *)&CUnmannedTraderControllerGetRegItemInfo54_user,
                (LPVOID *)&CUnmannedTraderControllerGetRegItemInfo54_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerGetRegItemInfo54_wrapper),
                (LPVOID)cast_pointer_function((struct CUnmannedTraderRegistItemInfo*(CUnmannedTraderController::*)(uint16_t, unsigned int))&CUnmannedTraderController::GetRegItemInfo)
            },
            _hook_record {
                (LPVOID)0x14034cbe0L,
                (LPVOID *)&CUnmannedTraderControllerInit56_user,
                (LPVOID *)&CUnmannedTraderControllerInit56_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerInit56_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderController::*)())&CUnmannedTraderController::Init)
            },
            _hook_record {
                (LPVOID)0x14034fc50L,
                (LPVOID *)&CUnmannedTraderControllerInitLogger58_user,
                (LPVOID *)&CUnmannedTraderControllerInitLogger58_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerInitLogger58_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderController::*)())&CUnmannedTraderController::InitLogger)
            },
            _hook_record {
                (LPVOID)0x140350610L,
                (LPVOID *)&CUnmannedTraderControllerInsertDefalutRecord60_user,
                (LPVOID *)&CUnmannedTraderControllerInsertDefalutRecord60_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerInsertDefalutRecord60_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderController::*)())&CUnmannedTraderController::InsertDefalutRecord)
            },
            _hook_record {
                (LPVOID)0x140350320L,
                (LPVOID *)&CUnmannedTraderControllerInsertStateRecord62_user,
                (LPVOID *)&CUnmannedTraderControllerInsertStateRecord62_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerInsertStateRecord62_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderController::*)())&CUnmannedTraderController::InsertStateRecord)
            },
            _hook_record {
                (LPVOID)0x14034caa0L,
                (LPVOID *)&CUnmannedTraderControllerInstance64_user,
                (LPVOID *)&CUnmannedTraderControllerInstance64_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerInstance64_wrapper),
                (LPVOID)cast_pointer_function((struct CUnmannedTraderController*(*)())&CUnmannedTraderController::Instance)
            },
            _hook_record {
                (LPVOID)0x140078c10L,
                (LPVOID *)&CUnmannedTraderControllerLoad66_user,
                (LPVOID *)&CUnmannedTraderControllerLoad66_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerLoad66_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderController::*)(uint16_t, unsigned int, struct _TRADE_DB_BASE*))&CUnmannedTraderController::Load)
            },
            _hook_record {
                (LPVOID)0x14034ccb0L,
                (LPVOID *)&CUnmannedTraderControllerLoad68_user,
                (LPVOID *)&CUnmannedTraderControllerLoad68_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerLoad68_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderController::*)())&CUnmannedTraderController::Load)
            },
            _hook_record {
                (LPVOID)0x1403501e0L,
                (LPVOID *)&CUnmannedTraderControllerLog70_user,
                (LPVOID *)&CUnmannedTraderControllerLog70_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerLog70_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::Log)
            },
            _hook_record {
                (LPVOID)0x14007a270L,
                (LPVOID *)&CUnmannedTraderControllerLogOut72_user,
                (LPVOID *)&CUnmannedTraderControllerLogOut72_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerLogOut72_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(uint16_t, unsigned int))&CUnmannedTraderController::LogOut)
            },
            _hook_record {
                (LPVOID)0x14034cd60L,
                (LPVOID *)&CUnmannedTraderControllerLoop74_user,
                (LPVOID *)&CUnmannedTraderControllerLoop74_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerLoop74_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)())&CUnmannedTraderController::Loop)
            },
            _hook_record {
                (LPVOID)0x1401d4700L,
                (LPVOID *)&CUnmannedTraderControllerModifyPrice76_user,
                (LPVOID *)&CUnmannedTraderControllerModifyPrice76_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerModifyPrice76_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(uint16_t, struct _a_trade_adjust_price_request_clzo*))&CUnmannedTraderController::ModifyPrice)
            },
            _hook_record {
                (LPVOID)0x1401d4b40L,
                (LPVOID *)&CUnmannedTraderControllerReRegist78_user,
                (LPVOID *)&CUnmannedTraderControllerReRegist78_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerReRegist78_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(uint16_t, struct _unmannedtrader_re_regist_request_clzo*))&CUnmannedTraderController::ReRegist)
            },
            _hook_record {
                (LPVOID)0x1401d4550L,
                (LPVOID *)&CUnmannedTraderControllerRegist80_user,
                (LPVOID *)&CUnmannedTraderControllerRegist80_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerRegist80_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(uint16_t, struct _a_trade_reg_item_request_clzo*))&CUnmannedTraderController::Regist)
            },
            _hook_record {
                (LPVOID)0x1401d4a30L,
                (LPVOID *)&CUnmannedTraderControllerSearch82_user,
                (LPVOID *)&CUnmannedTraderControllerSearch82_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerSearch82_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)(uint16_t, struct _unmannedtrader_search_list_request_clzo*))&CUnmannedTraderController::Search)
            },
            _hook_record {
                (LPVOID)0x14034d520L,
                (LPVOID *)&CUnmannedTraderControllerSelectBuy84_user,
                (LPVOID *)&CUnmannedTraderControllerSelectBuy84_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerSelectBuy84_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::SelectBuy)
            },
            _hook_record {
                (LPVOID)0x14034dea0L,
                (LPVOID *)&CUnmannedTraderControllerSelectSearchList86_user,
                (LPVOID *)&CUnmannedTraderControllerSelectSearchList86_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerSelectSearchList86_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderController::*)(char*, struct CRFWorldDatabase*, char*))&CUnmannedTraderController::SelectSearchList)
            },
            _hook_record {
                (LPVOID)0x14034d970L,
                (LPVOID *)&CUnmannedTraderControllerUpdateBuy88_user,
                (LPVOID *)&CUnmannedTraderControllerUpdateBuy88_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerUpdateBuy88_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::UpdateBuy)
            },
            _hook_record {
                (LPVOID)0x14034e1c0L,
                (LPVOID *)&CUnmannedTraderControllerUpdateBuyComplete90_user,
                (LPVOID *)&CUnmannedTraderControllerUpdateBuyComplete90_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerUpdateBuyComplete90_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::UpdateBuyComplete)
            },
            _hook_record {
                (LPVOID)0x14034dd60L,
                (LPVOID *)&CUnmannedTraderControllerUpdateBuyRollBack92_user,
                (LPVOID *)&CUnmannedTraderControllerUpdateBuyRollBack92_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerUpdateBuyRollBack92_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::UpdateBuyRollBack)
            },
            _hook_record {
                (LPVOID)0x14034d320L,
                (LPVOID *)&CUnmannedTraderControllerUpdateCancelRegist94_user,
                (LPVOID *)&CUnmannedTraderControllerUpdateCancelRegist94_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerUpdateCancelRegist94_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::UpdateCancelRegist)
            },
            _hook_record {
                (LPVOID)0x14034ea30L,
                (LPVOID *)&CUnmannedTraderControllerUpdateCheatRegistTime96_user,
                (LPVOID *)&CUnmannedTraderControllerUpdateCheatRegistTime96_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerUpdateCheatRegistTime96_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::UpdateCheatRegistTime)
            },
            _hook_record {
                (LPVOID)0x1403505b0L,
                (LPVOID *)&CUnmannedTraderControllerUpdateClearDanglingOwnerRecord98_user,
                (LPVOID *)&CUnmannedTraderControllerUpdateClearDanglingOwnerRecord98_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerUpdateClearDanglingOwnerRecord98_wrapper),
                (LPVOID)cast_pointer_function((bool(CUnmannedTraderController::*)())&CUnmannedTraderController::UpdateClearDanglingOwnerRecord)
            },
            _hook_record {
                (LPVOID)0x14034e100L,
                (LPVOID *)&CUnmannedTraderControllerUpdateItemState100_user,
                (LPVOID *)&CUnmannedTraderControllerUpdateItemState100_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerUpdateItemState100_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::UpdateItemState)
            },
            _hook_record {
                (LPVOID)0x140206a90L,
                (LPVOID *)&CUnmannedTraderControllerUpdateLazyClean102_user,
                (LPVOID *)&CUnmannedTraderControllerUpdateLazyClean102_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerUpdateLazyClean102_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::UpdateLazyClean)
            },
            _hook_record {
                (LPVOID)0x14034e440L,
                (LPVOID *)&CUnmannedTraderControllerUpdateLogInComplete104_user,
                (LPVOID *)&CUnmannedTraderControllerUpdateLogInComplete104_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerUpdateLogInComplete104_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::UpdateLogInComplete)
            },
            _hook_record {
                (LPVOID)0x14034d230L,
                (LPVOID *)&CUnmannedTraderControllerUpdateRePrice106_user,
                (LPVOID *)&CUnmannedTraderControllerUpdateRePrice106_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerUpdateRePrice106_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::UpdateRePrice)
            },
            _hook_record {
                (LPVOID)0x14034e680L,
                (LPVOID *)&CUnmannedTraderControllerUpdateReRegist108_user,
                (LPVOID *)&CUnmannedTraderControllerUpdateReRegist108_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerUpdateReRegist108_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::UpdateReRegist)
            },
            _hook_record {
                (LPVOID)0x14034eb40L,
                (LPVOID *)&CUnmannedTraderControllerUpdateReRegistRollBack110_user,
                (LPVOID *)&CUnmannedTraderControllerUpdateReRegistRollBack110_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerUpdateReRegistRollBack110_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::UpdateReRegistRollBack)
            },
            _hook_record {
                (LPVOID)0x14034cdd0L,
                (LPVOID *)&CUnmannedTraderControllerUpdateRegistItem112_user,
                (LPVOID *)&CUnmannedTraderControllerUpdateRegistItem112_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerUpdateRegistItem112_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::UpdateRegistItem)
            },
            _hook_record {
                (LPVOID)0x14034cfa0L,
                (LPVOID *)&CUnmannedTraderControllerUpdateTimeOutCancelRegist114_user,
                (LPVOID *)&CUnmannedTraderControllerUpdateTimeOutCancelRegist114_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerUpdateTimeOutCancelRegist114_wrapper),
                (LPVOID)cast_pointer_function((char(CUnmannedTraderController::*)(char*))&CUnmannedTraderController::UpdateTimeOutCancelRegist)
            },
            _hook_record {
                (LPVOID)0x14034c930L,
                (LPVOID *)&CUnmannedTraderControllerdtor_CUnmannedTraderController118_user,
                (LPVOID *)&CUnmannedTraderControllerdtor_CUnmannedTraderController118_next,
                (LPVOID)cast_pointer_function(CUnmannedTraderControllerdtor_CUnmannedTraderController118_wrapper),
                (LPVOID)cast_pointer_function((void(CUnmannedTraderController::*)())&CUnmannedTraderController::dtor_CUnmannedTraderController)
            },
        };
    }; // end namespace Detail
END_ATF_NAMESPACE
