#include <_qry_case_update_data_for_trade.hpp>


START_ATF_NAMESPACE
    _qry_case_update_data_for_trade::_qry_case_update_data_for_trade()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_update_data_for_trade*);
        (org_ptr(0x1400f7be0L))(this);
    };
    void _qry_case_update_data_for_trade::ctor__qry_case_update_data_for_trade()
    {
        using org_ptr = void (WINAPIV*)(struct _qry_case_update_data_for_trade*);
        (org_ptr(0x1400f7be0L))(this);
    };
    int _qry_case_update_data_for_trade::size()
    {
        using org_ptr = int (WINAPIV*)(struct _qry_case_update_data_for_trade*);
        return (org_ptr(0x1400f7bd0L))(this);
    };
    
END_ATF_NAMESPACE
