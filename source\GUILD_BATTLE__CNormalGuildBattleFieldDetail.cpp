#include <GUILD_BATTLE__CNormalGuildBattleFieldDetail.hpp>
#include <common/ATFCore.hpp>


START_ATF_NAMESPACE
    namespace GUILD_BATTLE
    {
        namespace Detail
        {
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldctor_CNormalGuildBattleField2_ptr GUILD_BATTLE__CNormalGuildBattleFieldctor_CNormalGuildBattleField2_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldctor_CNormalGuildBattleField2_clbk GUILD_BATTLE__CNormalGuildBattleFieldctor_CNormalGuildBattleField2_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCheatDestroyStone4_ptr GUILD_BATTLE__CNormalGuildBattleFieldCheatDestroyStone4_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCheatDestroyStone4_clbk GUILD_BATTLE__CNormalGuildBattleFieldCheatDestroyStone4_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCheatDropStone6_ptr GUILD_BATTLE__CNormalGuildBattleFieldCheatDropStone6_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCheatDropStone6_clbk GUILD_BATTLE__CNormalGuildBattleFieldCheatDropStone6_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCheatForceTakeStone8_ptr GUILD_BATTLE__CNormalGuildBattleFieldCheatForceTakeStone8_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCheatForceTakeStone8_clbk GUILD_BATTLE__CNormalGuildBattleFieldCheatForceTakeStone8_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCheatGetStone10_ptr GUILD_BATTLE__CNormalGuildBattleFieldCheatGetStone10_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCheatGetStone10_clbk GUILD_BATTLE__CNormalGuildBattleFieldCheatGetStone10_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone12_ptr GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone12_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone12_clbk GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone12_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone14_ptr GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone14_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone14_clbk GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone14_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCheatTakeStone16_ptr GUILD_BATTLE__CNormalGuildBattleFieldCheatTakeStone16_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCheatTakeStone16_clbk GUILD_BATTLE__CNormalGuildBattleFieldCheatTakeStone16_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCheckBallTakeLimitTime18_ptr GUILD_BATTLE__CNormalGuildBattleFieldCheckBallTakeLimitTime18_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCheckBallTakeLimitTime18_clbk GUILD_BATTLE__CNormalGuildBattleFieldCheckBallTakeLimitTime18_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCheckIsInTown20_ptr GUILD_BATTLE__CNormalGuildBattleFieldCheckIsInTown20_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCheckIsInTown20_clbk GUILD_BATTLE__CNormalGuildBattleFieldCheckIsInTown20_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldClearBall22_ptr GUILD_BATTLE__CNormalGuildBattleFieldClearBall22_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldClearBall22_clbk GUILD_BATTLE__CNormalGuildBattleFieldClearBall22_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldClearRegen24_ptr GUILD_BATTLE__CNormalGuildBattleFieldClearRegen24_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldClearRegen24_clbk GUILD_BATTLE__CNormalGuildBattleFieldClearRegen24_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCreateFieldObject26_ptr GUILD_BATTLE__CNormalGuildBattleFieldCreateFieldObject26_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldCreateFieldObject26_clbk GUILD_BATTLE__CNormalGuildBattleFieldCreateFieldObject26_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldDestroy28_ptr GUILD_BATTLE__CNormalGuildBattleFieldDestroy28_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldDestroy28_clbk GUILD_BATTLE__CNormalGuildBattleFieldDestroy28_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldDestroyFieldObject30_ptr GUILD_BATTLE__CNormalGuildBattleFieldDestroyFieldObject30_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldDestroyFieldObject30_clbk GUILD_BATTLE__CNormalGuildBattleFieldDestroyFieldObject30_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldDropBall32_ptr GUILD_BATTLE__CNormalGuildBattleFieldDropBall32_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldDropBall32_clbk GUILD_BATTLE__CNormalGuildBattleFieldDropBall32_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetBall34_ptr GUILD_BATTLE__CNormalGuildBattleFieldGetBall34_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetBall34_clbk GUILD_BATTLE__CNormalGuildBattleFieldGetBall34_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetBallOwner36_ptr GUILD_BATTLE__CNormalGuildBattleFieldGetBallOwner36_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetBallOwner36_clbk GUILD_BATTLE__CNormalGuildBattleFieldGetBallOwner36_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetCircleZone38_ptr GUILD_BATTLE__CNormalGuildBattleFieldGetCircleZone38_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetCircleZone38_clbk GUILD_BATTLE__CNormalGuildBattleFieldGetCircleZone38_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetMap40_ptr GUILD_BATTLE__CNormalGuildBattleFieldGetMap40_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetMap40_clbk GUILD_BATTLE__CNormalGuildBattleFieldGetMap40_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetMapCode42_ptr GUILD_BATTLE__CNormalGuildBattleFieldGetMapCode42_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetMapCode42_clbk GUILD_BATTLE__CNormalGuildBattleFieldGetMapCode42_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetMapID44_ptr GUILD_BATTLE__CNormalGuildBattleFieldGetMapID44_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetMapID44_clbk GUILD_BATTLE__CNormalGuildBattleFieldGetMapID44_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetMapStrCode46_ptr GUILD_BATTLE__CNormalGuildBattleFieldGetMapStrCode46_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetMapStrCode46_clbk GUILD_BATTLE__CNormalGuildBattleFieldGetMapStrCode46_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetPortalIndexInfo48_ptr GUILD_BATTLE__CNormalGuildBattleFieldGetPortalIndexInfo48_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetPortalIndexInfo48_clbk GUILD_BATTLE__CNormalGuildBattleFieldGetPortalIndexInfo48_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetRegener50_ptr GUILD_BATTLE__CNormalGuildBattleFieldGetRegener50_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetRegener50_clbk GUILD_BATTLE__CNormalGuildBattleFieldGetRegener50_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetStone52_ptr GUILD_BATTLE__CNormalGuildBattleFieldGetStone52_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldGetStone52_clbk GUILD_BATTLE__CNormalGuildBattleFieldGetStone52_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldInit54_ptr GUILD_BATTLE__CNormalGuildBattleFieldInit54_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldInit54_clbk GUILD_BATTLE__CNormalGuildBattleFieldInit54_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldIsGoal56_ptr GUILD_BATTLE__CNormalGuildBattleFieldIsGoal56_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldIsGoal56_clbk GUILD_BATTLE__CNormalGuildBattleFieldIsGoal56_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldLoadDummys58_ptr GUILD_BATTLE__CNormalGuildBattleFieldLoadDummys58_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldLoadDummys58_clbk GUILD_BATTLE__CNormalGuildBattleFieldLoadDummys58_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldMoveStartPos60_ptr GUILD_BATTLE__CNormalGuildBattleFieldMoveStartPos60_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldMoveStartPos60_clbk GUILD_BATTLE__CNormalGuildBattleFieldMoveStartPos60_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldRegenBall62_ptr GUILD_BATTLE__CNormalGuildBattleFieldRegenBall62_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldRegenBall62_clbk GUILD_BATTLE__CNormalGuildBattleFieldRegenBall62_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldSetPortalInx64_ptr GUILD_BATTLE__CNormalGuildBattleFieldSetPortalInx64_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldSetPortalInx64_clbk GUILD_BATTLE__CNormalGuildBattleFieldSetPortalInx64_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldStart66_ptr GUILD_BATTLE__CNormalGuildBattleFieldStart66_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldStart66_clbk GUILD_BATTLE__CNormalGuildBattleFieldStart66_user(nullptr);
            
            Info::GUILD_BATTLE__CNormalGuildBattleFieldTakeBall68_ptr GUILD_BATTLE__CNormalGuildBattleFieldTakeBall68_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFieldTakeBall68_clbk GUILD_BATTLE__CNormalGuildBattleFieldTakeBall68_user(nullptr);
            
            
            Info::GUILD_BATTLE__CNormalGuildBattleFielddtor_CNormalGuildBattleField72_ptr GUILD_BATTLE__CNormalGuildBattleFielddtor_CNormalGuildBattleField72_next(nullptr);
            Info::GUILD_BATTLE__CNormalGuildBattleFielddtor_CNormalGuildBattleField72_clbk GUILD_BATTLE__CNormalGuildBattleFielddtor_CNormalGuildBattleField72_user(nullptr);
            
            
            void GUILD_BATTLE__CNormalGuildBattleFieldctor_CNormalGuildBattleField2_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleFieldctor_CNormalGuildBattleField2_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldctor_CNormalGuildBattleField2_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleFieldCheatDestroyStone4_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleFieldCheatDestroyStone4_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldCheatDestroyStone4_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleFieldCheatDropStone6_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this, struct CPlayer* pkPlayer)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldCheatDropStone6_user(_this, pkPlayer, GUILD_BATTLE__CNormalGuildBattleFieldCheatDropStone6_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleFieldCheatForceTakeStone8_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this, struct CPlayer* pkPlayer)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldCheatForceTakeStone8_user(_this, pkPlayer, GUILD_BATTLE__CNormalGuildBattleFieldCheatForceTakeStone8_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleFieldCheatGetStone10_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this, struct CPlayer* pkPlayer)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldCheatGetStone10_user(_this, pkPlayer, GUILD_BATTLE__CNormalGuildBattleFieldCheatGetStone10_next);
            };
            int GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone12_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this, struct CPlayer* pkPlayer)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone12_user(_this, pkPlayer, GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone12_next);
            };
            int GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone14_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this, unsigned int uiPos)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone14_user(_this, uiPos, GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone14_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleFieldCheatTakeStone16_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this, int iPortalInx, struct CPlayer* pkPlayer)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldCheatTakeStone16_user(_this, iPortalInx, pkPlayer, GUILD_BATTLE__CNormalGuildBattleFieldCheatTakeStone16_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleFieldCheckBallTakeLimitTime18_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldCheckBallTakeLimitTime18_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldCheckBallTakeLimitTime18_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleFieldCheckIsInTown20_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldCheckIsInTown20_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldCheckIsInTown20_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleFieldClearBall22_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldClearBall22_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldClearBall22_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleFieldClearRegen24_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldClearRegen24_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldClearRegen24_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleFieldCreateFieldObject26_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldCreateFieldObject26_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldCreateFieldObject26_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleFieldDestroy28_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleFieldDestroy28_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldDestroy28_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleFieldDestroyFieldObject30_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleFieldDestroyFieldObject30_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldDestroyFieldObject30_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleFieldDropBall32_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this, struct CPlayer* pkPlayer)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldDropBall32_user(_this, pkPlayer, GUILD_BATTLE__CNormalGuildBattleFieldDropBall32_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleFieldGetBall34_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this, uint16_t wIndex, unsigned int dwObjSerial, struct CPlayer* pkPlayer)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldGetBall34_user(_this, wIndex, dwObjSerial, pkPlayer, GUILD_BATTLE__CNormalGuildBattleFieldGetBall34_next);
            };
            struct CPlayer* GUILD_BATTLE__CNormalGuildBattleFieldGetBallOwner36_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldGetBallOwner36_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldGetBallOwner36_next);
            };
            struct CCircleZone* GUILD_BATTLE__CNormalGuildBattleFieldGetCircleZone38_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this, int iInx)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldGetCircleZone38_user(_this, iInx, GUILD_BATTLE__CNormalGuildBattleFieldGetCircleZone38_next);
            };
            struct CMapData* GUILD_BATTLE__CNormalGuildBattleFieldGetMap40_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldGetMap40_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldGetMap40_next);
            };
            int GUILD_BATTLE__CNormalGuildBattleFieldGetMapCode42_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldGetMapCode42_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldGetMapCode42_next);
            };
            unsigned int GUILD_BATTLE__CNormalGuildBattleFieldGetMapID44_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldGetMapID44_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldGetMapID44_next);
            };
            char* GUILD_BATTLE__CNormalGuildBattleFieldGetMapStrCode46_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldGetMapStrCode46_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldGetMapStrCode46_next);
            };
            void GUILD_BATTLE__CNormalGuildBattleFieldGetPortalIndexInfo48_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this, int* iRedPortalInx, int* iBluePortalInx, int* piRegenPortalInx)
            {
               GUILD_BATTLE__CNormalGuildBattleFieldGetPortalIndexInfo48_user(_this, iRedPortalInx, iBluePortalInx, piRegenPortalInx, GUILD_BATTLE__CNormalGuildBattleFieldGetPortalIndexInfo48_next);
            };
            struct CGravityStoneRegener* GUILD_BATTLE__CNormalGuildBattleFieldGetRegener50_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this, int iInx)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldGetRegener50_user(_this, iInx, GUILD_BATTLE__CNormalGuildBattleFieldGetRegener50_next);
            };
            struct CGravityStone* GUILD_BATTLE__CNormalGuildBattleFieldGetStone52_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldGetStone52_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldGetStone52_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleFieldInit54_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this, unsigned int uiMapInx)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldInit54_user(_this, uiMapInx, GUILD_BATTLE__CNormalGuildBattleFieldInit54_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleFieldIsGoal56_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this, struct CPlayer* pkPlayer, int iPortalInx)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldIsGoal56_user(_this, pkPlayer, iPortalInx, GUILD_BATTLE__CNormalGuildBattleFieldIsGoal56_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleFieldLoadDummys58_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this, char* szSectionName, char* szKeyName, char* szItemName, unsigned int* uiCnt, struct _dummy_position*** ppDummy)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldLoadDummys58_user(_this, szSectionName, szKeyName, szItemName, uiCnt, ppDummy, GUILD_BATTLE__CNormalGuildBattleFieldLoadDummys58_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleFieldMoveStartPos60_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this, char byStartPos, char byMapOutType, struct CPlayer* pkPlayer)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldMoveStartPos60_user(_this, byStartPos, byMapOutType, pkPlayer, GUILD_BATTLE__CNormalGuildBattleFieldMoveStartPos60_next);
            };
            int GUILD_BATTLE__CNormalGuildBattleFieldRegenBall62_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldRegenBall62_user(_this, GUILD_BATTLE__CNormalGuildBattleFieldRegenBall62_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleFieldSetPortalInx64_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this, struct _dummy_position** ppkDummy, int** iPortalInx, unsigned int uiCnt)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldSetPortalInx64_user(_this, ppkDummy, iPortalInx, uiCnt, GUILD_BATTLE__CNormalGuildBattleFieldSetPortalInx64_next);
            };
            bool GUILD_BATTLE__CNormalGuildBattleFieldStart66_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this, char byStartPos, struct CPlayer* pkPlayer)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldStart66_user(_this, byStartPos, pkPlayer, GUILD_BATTLE__CNormalGuildBattleFieldStart66_next);
            };
            char GUILD_BATTLE__CNormalGuildBattleFieldTakeBall68_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this, int iPortalInx, struct CPlayer* pkPlayer)
            {
               return GUILD_BATTLE__CNormalGuildBattleFieldTakeBall68_user(_this, iPortalInx, pkPlayer, GUILD_BATTLE__CNormalGuildBattleFieldTakeBall68_next);
            };
            
            void GUILD_BATTLE__CNormalGuildBattleFielddtor_CNormalGuildBattleField72_wrapper(struct GUILD_BATTLE::CNormalGuildBattleField* _this)
            {
               GUILD_BATTLE__CNormalGuildBattleFielddtor_CNormalGuildBattleField72_user(_this, GUILD_BATTLE__CNormalGuildBattleFielddtor_CNormalGuildBattleField72_next);
            };
            
            ::std::array<hook_record, 35> CNormalGuildBattleField_functions = 
            {
                _hook_record {
                    (LPVOID)0x1403eb7b0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldctor_CNormalGuildBattleField2_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldctor_CNormalGuildBattleField2_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldctor_CNormalGuildBattleField2_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleField::*)())&GUILD_BATTLE::CNormalGuildBattleField::ctor_CNormalGuildBattleField)
                },
                _hook_record {
                    (LPVOID)0x1403ed6c0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCheatDestroyStone4_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCheatDestroyStone4_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldCheatDestroyStone4_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleField::*)())&GUILD_BATTLE::CNormalGuildBattleField::CheatDestroyStone)
                },
                _hook_record {
                    (LPVOID)0x1403ed7c0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCheatDropStone6_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCheatDropStone6_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldCheatDropStone6_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattleField::*)(struct CPlayer*))&GUILD_BATTLE::CNormalGuildBattleField::CheatDropStone)
                },
                _hook_record {
                    (LPVOID)0x1403ed8c0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCheatForceTakeStone8_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCheatForceTakeStone8_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldCheatForceTakeStone8_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleField::*)(struct CPlayer*))&GUILD_BATTLE::CNormalGuildBattleField::CheatForceTakeStone)
                },
                _hook_record {
                    (LPVOID)0x1403ed760L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCheatGetStone10_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCheatGetStone10_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldCheatGetStone10_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattleField::*)(struct CPlayer*))&GUILD_BATTLE::CNormalGuildBattleField::CheatGetStone)
                },
                _hook_record {
                    (LPVOID)0x1403ed550L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone12_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone12_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone12_wrapper),
                    (LPVOID)cast_pointer_function((int(GUILD_BATTLE::CNormalGuildBattleField::*)(struct CPlayer*))&GUILD_BATTLE::CNormalGuildBattleField::CheatRegenStone)
                },
                _hook_record {
                    (LPVOID)0x1403ed490L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone14_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone14_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldCheatRegenStone14_wrapper),
                    (LPVOID)cast_pointer_function((int(GUILD_BATTLE::CNormalGuildBattleField::*)(unsigned int))&GUILD_BATTLE::CNormalGuildBattleField::CheatRegenStone)
                },
                _hook_record {
                    (LPVOID)0x1403ed710L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCheatTakeStone16_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCheatTakeStone16_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldCheatTakeStone16_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattleField::*)(int, struct CPlayer*))&GUILD_BATTLE::CNormalGuildBattleField::CheatTakeStone)
                },
                _hook_record {
                    (LPVOID)0x1403ed0f0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCheckBallTakeLimitTime18_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCheckBallTakeLimitTime18_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldCheckBallTakeLimitTime18_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleField::*)())&GUILD_BATTLE::CNormalGuildBattleField::CheckBallTakeLimitTime)
                },
                _hook_record {
                    (LPVOID)0x1403ed070L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCheckIsInTown20_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCheckIsInTown20_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldCheckIsInTown20_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleField::*)())&GUILD_BATTLE::CNormalGuildBattleField::CheckIsInTown)
                },
                _hook_record {
                    (LPVOID)0x1403ed140L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldClearBall22_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldClearBall22_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldClearBall22_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleField::*)())&GUILD_BATTLE::CNormalGuildBattleField::ClearBall)
                },
                _hook_record {
                    (LPVOID)0x1403ed190L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldClearRegen24_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldClearRegen24_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldClearRegen24_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleField::*)())&GUILD_BATTLE::CNormalGuildBattleField::ClearRegen)
                },
                _hook_record {
                    (LPVOID)0x1403ec630L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCreateFieldObject26_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldCreateFieldObject26_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldCreateFieldObject26_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleField::*)())&GUILD_BATTLE::CNormalGuildBattleField::CreateFieldObject)
                },
                _hook_record {
                    (LPVOID)0x1403eded0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldDestroy28_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldDestroy28_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldDestroy28_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleField::*)())&GUILD_BATTLE::CNormalGuildBattleField::Destroy)
                },
                _hook_record {
                    (LPVOID)0x1403ec810L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldDestroyFieldObject30_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldDestroyFieldObject30_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldDestroyFieldObject30_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleField::*)())&GUILD_BATTLE::CNormalGuildBattleField::DestroyFieldObject)
                },
                _hook_record {
                    (LPVOID)0x1403ece10L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldDropBall32_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldDropBall32_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldDropBall32_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattleField::*)(struct CPlayer*))&GUILD_BATTLE::CNormalGuildBattleField::DropBall)
                },
                _hook_record {
                    (LPVOID)0x1403ecd90L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetBall34_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetBall34_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldGetBall34_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattleField::*)(uint16_t, unsigned int, struct CPlayer*))&GUILD_BATTLE::CNormalGuildBattleField::GetBall)
                },
                _hook_record {
                    (LPVOID)0x1403ed210L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetBallOwner36_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetBallOwner36_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldGetBallOwner36_wrapper),
                    (LPVOID)cast_pointer_function((struct CPlayer*(GUILD_BATTLE::CNormalGuildBattleField::*)())&GUILD_BATTLE::CNormalGuildBattleField::GetBallOwner)
                },
                _hook_record {
                    (LPVOID)0x1403eca10L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetCircleZone38_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetCircleZone38_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldGetCircleZone38_wrapper),
                    (LPVOID)cast_pointer_function((struct CCircleZone*(GUILD_BATTLE::CNormalGuildBattleField::*)(int))&GUILD_BATTLE::CNormalGuildBattleField::GetCircleZone)
                },
                _hook_record {
                    (LPVOID)0x1400a6a60L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetMap40_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetMap40_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldGetMap40_wrapper),
                    (LPVOID)cast_pointer_function((struct CMapData*(GUILD_BATTLE::CNormalGuildBattleField::*)())&GUILD_BATTLE::CNormalGuildBattleField::GetMap)
                },
                _hook_record {
                    (LPVOID)0x1403ec950L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetMapCode42_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetMapCode42_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldGetMapCode42_wrapper),
                    (LPVOID)cast_pointer_function((int(GUILD_BATTLE::CNormalGuildBattleField::*)())&GUILD_BATTLE::CNormalGuildBattleField::GetMapCode)
                },
                _hook_record {
                    (LPVOID)0x1403eb0f0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetMapID44_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetMapID44_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldGetMapID44_wrapper),
                    (LPVOID)cast_pointer_function((unsigned int(GUILD_BATTLE::CNormalGuildBattleField::*)())&GUILD_BATTLE::CNormalGuildBattleField::GetMapID)
                },
                _hook_record {
                    (LPVOID)0x1403ec910L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetMapStrCode46_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetMapStrCode46_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldGetMapStrCode46_wrapper),
                    (LPVOID)cast_pointer_function((char*(GUILD_BATTLE::CNormalGuildBattleField::*)())&GUILD_BATTLE::CNormalGuildBattleField::GetMapStrCode)
                },
                _hook_record {
                    (LPVOID)0x1403ecaf0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetPortalIndexInfo48_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetPortalIndexInfo48_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldGetPortalIndexInfo48_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleField::*)(int*, int*, int*))&GUILD_BATTLE::CNormalGuildBattleField::GetPortalIndexInfo)
                },
                _hook_record {
                    (LPVOID)0x1403ec980L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetRegener50_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetRegener50_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldGetRegener50_wrapper),
                    (LPVOID)cast_pointer_function((struct CGravityStoneRegener*(GUILD_BATTLE::CNormalGuildBattleField::*)(int))&GUILD_BATTLE::CNormalGuildBattleField::GetRegener)
                },
                _hook_record {
                    (LPVOID)0x1403f0360L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetStone52_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldGetStone52_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldGetStone52_wrapper),
                    (LPVOID)cast_pointer_function((struct CGravityStone*(GUILD_BATTLE::CNormalGuildBattleField::*)())&GUILD_BATTLE::CNormalGuildBattleField::GetStone)
                },
                _hook_record {
                    (LPVOID)0x1403eb870L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldInit54_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldInit54_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldInit54_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleField::*)(unsigned int))&GUILD_BATTLE::CNormalGuildBattleField::Init)
                },
                _hook_record {
                    (LPVOID)0x1403ece70L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldIsGoal56_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldIsGoal56_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldIsGoal56_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattleField::*)(struct CPlayer*, int))&GUILD_BATTLE::CNormalGuildBattleField::IsGoal)
                },
                _hook_record {
                    (LPVOID)0x1403ed930L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldLoadDummys58_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldLoadDummys58_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldLoadDummys58_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleField::*)(char*, char*, char*, unsigned int*, struct _dummy_position***))&GUILD_BATTLE::CNormalGuildBattleField::LoadDummys)
                },
                _hook_record {
                    (LPVOID)0x1403ed280L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldMoveStartPos60_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldMoveStartPos60_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldMoveStartPos60_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleField::*)(char, char, struct CPlayer*))&GUILD_BATTLE::CNormalGuildBattleField::MoveStartPos)
                },
                _hook_record {
                    (LPVOID)0x1403ecbe0L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldRegenBall62_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldRegenBall62_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldRegenBall62_wrapper),
                    (LPVOID)cast_pointer_function((int(GUILD_BATTLE::CNormalGuildBattleField::*)())&GUILD_BATTLE::CNormalGuildBattleField::RegenBall)
                },
                _hook_record {
                    (LPVOID)0x1403edd60L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldSetPortalInx64_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldSetPortalInx64_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldSetPortalInx64_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleField::*)(struct _dummy_position**, int**, unsigned int))&GUILD_BATTLE::CNormalGuildBattleField::SetPortalInx)
                },
                _hook_record {
                    (LPVOID)0x1403ed410L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldStart66_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldStart66_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldStart66_wrapper),
                    (LPVOID)cast_pointer_function((bool(GUILD_BATTLE::CNormalGuildBattleField::*)(char, struct CPlayer*))&GUILD_BATTLE::CNormalGuildBattleField::Start)
                },
                _hook_record {
                    (LPVOID)0x1403ecc60L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldTakeBall68_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFieldTakeBall68_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFieldTakeBall68_wrapper),
                    (LPVOID)cast_pointer_function((char(GUILD_BATTLE::CNormalGuildBattleField::*)(int, struct CPlayer*))&GUILD_BATTLE::CNormalGuildBattleField::TakeBall)
                },
                _hook_record {
                    (LPVOID)0x1403eb830L,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFielddtor_CNormalGuildBattleField72_user,
                    (LPVOID *)&GUILD_BATTLE__CNormalGuildBattleFielddtor_CNormalGuildBattleField72_next,
                    (LPVOID)cast_pointer_function(GUILD_BATTLE__CNormalGuildBattleFielddtor_CNormalGuildBattleField72_wrapper),
                    (LPVOID)cast_pointer_function((void(GUILD_BATTLE::CNormalGuildBattleField::*)())&GUILD_BATTLE::CNormalGuildBattleField::dtor_CNormalGuildBattleField)
                },
            };
        }; // end namespace Detail
    }; // end namespace GUILD_BATTLE
END_ATF_NAMESPACE
