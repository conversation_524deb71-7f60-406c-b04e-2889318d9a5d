#include <_throw_unit_result_other_zocl.hpp>


START_ATF_NAMESPACE
    _throw_unit_result_other_zocl::_throw_unit_result_other_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _throw_unit_result_other_zocl*);
        (org_ptr(0x1400efdd0L))(this);
    };
    void _throw_unit_result_other_zocl::ctor__throw_unit_result_other_zocl()
    {
        using org_ptr = void (WINAPIV*)(struct _throw_unit_result_other_zocl*);
        (org_ptr(0x1400efdd0L))(this);
    };
END_ATF_NAMESPACE
