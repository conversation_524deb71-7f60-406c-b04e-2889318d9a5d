// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    struct _use_potion_result_zocl
    {
        char byErrCode;
         unsigned __int16 wPotionSerial;
         unsigned __int16 wHP;
         unsigned __int16 wFP;
         unsigned __int16 wSP;
        char byLeftNum;
    };
END_ATF_NAMESPACE
