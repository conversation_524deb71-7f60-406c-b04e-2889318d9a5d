// This file auto generated by plugin for ida pro. Generated code only for x64. Please, dont change manually
#pragma once

#include <common/common.h>


START_ATF_NAMESPACE
    #pragma pack(push, 1)
    struct  _unmannedtrader_Sell_Wait_item_inform_zocl
    {
        struct  __list
        {
            unsigned __int16 wItemSerial;
            unsigned int dwSellDalant;
            unsigned int dwTax;
        };
        char by<PERSON>um;
        unsigned int dwTotalSellDalant;
        unsigned int dwTotalTaxDalant;
        unsigned int dwCurInvenDalant;
        __list List[10];
    public:
        _unmannedtrader_Sell_Wait_item_inform_zocl();
        void ctor__unmannedtrader_Sell_Wait_item_inform_zocl();
        int size();
    };
    #pragma pack(pop)    
    static_assert(ATF::checkSize<_unmannedtrader_Sell_Wait_item_inform_zocl, 113>(), "_unmannedtrader_Sell_Wait_item_inform_zocl");
END_ATF_NAMESPACE
